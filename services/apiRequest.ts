import { AxiosRequestConfig } from 'axios';
import { ErrorCode } from 'configs';
import { FetchDataResponse } from 'types/model';
import axiosConfig from 'utils/axios.config';

export const apiRequest = async (
  url: string,
  options: AxiosRequestConfig & { cache?: boolean } = {}
): Promise<FetchDataResponse> => {
  try {
    const response = await axiosConfig(url, options);
    return {
      success: true,
      message: '',
      data: response.data || response,
    };
  } catch (err: any) {
    const data = err?.data || {};
    console.log('apiRequestError', err.message);
    return {
      success: false,
      message: ErrorCode[data?.errorCode] || data?.message || err.message,
      ...err?.response,
      data: {
        ...data,
      },
    };
  }
};
