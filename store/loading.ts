import { LoadingStoreProps } from 'types/stores';
import { create } from 'zustand';

const useLoadingStore = create<LoadingStoreProps>()((set) => ({
  loading: true,
  countRequest: 0,
  increaseLoading: () => {
    set(({ countRequest }) => ({ countRequest: countRequest + 1, loading: true }));
  },
  decreaseLoading: (initLoading) => {
    set(({ countRequest }) => ({
      countRequest: countRequest - 1,
      loading: countRequest - 1 > 0 && !initLoading,
    }));
  },
}));

export default useLoadingStore;
