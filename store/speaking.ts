import { create } from 'zustand';
import { SpeakingStoreProps } from 'types/stores';
import { SentenceProcessEnum } from 'configs/SentenceProcessEnum';
import {RecordProcessEnum} from '@/configs/RecordProcessEnum';

const useSpeakingStore = create<SpeakingStoreProps>()((set) => ({
  sentenceProcess: SentenceProcessEnum.START,
  setSentenceProcess: (sentenceProcess) => {
    set(() => ({ sentenceProcess: sentenceProcess }));
  },
  recordProcess: RecordProcessEnum.INIT,
  setRecordProcess: (recordProcess) => {
    set(() => ({ recordProcess: recordProcess }));
  },
  timestamp: 0,
  setTimestamp: (timestamp: number) => {
    set(() => ({ timestamp: timestamp }));
  },
  sentence: null,
  setSentence: (sentence) => {
    set(() => ({ sentence }));
  },
  volume: 80,
  setVolume: (volume) => {
    set(() => ({ volume }));
  },
  autoReading: false,
  setAutoReading: (autoReading) => {
    set(() => ({ autoReading }));
  },
  replay: false,
  setReplay: (replay) => {
    set(() => ({ replay }));
  },
  sentenceScore: null,
  setSentenceScore: (sentenceScore) => {
    set(() => ({ sentenceScore }));
  },
}));

export default useSpeakingStore;
