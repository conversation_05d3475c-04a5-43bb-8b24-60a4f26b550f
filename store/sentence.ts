import { Character, DocumentEntity, ParagraphEntity, SentenceEntity } from 'types/model';
import { SentenceStoreProps } from 'types/stores';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

const useSentenceStore = create<SentenceStoreProps>()(
  persist(
    (set) => ({
      currentCourseId: null,
      currentDocumentId: null,
      currentParagraphId: null,
      currentSentenceId: null,
      conversations: [],
      speakings: [],
      conversationsByParagraph: [],
      timeToLearnOneConversation: 0,
      activeDocument: null,
      activeParagraph: null,
      showWord: false,
      activeCharacter: null,
      selectedSentence: null,
      setCharacter: (activeCharacter: Character) => {
        set(() => ({ activeCharacter: activeCharacter }));
      },
      setShowWord: (showWord: boolean) => {
        set(() => ({ showWord: showWord }));
      },
      setCurrentDocumentId: (documentId) => {
        set(() => ({ currentDocumentId: documentId }));
      },
      setActiveDocument: (activeDocument: DocumentEntity) => {
        set(() => ({ activeDocument: activeDocument }));
      },
      setActiveParagraph: (activeParagraph: ParagraphEntity | null) => {
        set(() => ({ activeParagraph: activeParagraph }));
      },
      setCourseId: (courseId) => {
        set(() => ({ currentCourseId: courseId }));
      },
      setCurrentParagraphId: (paragraphId) => {
        set(() => ({ currentParagraphId: paragraphId }));
      },
      setCurrentSentenceId: (sentenceId) => {
        set(() => ({ currentSentenceId: sentenceId }));
      },
      setTimeToLearnOne: (time) => {
        set(() => ({ timeToLearnOneConversation: time }));
      },
      setConversations: (conversations: Array<Array<SentenceEntity>>) => {
        set(() => ({ conversations }));
      },
      setSpeakings: (speakings: Array<Array<SentenceEntity>>) => {
        set(() => ({ speakings }));
      },
      setSelectedSentence: (selectedSentence: SentenceEntity) => {
        set(() => ({ selectedSentence: selectedSentence }));
      },
      setConversationsByParagraph: (conversationsByParagraph: Array<Array<SentenceEntity>>) => {
        set(() => ({ conversationsByParagraph }));
      },
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) =>
        Object.fromEntries(
          Object.entries(state).filter(([key]) => ['conversations', 'showWord'].includes(key))
        ),
    }
  )
);

export default useSentenceStore;
