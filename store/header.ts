import { HeaderStoreProps } from 'types/stores';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

const useHeaderStore = create<HeaderStoreProps>()(
  persist(
    (set) => ({
      title: '',
      setTitle: (title) => {
        set(() => ({ title }));
      },
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);

export default useHeaderStore;
