import { KnowledgeEntity } from 'types/model';
import { KnowledgeStoreProps } from 'types/stores';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

const useKnowledgeStore = create<KnowledgeStoreProps>()(
  persist(
    (set) => ({
      knowledge: null,
      setKnowledge: (knowledge: KnowledgeEntity) => {
        set(() => ({ knowledge: knowledge }));
      },
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) =>
        Object.fromEntries(
          Object.entries(state).filter(([key]) => ['conversations', 'showWord'].includes(key))
        ),
    }
  )
);

export default useKnowledgeStore;
