import { CategoryStoreProps } from 'types/stores';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

const useCategoryStore = create<CategoryStoreProps>()(
  persist(
    (set) => ({
      categories: [],
      setCategories: (categories) => {
        set(() => ({ categories }));
      },
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) =>
        Object.fromEntries(Object.entries(state).filter(([key]) => !['item'].includes(key))),
    }
  )
);

export default useCategoryStore;
