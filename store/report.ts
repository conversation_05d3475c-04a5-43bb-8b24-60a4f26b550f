import { ReportStoreProps } from '@/types/stores';
import { endOfMonth, getUnixTime } from 'date-fns';
import { create } from 'zustand';

const useReportStore = create<ReportStoreProps>()((set) => ({
  loading: false,
  params: {
    start_day: Math.floor(
      Date.UTC(new Date().getUTCFullYear(), new Date().getUTCMonth(), 1) / 1000
    ),
    end_day: Math.floor(getUnixTime(endOfMonth(new Date()))),
    item: '',
    object_id: 0,
  },
  date: new Date(),
  setDate: (date: Date) => {
    set(() => ({ date }));
  },
  setParams: (params) => {
    set(() => ({ params }));
  },
  setLoading: (loading) => {
    set(() => ({ loading }));
  },
}));

export default useReportStore;
