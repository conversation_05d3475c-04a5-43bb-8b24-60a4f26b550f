import { LayoutStoreProps } from 'types/stores';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

const useLayoutStore = create<LayoutStoreProps>()(
  persist(
    (set) => ({
      enableScroll: true,
      setEnableScroll: (enableScroll) => {
        set(() => ({ enableScroll }));
      },
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => localStorage),
      partialize: (state) =>
        Object.fromEntries(Object.entries(state).filter(([key]) => ['themesList'].includes(key))),
    }
  )
);

export default useLayoutStore;
