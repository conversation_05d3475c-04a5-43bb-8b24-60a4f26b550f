'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

import { usePathname } from 'next/navigation';

import CategoryEnum from '@/configs/CategoryEnum';
import useCategoryStore from '@/store/category';
import { CategoryEntity } from '@/types/model';
import { AppContextProps } from '@/types/theme';
import { isEmpty, map } from 'lodash';
import { parseColor } from 'tailwindcss/lib/util/color';

const AppContext = createContext(null);

export const useAppContext = () => {
  const context: AppContextProps | null = useContext(AppContext);

  if (!context) {
    throw new Error('useAppContext must be used within a AppProvider');
  }
  return context;
};

export const AppProvider = ({ children }: { children: React.ReactNode }) => {
  const [isOpenPanel, setOpenPanel] = useState(false);
  const [panel, setPanel] = useState(null);
  const { categories } = useCategoryStore();
  const toRGB = (value: string) => parseColor(value).color.join(' ');

  useEffect(() => {
    const themesConfigList: CategoryEntity[] =
      categories?.filter((item: CategoryEntity) => item.parent_id === CategoryEnum.THEME_LIST) ||
      [];
    map(themesConfigList, (category: CategoryEntity) => {
      const _themeData =
        themesConfigList?.filter((item: CategoryEntity) => item.parent_id === category.id) || [];
      if (_themeData.length > 0) {
        category.items = _themeData;
      }
    });

    if (!isEmpty(themesConfigList)) {
      // Create a new style element
      const styleElement = document.createElement('style');
      document.head.appendChild(styleElement);

      // Add rules to the new style element
      map(themesConfigList, (theme) => {
        map(theme?.items, (themeData) => {
          if (themeData.valuex && themeData.keyx) {
            styleElement.sheet?.insertRule(
              `[data-theme='${theme.keyx}']{${themeData.keyx}: ${toRGB(
                themeData.valuex.toString()
              )}}`,
              0
            );
          }
        });
      });

      // Cleanup function to remove the style element when component unmounts
      return () => {
        document.head.removeChild(styleElement);
      };
    }
  }, [categories]);

  const pathname = usePathname();

  useEffect(() => {
    // @ts-ignore
    setPanel(null);
    // @ts-ignore
    setOpenPanel(false);
  }, [pathname]);
  return (
    <AppContext.Provider
      // @ts-ignore
      value={{
        panel,
        setPanel,
        isOpenPanel,
        setOpenPanel,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};
