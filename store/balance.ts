import { BalanceStoreProps } from 'types/stores';
import { create } from 'zustand';

const useBalanceStore = create<BalanceStoreProps>()((set) => ({
  balance: 0,
  balanceString: '',
  balanceStatus: 1,
  setBalance: (balance) => {
    set(() => ({ balance }));
  },
  setBalanceStatus: (balanceStatus) => {
    set(() => ({ balanceStatus }));
  },
  setBalanceString: (balanceString) => {
    set(() => ({ balanceString }));
  },
}));

export default useBalanceStore;
