import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

interface FavouriteItem {
  item: string;
  object_id: number;
}

interface FavouriteStore {
  favouriteList: FavouriteItem[];
  addFavourite: (item: FavouriteItem) => void;
  removeFavourite: (item: FavouriteItem) => void;
  setFavourites: (items: FavouriteItem[]) => void;
}

const useFavouriteStore = create<FavouriteStore>()(
  persist(
    (set) => ({
      favouriteList: [],
      addFavourite: (item) =>
        set((state) => ({
          favouriteList: [...state.favouriteList, item],
        })),
      removeFavourite: (item) =>
        set((state) => ({
          favouriteList: state.favouriteList.filter(
            (fav) => fav.object_id !== item.object_id || fav.item !== item.item
          ),
        })),
      setFavourites: (items) => set({ favouriteList: items }),
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) =>
        Object.fromEntries(Object.entries(state).filter(([key]) => !['item'].includes(key))),
    }
  )
);

export default useFavouriteStore;
