import { OnboardingStoreProps } from 'types/stores';
import { create } from 'zustand';

const useOnboardingStore = create<OnboardingStoreProps>()((set) => ({
  questionId: 0,
  setQuestionId: (questionId) => {
    set(() => ({ questionId }));
  },
  questionContent: '',
  setQuestionContent: (questionContent) => {
    set(() => ({ questionContent }));
  },
  questionType: 0,
  setQuestionType: (questionType) => {
    set(() => ({ questionType }));
  },
  answerReqs: {},
  setAnswerReqs: (updateFn) => {
    set((state) => ({
      answerReqs: updateFn(state.answerReqs),
    }));
  },
  questionStep: 0,
  setQuestionStep: (questionStep) => {
    set(() => ({ questionStep }));
  },
}));

export default useOnboardingStore;
