import {
  DocumentEntity,
  ParagraphEntity,
  PronounceEntity,
  SentenceEntity,
  SentencePosEntity,
} from 'types/model';
import { PronounceStoreProps } from 'types/stores';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

const usePronounceStore = create<PronounceStoreProps>()(
  persist(
    (set) => ({
      activePronounceId: 0,
      currentCourseId: null,
      currentDocumentId: null,
      currentParagraphId: null,
      currentSentenceId: null,
      conversations: [],
      speakings: [],
      conversationsByParagraph: [],
      timeToLearnOneConversation: 0,
      activeDocument: null,
      activeParagraph: null,
      activeParagraphId: 0,
      showWord: false,
      selectedSentence: null,
      activeSentencePos: null,
      activePronounce: null,
      setActivePronounceId: (pronounceId: number) => {
        set(() => ({ activePronounceId: pronounceId }));
      },
      setShowWord: (showWord: boolean) => {
        set(() => ({ showWord: showWord }));
      },
      setCurrentDocumentId: (documentId) => {
        set(() => ({ currentDocumentId: documentId }));
      },
      setActiveDocument: (activeDocument: DocumentEntity) => {
        set(() => ({ activeDocument: activeDocument }));
      },
      setActiveParagraph: (activeParagraph: ParagraphEntity | null) => {
        set(() => ({ activeParagraph: activeParagraph }));
      },
      setCourseId: (courseId) => {
        set(() => ({ currentCourseId: courseId }));
      },
      setCurrentParagraphId: (paragraphId) => {
        set(() => ({ currentParagraphId: paragraphId }));
      },
      setCurrentSentenceId: (sentenceId) => {
        set(() => ({ currentSentenceId: sentenceId }));
      },
      setTimeToLearnOne: (time) => {
        set(() => ({ timeToLearnOneConversation: time }));
      },
      setConversations: (conversations: Array<Array<SentenceEntity>>) => {
        set(() => ({ conversations }));
      },
      setSpeakings: (speakings: Array<Array<SentenceEntity>>) => {
        set(() => ({ speakings }));
      },
      setSelectedSentence: (selectedSentence: SentenceEntity) => {
        set(() => ({ selectedSentence: selectedSentence }));
      },
      setConversationsByParagraph: (conversationsByParagraph: Array<Array<SentenceEntity>>) => {
        set(() => ({ conversationsByParagraph }));
      },
      setSelectedSentencePos: (activeSentencePos: SentencePosEntity) => {
        set(() => ({ activeSentencePos: activeSentencePos }));
      },
      setActivePronounce: (activePronounce: PronounceEntity) => {
        set(() => ({ activePronounce: activePronounce }));
      },
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) =>
        Object.fromEntries(
          Object.entries(state).filter(([key]) => ['conversations', 'showWord'].includes(key))
        ),
    }
  )
);

export default usePronounceStore;
