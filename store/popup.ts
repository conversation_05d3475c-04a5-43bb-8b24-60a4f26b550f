import { ErrorModalProps, IPopupStore } from 'types/popup';
import { create } from 'zustand';

const usePopupStore = create<IPopupStore>()((set) => ({
  openedExpiredSession: false,
  openedErrorModal: false,
  errorMessage: '',
  title: '',

  setExpiredSessionModal: (openedExpiredSession: boolean) => {
    set(() => ({ openedExpiredSession }));
  },
  setErrorModal: ({ opened, message = '', title = '' }: ErrorModalProps) => {
    set(() => ({ openedErrorModal: opened, errorMessage: message, title: title }));
  },
}));

export default usePopupStore;
