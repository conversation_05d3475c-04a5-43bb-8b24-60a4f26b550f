import LearnTypeEnum from '@/configs/LearnTypeEnum';
import {
  Character,
  CourseEntity,
  DocumentEntity,
  ParagraphEntity,
  SentenceEntity,
  TransactionLearnInfo,
} from 'types/model';
import { LearnStoreProps } from 'types/stores';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

export const APP_STORE_NAME = 'entStore';

const useLearnStore = create<LearnStoreProps>()(
  persist(
    (set) => ({
      activeTab: LearnTypeEnum.LISTEN,
      isFinishLearn: false,
      currentCourseId: 0,
      currentSentenceId: 0,
      conversations: [],
      speakings: [],
      conversationsByParagraph: [],
      documents: [],
      course: null,
      activeDocument: null,
      activeParagraph: undefined,
      showWord: true,
      activeCharacter: null,
      selectedSentence: null,
      paragraphs: [],
      transactionInfo: null,
      sentenceGroupsId: 0,
      exerciseToken: '',
      setTransactionInfo: (transactionInfo: TransactionLearnInfo) => {
        set(() => ({ transactionInfo: transactionInfo }));
      },
      setCharacter: (activeCharacter: Character) => {
        set(() => ({ activeCharacter: activeCharacter }));
      },
      setShowWord: (showWord: boolean) => {
        set(() => ({ showWord: showWord }));
      },
      setCourse: (course: CourseEntity) => {
        set(() => ({ course: course }));
      },

      setExerciseToken: (exerciseToken) => {
        set(() => ({ exerciseToken: exerciseToken }));
      },
      setDocuments: (documents) => {
        set(() => ({ documents: documents }));
      },
      setActiveDocument: (activeDocument: DocumentEntity) => {
        set(() => ({ activeDocument: activeDocument }));
      },
      setActiveParagraph: (activeParagraph: ParagraphEntity | undefined) => {
        set(() => ({ activeParagraph: activeParagraph }));
      },
      setCourseId: (courseId) => {
        set(() => ({ currentCourseId: courseId }));
      },
      setCurrentSentenceId: (currentSentenceId) => {
        set(() => ({ currentSentenceId: currentSentenceId }));
      },
      setConversations: (conversations: Array<Array<SentenceEntity>>) => {
        set(() => ({ conversations }));
      },
      setSpeakings: (speakings: Array<Array<SentenceEntity>>) => {
        set(() => ({ speakings }));
      },
      setSelectedSentence: (selectedSentence: SentenceEntity) => {
        set(() => ({ selectedSentence: selectedSentence }));
      },
      setConversationsByParagraph: (conversationsByParagraph: Array<Array<SentenceEntity>>) => {
        set(() => ({ conversationsByParagraph }));
      },
      setParagraphs: (paragraphs: Array<ParagraphEntity>) => {
        set(() => ({ paragraphs }));
      },
  
      setFinishLearn: (isFinishLearn: boolean) => {
        set(() => ({ isFinishLearn }));
      },
      setActiveTab: (activeTab: LearnTypeEnum) => {
        set(() => ({ activeTab }));
      },
      setSentenceGroupsId: (sentenceGroupsId: number) => {
        set(() => ({ sentenceGroupsId }));
      },
    }),
    {
      name: APP_STORE_NAME,
      storage: createJSONStorage(() => sessionStorage),
      partialize: (state) =>
        Object.fromEntries(Object.entries(state).filter(([key]) => ['showWord'].includes(key))),
    }
  )
);

export default useLearnStore;
