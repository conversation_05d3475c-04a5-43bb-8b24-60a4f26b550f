import { SearchStoreProps } from 'types/stores';
import { create } from 'zustand';

const useSearchStore = create<SearchStoreProps>()((set) => ({
  keyword: '',
  keywordDebounce: '',
  type: 'all',
  pageSearch: 1,
  setKeyword: (keyword) => {
    set(() => ({ keyword }));
  },
  setKeywordDebounce: (keywordDebounce) => {
    set(() => ({ keywordDebounce }));
  },
  setType: (type) => {
    set(() => ({ type }));
  },
  setPageSearch: (pageSearch) => {
    set(() => ({ pageSearch }));
  },
}));

export default useSearchStore;
