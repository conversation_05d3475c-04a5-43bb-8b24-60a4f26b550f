'use client';

import React from 'react';

import { IconProps } from 'types/component';

export default function CheckCardIcon(props: IconProps) {
  const { className = '' } = props;
  return (
    <svg
      className={className}
      width="22"
      height="21"
      viewBox="0 0 22 21"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.208 8.44C18.208 8.184 18.128 7.976 17.968 7.816L16.744 6.616C16.568 6.44 16.368 6.352 16.144 6.352C15.92 6.352 15.72 6.44 15.544 6.616L10.072 12.064L7.048 9.04C6.872 8.864 6.672 8.776 6.448 8.776C6.224 8.776 6.024 8.864 5.848 9.04L4.624 10.24C4.464 10.4 4.384 10.608 4.384 10.864C4.384 11.104 4.464 11.304 4.624 11.464L9.472 16.312C9.632 16.472 9.832 16.552 10.072 16.552C10.312 16.552 10.52 16.472 10.696 16.312L17.968 9.04C18.128 8.88 18.208 8.68 18.208 8.44ZM21.568 10.6C21.568 12.472 21.112 14.192 20.2 15.76C19.288 17.328 18.04 18.576 16.456 19.504C14.872 20.432 13.152 20.896 11.296 20.896C9.44 20.896 7.712 20.432 6.112 19.504C4.512 18.576 3.272 17.328 2.392 15.76C1.512 14.192 1.048 12.472 1 10.6C0.952 8.728 1.416 7.008 2.392 5.44C3.368 3.872 4.608 2.624 6.112 1.696C7.616 0.767999 9.344 0.303999 11.296 0.303999C13.248 0.303999 14.968 0.767999 16.456 1.696C17.944 2.624 19.192 3.872 20.2 5.44C21.208 7.008 21.664 8.728 21.568 10.6Z"
        fillOpacity="1"
      />
    </svg>
  );
}
