'use client';

import React from 'react';
import { useTheme } from 'next-themes';
import { IconProps } from 'types/component';

export default function EntLogoRectangle(props: IconProps) {
  const { theme } = useTheme();
  const { className = '' } = props;
  console.log('theme', theme);
  return (
    <div>
      <img
        alt={''}
        className={className}
        src={theme === 'dark' ? 'https://file.langenter.com/config/images/logo.256.grey.png' : 'https://file.langenter.com/config/images/logo.256.png'}
      />
    </div>

  );
}
