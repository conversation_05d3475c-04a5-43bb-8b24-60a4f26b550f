'use client';

import Image from 'next/image';

import { default as AvatarBoring } from 'boring-avatars';

const Avatar = ({
  image,
  name,
  className,
  size,
}: {
  image?: string;
  name: string;
  className?: string;
  size?: number;
}) => {
  if (image && image !== '')
    return (
      <Image src={image} alt={''} width={size || 18} height={size || 18} className={className} />
    );
  return (
    <div className={className}>
      <AvatarBoring variant={'beam'} name={name} size={size || 18} />
    </div>
  );
};
export default Avatar;
