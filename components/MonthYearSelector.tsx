'use client';

import React, { useState } from 'react';

import useReportStore from '@/store/report';
import { Popover, PopoverContent, PopoverTrigger } from '@heroui/react';
import { useTranslations } from 'next-intl';

import Button from '@/components/Button';

export default function MonthYearSelector({ setDate, date }) {
  const [isOpen, setIsOpen] = useState(false);
  const { loading } = useReportStore();
  const t = useTranslations();
  const months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

  const formatDate = () => {
    if (!date) return null;
    return `${months[date.getMonth()]}/${date.getFullYear()}`;
  };

  const changeMonth = (increment: number) => {
    if (loading) return;
    const newDate = new Date(date);
    newDate.setMonth(date.getMonth() + increment);
    setDate(newDate);
  };

  const changeYear = (increment: number) => {
    if (loading) return;
    const newDate = new Date(date);
    newDate.setFullYear(date.getFullYear() + increment);
    setDate(newDate);
  };
  const handleMonthSelect = (month: number) => {
    if (loading) return;
    const newDate = new Date(date);
    newDate.setMonth(month - 1);
    setDate(newDate);
    setIsOpen(false);
  };

  if (!date) return null;
  return (
    <div className="inline-flex items-center justify-center shadow-medium">
      <Button
        isIconOnly
        variant="flat"
        onPress={() => changeMonth(-1)}
        aria-label="Previous month"
        size={'sm'}
        className="h-[24px] w-6 min-w-6 text-color-minor shadow-none text-medium bg-bg-button border border-color-border border-r-0 rounded-r-none"
      >
        <i className="text-medium text-color-major icon-arrow-left" />
      </Button>
      <Popover placement="bottom" isOpen={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger>
          <Button
            variant="flat"
            size={'sm'}
            className="h-[24px] shadow-none bg-bg-button border-color-border border border-l-0 border-r-0 text-color-major text-medium rounded-none"
          >
            {formatDate()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="px-1 rounded-md">
          <div className="w-full shadow-none bg-bg-general">
            <div className="flex flex-col space-y-2">
              <div className="flex items-center justify-between">
                <Button
                  variant="flat"
                  size="icon"
                  onPress={() => changeYear(-1)}
                  aria-label="Previous year"
                  className="bg-transparent hover:bg-bg-box"
                >
                  <i className="text-small icon-arrow-left" />
                </Button>
                <span className="text-medium font-bold text-color-major">{date.getFullYear()}</span>
                <Button
                  variant="flat"
                  size="icon"
                  onPress={() => changeYear(1)}
                  aria-label="Next year"
                  className="bg-transparent hover:bg-bg-box"
                >
                  <i className="text-small icon-arrow-right-line" />
                </Button>
              </div>
              <div className="grid grid-cols-3 gap-2 p-1">
                {months.map((month) => (
                  <Button
                    key={month}
                    variant="flat"
                    size={'md'}
                    onPress={() => handleMonthSelect(month)}
                    className={`
                      ${
                        date.getMonth() === month - 1
                          ? 'bg-bg-general border border-color-border font-bold'
                          : 'bg-bg-box hover:bg-bg-box/50 hover:border-color-border'
                      }
                        transition-colors duration-200 px-0
                      `}
                  >
                    {t(`calendar.months.th${month}`)}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      <Button
        isIconOnly
        variant="flat"
        onPress={() => changeMonth(1)}
        aria-label="Next month"
        size={'sm'}
        className="text-color-minor w-6 min-w-6 shadow-none h-[24px] bg-bg-button border border-color-border border-l-0 text-normal rounded-l-none"
      >
        <i className="text-medium text-color-major icon-arrow-right-line" />
      </Button>
    </div>
  );
}
