import React, { useEffect, useState } from 'react';

import fetchImageAction from '@/actions/fetchImage';
import { Skeleton } from '@heroui/react';
import SmartCrop from 'smartcrop';

const CropImage = ({ src, targetWidth, targetHeight }) => {
  const [croppedImage, setCroppedImage] = useState<string | null>(null);
  const [blobUrl, setBlobUrl] = useState<string | null>(null);
  const transformImageUrl = (url: string): string => {
    const parts = url.split('/');
    const filename = parts.pop(); // lấy tên file cuối cùng
    return [...parts, '640', filename].join('/');
  };

  useEffect(() => {
    if (!src) return;

    const transformedSrc = transformImageUrl(src);

    let isMounted = true;
    fetchImageAction(transformedSrc)
      .then((res) => {
        if (!isMounted) return;
        const objectUrl = URL.createObjectURL(res);
        setBlobUrl(objectUrl);
      })
      .catch((err) => console.error('Error fetching image:', err));

    return () => {
      isMounted = false;
    };
  }, [src]);

  useEffect(() => {
    if (!blobUrl) return;

    let isMounted = true;

    const processCrop = async () => {
      try {
        const image = new Image();
        image.crossOrigin = 'anonymous';
        image.src = blobUrl;

        image.onload = () => {
          if (!isMounted) return;

          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) return;

          SmartCrop.crop(image, { width: targetWidth, height: targetHeight }).then((result) => {
            const { x, y, width, height } = result.topCrop || {
              x: 0,
              y: 0,
              width: image.width,
              height: image.height,
            };

            canvas.width = targetWidth;
            canvas.height = targetHeight;
            ctx.drawImage(image, x, y, width, height, 0, 0, targetWidth, targetHeight);

            setCroppedImage(canvas.toDataURL());
          });
        };
      } catch (error) {
        console.error('Error cropping image:', error);
      }
    };

    processCrop();

    return () => {
      isMounted = false;
      if (blobUrl) URL.revokeObjectURL(blobUrl);
    };
  }, [blobUrl, targetWidth, targetHeight]);

  return (
    <div className="crop-image-container">
      {croppedImage ? (
        <img
          src={croppedImage}
          alt="Cropped"
          className="cropped-image w-full h-auto object-cover rounded-lg"
        />
      ) : (
        <Skeleton className="rounded-sm w-full h-full absolute inset-0" />
      )}
    </div>
  );
};

export default CropImage;
