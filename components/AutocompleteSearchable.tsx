'use client';

import React, { ReactNode, SetStateAction, useCallback, useEffect, useRef, useState } from 'react';

import { OptionItem } from '@/types/component';
import { Autocomplete, AutocompleteItem, AutocompleteProps, Chip, cn } from '@heroui/react';

import Button from '@/components/Button';

type AutocompleteSearchableProps = {
  items: OptionItem[];
  selectedKeys: Array<string | number>;
  onItemChange: (id: SetStateAction<string | number | null>, isRemove?: boolean) => void;
  primaryKey?: string;
  checkAllLabel?: ReactNode | string;
  showCheckAll?: boolean;
  onGetAll?: () => void;
} & Omit<AutocompleteProps, 'children'>;

const AutocompleteSearchable = ({
  items = [],
  selectedKeys = [],
  onItemChange,
  primaryKey = 'id',
  checkAllLabel = 'All',
  showCheckAll = false,
  onGetAll,
  ...props
}: AutocompleteSearchableProps) => {
  const [inputValue, setInputValue] = useState('');
  const [showInput, setShowInput] = useState(true);
  const componentRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Manage the selection of the items

  const handleSelectItem = (id: React.Key | null) => {
    setInputValue('');
    if (!id) return;
    if (!selectedKeys.map((key) => key.toString()).includes(id.toString())) {
      onItemChange(id.toString());
    } else {
      if (selectedKeys.includes(0) || selectedKeys.includes('0')) handleRemoveItem('0');
      handleRemoveItem(id);
    }
    if (selectedKeys.length === 0) {
      setShowInput(false);
    }
  };

  const filteredItems = inputValue
    ? items.filter((item) => item.title.toLowerCase().includes(inputValue.toLowerCase()))
    : items;
  const handleRemoveItem = (id: React.Key) => {
    onItemChange(id.toString(), true);
    if (selectedKeys.length === 0) {
      setShowInput(true);
    }
  };

  const selectedKeysRef = useRef(selectedKeys);

  const changeFilledWithin = useCallback((filledWithin = 'false') => {
    const isFilledWithin =
      filledWithin === 'true' || inputRef?.current?.getAttribute('data-filled-within') === 'true';
    const filled = isFilledWithin || selectedKeysRef.current.length;
    inputRef?.current?.parentElement?.parentElement?.parentElement?.parentElement?.setAttribute(
      'data-filled-within',
      'false'
    );
    inputRef?.current?.parentElement?.parentElement?.parentElement?.setAttribute(
      'data-filled-within',
      filled ? 'true' : 'false'
    );
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (componentRef.current && !componentRef.current.contains(event.target as Node)) {
        if (selectedKeys.length > 0) {
          setShowInput(false);
        }
      }
    };

    selectedKeysRef.current = selectedKeys;
    changeFilledWithin();

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [selectedKeys]);

  useEffect(() => {
    setShowInput(false);
    const handleMutation = (mutationsList: any) => {
      for (const mutation of mutationsList)
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-filled-within')
          changeFilledWithin(mutation?.target?.getAttribute('data-filled-within'));
    };
    if (inputRef.current) {
      const observer = new MutationObserver(handleMutation);
      observer.observe(inputRef.current, { attributes: true });
      return () => observer.disconnect();
    }
  }, []);
  const handleComponentClick = () => {
    setShowInput(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const renderStartContent = () => {
    if (selectedKeys.includes(0) || selectedKeys.includes('0')) {
      return (
        <Chip
          key={'key-0'}
          classNames={{
            base: 'bg-bg-general rounded-lg min-w-0 flex items-center justify-between border border-color-border',
            content: 'truncate',
          }}
          endContent={
            <i
              className="text-base text-color-major icon-close cursor-pointer mr-1"
              onClick={() => handleRemoveItem(0)}
            />
          }
        >
          {items.find((item) => item[primaryKey].toString() === '0')?.title}
        </Chip>
      );
    }
    return selectedKeys.map((id) => (
      <Chip
        key={id.toString()}
        classNames={{
          base: 'bg-bg-general rounded-lg min-w-0 flex items-center justify-between border border-color-border',
          content: 'truncate',
        }}
        endContent={
          <i
            className="text-base text-color-major icon-close cursor-pointer mr-1"
            onClick={() => handleRemoveItem(id)}
          />
        }
      >
        {items.find((item) => item[primaryKey].toString() === id.toString())?.title}
      </Chip>
    ));
  };
  return (
    <div
      className={'flex items-center justify-between'}
      ref={componentRef}
      onClick={handleComponentClick}
    >
      <Autocomplete
        ref={inputRef}
        className="w-full border-none !bg-transparent shadow-none !px-0"
        classNames={{
          listboxWrapper: '!rounded-sm px-0',
          listbox: '!rounded-none',
          popoverContent: 'rounded-sm px-0',
          base: 'px-0 overflow-hidden bg-none',
          endContentWrapper: 'absolute top-[0.4px] right-3',
        }}
        listboxProps={{
          classNames: {
            base: 'px-0',
          },
          itemClasses: {
            base: 'rounded-none',
          },
        }}
        inputProps={{
          classNames: {
            inputWrapper: cn(
              'w-full h-8 px-1 block border-none !bg-transparent group-data[focus="true"]:!bg-transparent shadow-none focus-within:border-none focus-within:!bg-transparent focus-within:shadow-none active:border-none active:!bg-transparent active:shadow-none hover:border-none hover:!bg-transparent hover:shadow-none focus-visible:border-none focus-visible:!bg-transparent focus-visible:shadow-none',
              {
                'min-h-8': selectedKeys.length === 0,
                'h-auto': selectedKeys.length > 0,
              }
            ),
            innerWrapper: cn(
              'flex flex-wrap gap-1 !bg-transparent min-h-8 max-w-[calc(100%-4rem)] w-full data-[has-end-content=true]:outline-none',
              {
                '-ml-1.5': selectedKeys.length === 0,
                'mt-0': selectedKeys.length > 0,
              }
            ),
            input: cn(
              'text-medium !bg-transparent px-0 data-[has-end-content=true]:ps-0 data-[has-end-content=true]:outline-none',
              {
                hidden: !showInput,
                '!ps-1.5': selectedKeys.length === 0,
                '!ps-0': selectedKeys.length > 0,
              }
            ),
          },
        }}
        startContent={renderStartContent()}
        endContent={
          <Button
            variant="light"
            isIconOnly
            size="sm"
            className={cn(
              'rounded-full !min-w-4 w-6 opacity-0 group-data-[hover=true]:opacity-100 p-0 data-[hover=true]:bg-default/40',
              {
                hidden: !selectedKeys.length,
              }
            )}
            onPress={() => {
              onItemChange(null);
              setShowInput(true);
            }}
          >
            <i className={'text-base text-color-major icon-close'} />
          </Button>
        }
        isVirtualized
        selectedKey={null}
        isClearable={false}
        onSelectionChange={(id) => handleSelectItem(id)}
        inputValue={inputValue}
        onInputChange={setInputValue}
        scrollShadowProps={{
          isEnabled: false,
          className: 'mask-image-none',
        }}
        {...props}
      >
        {filteredItems.map((item) => (
          <AutocompleteItem
            key={item[primaryKey]}
            textValue={item.title}
            endContent={
              selectedKeys.map((key) => key.toString()).includes(item[primaryKey].toString()) && (
                <i className="text-base icon-ok-circled text-primary" />
              )
            }
          >
            {item.title}
          </AutocompleteItem>
        ))}
      </Autocomplete>
      {showCheckAll ? (
        <Button
          onClick={onGetAll}
          variant={'bordered'}
          size={'sm'}
          className={'whitespace-nowrap ml-2'}
        >
          {checkAllLabel}
        </Button>
      ) : null}
    </div>
  );
};

export default AutocompleteSearchable;
