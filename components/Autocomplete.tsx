'use client';

import {
  AutocompleteItem,
  AutocompleteProps,
  Autocomplete as AutocompleteUi,
  extendVariants,
} from '@heroui/react';

const AutocompleteStyle = extendVariants(AutocompleteUi, {
  defaultVariants: {
    classNames: {
      // @ts-ignore
      wrapper: 'bg-bg-box border-none rounded-small bg-center bg-no-repeat',
    },
  },
});

const Autocomplete = (props: AutocompleteProps) => {
  // @ts-ignore
  return <AutocompleteStyle {...props}>{props.children}</AutocompleteStyle>;
};
export { Autocomplete, AutocompleteItem };
