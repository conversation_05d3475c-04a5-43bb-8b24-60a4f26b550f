'use client';

import React from 'react';

import { Button } from '@/components';
import {
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  ModalHeader,
  SlotsToClasses,
  Modal as UIModal,
} from '@heroui/react';

interface ModalProps {
  opened: boolean;
  onClose?: () => void;
  onSubmit?: () => void;
  submitLabel?: React.ReactNode | string;
  classNames?: SlotsToClasses<
    'base' | 'body' | 'footer' | 'header' | 'backdrop' | 'wrapper' | 'closeButton'
  >;
  onOpenChange: (opened: boolean) => void;
  children: React.ReactNode;
  icon?: React.ReactNode;
  header?: React.ReactNode;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | 'full';
  afterLeave?: () => void;
  scrollBehavior?: 'inside' | 'outside';
  buttonSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'icon' | undefined;
  placement?: 'top' | 'auto' | 'bottom' | 'center' | 'top-center' | 'bottom-center';
  disabledButton?: boolean;
  cancelLabel?: React.ReactNode | string;
}

const Modal: React.FC<ModalProps> = ({
  opened,
  onOpenChange,
  onClose,
  onSubmit,
  header,
  classNames,
  submitLabel,
  children,
  icon,
  scrollBehavior = 'inside',
  size = 'md',
  buttonSize = 'md',
  placement = 'top',
  disabledButton,
  cancelLabel = 'Hủy bỏ',
}) => {
  return (
    <UIModal
      isOpen={opened}
      onOpenChange={onOpenChange}
      size={size}
      backdrop="blur"
      placement={placement}
      classNames={{
        wrapper: placement === 'top' ? 'mt-16' : '',
        base: 'rounded-small bg-bg-general shadow-lg',
        closeButton: 'rounded-small !bg-transparent hover:!bg-transparent w-8 h-8 p-0',
        ...classNames,
      }}
      onClose={onClose}
      scrollBehavior={scrollBehavior}
      closeButton={
        <div>
          <div
            className={
              'w-8 h-8 cursor-pointer rounded-full hover:bg-bg-box flex items-center justify-center'
            }
          >
            <i className={'text-lg text-color-major icon-close'}></i>
          </div>
        </div>
      }
      motionProps={{
        variants: {
          enter: {
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.3,
              ease: 'easeOut',
            },
          },
          exit: {
            y: -20,
            opacity: 0,
            transition: {
              duration: 0.2,
              ease: 'easeIn',
            },
          },
        },
      }}
    >
      <ModalContent>
        {(onClose) => (
          <>
            {header && <ModalHeader className="flex flex-col gap-1 !py-2">{header}</ModalHeader>}
            <ModalBody>
              {icon && icon}
              {children}
            </ModalBody>
            <ModalFooter className={'border-t-1 border-color-border px-4 rounded-b-md py-2.5'}>
              <Button
                size={buttonSize}
                variant={'light'}
                onPress={onClose}
                isDisabled={disabledButton}
              >
                {cancelLabel}
              </Button>
              {onSubmit && (
                <Button
                  size={buttonSize}
                  color={'primary'}
                  className={'!shadow-[0px_-1px_0px_0px_#01AE4E_inset] ml-2'}
                  onPress={onSubmit}
                  isDisabled={disabledButton}
                >
                  {submitLabel ? submitLabel : ''}
                </Button>
              )}
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </UIModal>
  );
};

export default Modal;
