'use client';

import React, { useEffect, useState } from 'react';

import { useParams, usePathname } from 'next/navigation';

import { useTranslations } from 'next-intl';

const OnBoardingSideBar = () => {
  const t = useTranslations();
  const params = useParams();
  const pathname = usePathname();
  const [showWelcome, setShowWelcome] = useState(false);
  useEffect(() => {
    if (pathname.indexOf('/finish') > -1) {
      setShowWelcome(true);
    } else {
      setShowWelcome(false);
    }
  }, [params]);
  return (
    <nav
      // className='md:left-0 md:block md:fixed md:top-0 md:bottom-0 md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden bg-bg-general flex flex-wrap items-center justify-between relative md:w-[13.75rem] z-10 py-4 pl-[15px] pr-[17px]'
      className=" md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden bg-bg-general flex flex-wrap items-center justify-between relative md:w-[13.75rem] z-10 py-4 pl-[15px] pr-[17px]"
    >
      <div className="md:flex-col md:items-stretch md:min-h-full md:flex-nowrap px-0 flex flex-wrap items-center justify-between w-full mx-auto">
        {showWelcome ? (
          <div
            className={
              'h-10 p-1 rounded-3xl shadow-[0px_2px_8px_0px_rgba(0,0,0,0.10)] flex items-center content-between'
            }
          >
            <div className={'rounded-full bg-bg-box w-8 h-8'}></div>
            <div className={'w-[1px] mx-3 bg-color-line h-6'}></div>
            <div className={'text-color-minor font-[14px]'}>{t('onboard.welcome')}</div>
          </div>
        ) : null}
      </div>
    </nav>
  );
};
export default OnBoardingSideBar;
