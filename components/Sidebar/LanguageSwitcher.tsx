'use client';

import { Locale } from '@/configs/config.i18n';
import Tabs from 'components/Tabs';
import { useLocale, useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import useUpdateBalance from '@/hooks/Ent/useUpdateBalance';
import { getLocales } from '@/hooks/Ent/useUserConfig';
import saveUserLanguageHook from '@/hooks/common/saveUserLanguageHook';
import { useSession } from '@/hooks/useSession';

const LanguageSwitcher = () => {
  const t = useTranslations();
  const locale = useLocale();
  const updateBalance = useUpdateBalance();
  const { data: session } = useSession();
  const handleChangeLanguage = (newLocale) => {
    if (newLocale !== null) {
      const locale = newLocale as Locale;
      const toastId = toast.loading(t('message.toast_loading'));
      saveUserLanguageHook(locale, session.accessToken).then(() => {
        updateBalance();
        toast.dismiss(toastId);
      });
    }
  };
  const locales = getLocales(locale);
  if (!locales || locales.length === 0) return null;
  return (
    <div className={'flex mb-4 flex-col'}>
      <span>{t('sidebar.current_language')}</span>
      <div className={'inline-flex mt-1'}>
        <Tabs
          classNames={{
            tab: 'px-4',
          }}
          tabs={locales}
          value={'keyx'}
          onClick={handleChangeLanguage}
          activeTabId={locale}
        />
      </div>
    </div>
  );
};
export default LanguageSwitcher;
