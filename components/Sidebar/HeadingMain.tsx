'use client';

import React, { useEffect, useState } from 'react';

import Link from 'next/link';

import useFavouriteStore from '@/store/favourite';
import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownSection,
  DropdownTrigger,
} from '@heroui/react';
import classNames from 'classnames';
import Avatar from 'components/Avatar';
import EntRouters from 'configs/EntRouters';
import { StatusEnum } from 'configs/StatusEnum';
import { map } from 'lodash';
import { signOut } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { Member } from 'types/auth';

import { useSession } from '@/hooks/useSession';

const HeadingMain = () => {
  const { setFavourites } = useFavouriteStore();
  const t = useTranslations();
  const { data: session } = useSession();
  const [currentMember, setMember] = useState<Member>();
  useEffect(() => {
    if (session?.user?.id) {
      const memberOfAccount = session?.members?.find((member) => member.is_main === StatusEnum.ON);
      if (memberOfAccount) {
        setMember(memberOfAccount);
      } else {
        const { user } = session;
        setMember({
          id: user.id,
          account_id: user.id,
          fullname: user.phone,
          nickname: user.phone,
          created_at: user.created_at,
          status: user.status,
          role_id: user.role_id ?? 0,
        });
      }
    } else {
      if (session?.member) {
        setMember(session?.member);
      }
    }
  }, [session]);
  return (
    <Dropdown>
      <DropdownTrigger className="w-full">
        <div
          className={
            'flex align-middle items-center cursor-pointer bg-bg-box rounded-md h-[30px] flex-1 px-2 mr-2'
          }
        >
          <Avatar name={currentMember?.id?.toString() || '0'} size={18} className={'mr-2'} />
          <span className={'text-color-major text-normal'}>{currentMember?.fullname || ''}</span>
        </div>
      </DropdownTrigger>
      <DropdownMenu className={''}>
        {session?.user?.id ? (
          <DropdownSection
            title={session?.user?.phone}
            classNames={{
              heading: 'p-2 text-[rgba(0,0,0,0.39)] text-normal',
              divider: 'my-2 bg-color-line',
            }}
            showDivider
          >
            {map(session?.members, (member) => (
              <DropdownItem
                className={classNames(
                  'h-[30px] w-full px-2 py-0 mb-0.5 hover:!bg-bg-box rounded-md',
                  {
                    'bg-bg-box': member.id === currentMember?.id,
                  }
                )}
                classNames={{
                  base: '',
                  title: '',
                }}
                key={member.id}
                startContent={<Avatar name={member.id.toString()} size={18} className={'mr-2'} />}
                textValue={member.nickname || member.fullname}
              >
                <Link
                  href={`${EntRouters.member_detail}/${member.token}`}
                  className={'text-color-major leading-3 text-normal'}
                >
                  {member.nickname || member.fullname}
                </Link>
              </DropdownItem>
            ))}
          </DropdownSection>
        ) : (
          <DropdownSection title={''}>
            <></>
          </DropdownSection>
        )}
        {session?.members && (
          <DropdownItem
            key={'add-member'}
            className={classNames('h-[30px] w-full px-2 py-0 mb-0.5 hover:!bg-bg-box rounded-md')}
            textValue={t('menu.account.addMember')}
          >
            <Link
              href={EntRouters.setting + '/member/add'}
              className={'text-color-major leading-3 text-normal'}
            >
              {t('menu.account.addMember')}
            </Link>
          </DropdownItem>
        )}

        <DropdownItem
          key={'workspace'}
          className={classNames('h-[30px] w-full px-2 py-0 mb-0.5 hover:!bg-bg-box rounded-md')}
        >
          <Link
            href={EntRouters.setting + '/workspace'}
            className={'text-color-major leading-3 text-normal'}
          >
            {t('menu.account.config')}
          </Link>
        </DropdownItem>

        <DropdownItem
          key={'sign-out'}
          onPress={() => {
            setFavourites([]); // Xóa danh sách yêu thích khi đăng xuất
            signOut();
          }}
          className={classNames('h-[30px] w-full px-2 py-0 mb-0.5 hover:!bg-bg-box rounded-md')}
        >
          <span className={'text-color-major leading-3 text-normal'}>
            {t('menu.account.logout')}
          </span>
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  );
};
export default HeadingMain;
