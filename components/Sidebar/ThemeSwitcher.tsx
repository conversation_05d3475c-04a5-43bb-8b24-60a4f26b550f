'use client';

import { memo } from 'react';

import CategoryEnum from '@/configs/CategoryEnum';
import useCategoryStore from '@/store/category';
import { CategoryEntity } from '@/types/model';
import Tabs from 'components/Tabs';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import toast from 'react-hot-toast';

import saveUserThemeHook from '@/hooks/common/saveUserThemeHook';
import { useSession } from '@/hooks/useSession';

const ThemeSwitcher = () => {
  const t = useTranslations();
  const { theme, setTheme } = useTheme();
  const { data: session } = useSession();
  const { categories } = useCategoryStore();
  const themesConfigList: CategoryEntity[] =
    categories?.filter((item: CategoryEntity) => item.parent_id === CategoryEnum.THEME_LIST) || [];

  const handleChangeTheme = (newTheme: string) => {
    if (newTheme) {
      const selectedTheme: CategoryEntity | undefined =
        themesConfigList && themesConfigList.find((themeItem) => themeItem.keyx === newTheme);
      if (selectedTheme) {
        const myTheme = themesConfigList.find((item) => item.keyx === selectedTheme.keyx);
        if (!myTheme) return;
        const toastId = toast.loading(t('message.toast_loading'));
        saveUserThemeHook(myTheme as CategoryEntity, session.accessToken).then(() => {
          setTheme(myTheme.keyx);
          toast.dismiss(toastId);
        });
      }
    }
  };
  if (!themesConfigList) return <></>;
  return (
    <div className={'flex mb-2 flex-col'}>
      <span>{t('sidebar.theme_chooser')}</span>
      <div className={'inline-flex mt-1'}>
        <Tabs
          tabs={themesConfigList}
          value={'keyx'}
          onSelectionChange={handleChangeTheme}
          activeTabId={theme}
        />
      </div>
    </div>
  );
};
export default memo(ThemeSwitcher);
