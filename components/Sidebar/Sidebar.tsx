'use client';

import React, { useEffect, useState } from 'react';

import { usePathname } from 'next/navigation';

import {
  SIDEBAR_MAIN_PARENT_ID,
  SIDEBAR_SETTING_MEMBER_PARENT_ID,
  SIDEBAR_SETTING_PARENT_ID,
} from '@/configs';
import { CategoryEntity } from '@/types/model';
import classNames from 'classnames';
import { SidebarEnum } from 'configs/SidebarEnum';
import { map } from 'lodash';
import useCategoryStore from 'store/category';
import useSidebarStore from 'store/sidebar';
import { type MenuItem, SidebarProps } from 'types/sidebar';

import ScrollArea from '@/components/ScrollArea';
import HeadingSetting from '@/components/Sidebar/HeadingSetting';
import SideBarFooter from '@/components/Sidebar/SideBarFooter';

import { useBalance } from '@/hooks/Ent/useBalance';
import { useSession } from '@/hooks/useSession';

import HeadingMain from './HeadingMain';
import SideBarList from './SideBarList';

const Sidebar = ({ categories, heading }: SidebarProps) => {
  const { data: session } = useSession();
  useBalance();
  const { setCategories } = useCategoryStore();
  const { isOpenSidebar, setOpenSidebar } = useSidebarStore();
  const pathname = usePathname();
  const [isMobile, setIsMobile] = useState(
    typeof window !== 'undefined' && window.innerWidth < 1024
  );
  const [sidebar, setSidebar] = useState<CategoryEntity[]>([]);
  const [sidebarType, setSidebarType] = useState(SidebarEnum.MAIN);

  useEffect(() => {
    if (categories) {
      setCategories(categories);
    }
  }, [categories]);

  useEffect(() => {
    const handleResize = () => {
      const isNowMobile = window.innerWidth < 1024;
      setIsMobile(isNowMobile);
      if (!isNowMobile) {
        setOpenSidebar(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [setOpenSidebar]);

  const generateTreeList = (lists: MenuItem[]) => {
    // Create a map to store posts by their id for fast lookup
    const categoryMap = new Map();
    for (const category of lists) {
      category.items = [];
      categoryMap.set(category.id, category);
    }

    // Initialize the root of the tree
    const root: MenuItem[] = [];

    // Iterate through the categorys to build the tree
    for (const category of lists) {
      if (category.parent_id !== null && categoryMap.has(category.parent_id)) {
        const parentPost = categoryMap.get(category.parent_id);
        parentPost.items.push(category);
      } else {
        // If a category has no parent, it's a root level category
        root.push(category);
      }
    }

    return root;
  };

  useEffect(() => {
    const treeList = generateTreeList(categories ?? []);
    let sidebar_id = SIDEBAR_MAIN_PARENT_ID;
    let sidebarType = SidebarEnum.MAIN;
    if (pathname.indexOf('/setting/') >= 0) {
      sidebarType = SidebarEnum.SETTING;
    }
    if (sidebarType === SidebarEnum.SETTING) {
      sidebar_id = SIDEBAR_SETTING_PARENT_ID;
      if (!session.members) {
        sidebar_id = SIDEBAR_SETTING_MEMBER_PARENT_ID;
      }
    }
    setSidebarType(sidebarType);
    if (sidebar_id === 219) {
      // Gộp tất cả item con của những node có parent_id === 219
      const mergedItems = treeList.filter((item: CategoryEntity) => item.parent_id === 219);

      const sidebarMember: CategoryEntity = {
        keyx: '',
        parent_id: 7,
        status: 2,
        id: 219,
        title: 'Menu Setting',
        title_vi: 'Menu Setting',
        icon: '',
        is_favourite: 0,
        valuex: '',
        items: mergedItems,
      };

      setSidebar([sidebarMember]);
      return;
    }
    const _sidebar = treeList.find((item: CategoryEntity) => item.id === sidebar_id);

    if (_sidebar) setSidebar(_sidebar.items);
  }, [categories, pathname]);

  if (!sidebar || sidebar.length === 0) return <></>;

  return (
    <>
      <div
        className={classNames(
          'fixed inset-y-0 left-0 z-40  bg-bg-general transition-transform transform lg:relative lg:translate-x-0 flex flex-col',
          {
            '-translate-x-full': !isOpenSidebar,
            'translate-x-0': isOpenSidebar,
          }
        )}
      >
        <nav className="h-screen flex flex-col  border-r border-color-line bg-bg-general z-10">
          <div className="flex-grow flex flex-col">
            <div className="flex justify-between p-4">
              <button
                className="cursor-pointer text-black opacity-50 lg:hidden pr-[2px] py-1 text-xl leading-none bg-transparent rounded border border-solid border-transparent"
                type="button"
                onClick={() => setOpenSidebar(false)}
              >
                <i className="text-2xl icon-close text-color-minor" />
              </button>
              {heading ? (
                heading
              ) : sidebarType === SidebarEnum.MAIN ? (
                <HeadingMain />
              ) : (
                <HeadingSetting />
              )}
            </div>
            <div className="h-[calc(100vh_-_260px)]">
              <ScrollArea className="h-full relative w-full flex-1 bg-bg-general overflow-x-hidden lg:w-[13.75rem] ">
                <div
                  className={classNames(
                    'lg:flex lg:flex-col lg:items-stretch lg:opacity-100 lg:relative lg:shadow-none lg:mt-0 pr-[20px] pl-[10px] h-full',
                    'shadow top-0 left-0 right-0 z-40 flex-1 rounded'
                  )}
                >
                  {sidebar &&
                    map(sidebar, (menu: MenuItem, index) => (
                      <SideBarList key={`side-bar-list-${index}`} sidebar={menu} />
                    ))}
                </div>
              </ScrollArea>
            </div>
          </div>
          <div className="p-4">
            <SideBarFooter />
          </div>
        </nav>
      </div>

      {isMobile && isOpenSidebar && (
        <div
          className="fixed inset-0 bg-black opacity-50 z-30"
          onClick={() => setOpenSidebar(false)}
        ></div>
      )}
    </>
  );
};

export default Sidebar;
