'use client';

import React from 'react';

import classNames from 'classnames';
import { Button } from 'components';
import { EntRouters } from 'configs';
import isEmpty from 'lodash/isEmpty';
import { useTranslations } from 'next-intl';

const HeadingSetting = ({ title = '' }: { title?: string | null }) => {
  const t = useTranslations();
  return (
    <h2 className="md:min-w-full text-blueGray-500 text-xl block pt-0 pb-2 no-underline">
      <Button size={'icon'} color={'default'} as={'a'} href={EntRouters.home}>
        <i className={classNames('icon-arrow-left text-[1.25rem]')} />
      </Button>
      <span className={'pl-[8px]'}>{!isEmpty(title) ? title : t('settings.title')}</span>
    </h2>
  );
};
export default HeadingSetting;
