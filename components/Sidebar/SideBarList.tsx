'use client';

import React from 'react';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

import classNames from 'classnames';
import { useLocale } from 'next-intl';
import { MenuItem } from 'types/sidebar';

interface SideBarListProps {
  sidebar: MenuItem;
}
interface ChildrenListProps {
  items: Array<MenuItem>;
}
const ChildrenList = ({ items }: ChildrenListProps) => {
  const pathname = usePathname();
  const locale = useLocale();
  const getTitleByLocale = (sidebar: MenuItem) => {
    if (
      typeof sidebar['title_' + locale] !== 'undefined' &&
      sidebar['title_' + locale] !== '' &&
      locale !== 'en'
    )
      return sidebar['title_' + locale];
    return sidebar.title;
  };
  return (
    <ul>
      {items &&
        items.map((item) => (
          <li className="" key={`li-${item.id}`}>
            {item.keyx !== '' ? (
              <Link
                href={item.keyx === '/' ? '/' : '/' + item.keyx}
                shallow={true}
                prefetch={false}
                key={`link-${item.id}`}
                className={classNames(
                  'text-normal cursor-pointer rounded-md py-[0.5px] mt-0.5 flex px-[9px] items-center text-color-major hover:bg-bg-box',
                  {
                    'bg-bg-box': pathname === '/'.concat(item.keyx) || pathname === item.keyx,
                  }
                )}
              >
                <i className={classNames(item.icon, 'w-4 h-4')}></i>
                <span className={'pl-[8px]'}>{getTitleByLocale(item)}</span>
              </Link>
            ) : (
              <h6
                className={classNames(
                  'md:min-w-full first:!pt-[0.625rem] text-color-minor text-xs block pl-2 pb-1 no-underline text-[12px]'
                )}
              ></h6>
            )}
          </li>
        ))}
    </ul>
  );
};

const SideBarList = ({ sidebar }: SideBarListProps) => {
  const locale = useLocale();
  const pathname = usePathname();
  // const renderIcon = (icon: string | null) => {

  // if (icon === null || icon === '') return <ListIcon className={'w-4 h-4 mr-1'} />;
  // @ts-ignore
  // const NamedComponent = IconName[icon] ?? 'ListIcon';
  // const DynamicIcon = dynamic(() => import(`components/Icons/${NamedComponent}`));
  // @ts-ignore
  // return <DynamicIcon className={'w-4 h-4'} />;
  // };
  const getTitleByLocale = (sidebar: MenuItem) => {
    if (
      typeof sidebar['title_' + locale] !== 'undefined' &&
      sidebar['title_' + locale] !== '' &&
      locale !== 'en'
    )
      return sidebar['title_' + locale];
    return sidebar.title;
  };

  return (
    <>
      <ul className="md:flex-col md:min-w-full flex flex-col list-none leading-[26px]">
        <li className="" key={`li-${sidebar.id}`}>
          {sidebar.keyx !== '' ? (
            <Link
              href={sidebar.keyx === '/' ? '/' : '/' + sidebar.keyx}
              key={`link-${sidebar.id}`}
              prefetch={false}
              className={classNames(
                'text-normal cursor-pointer rounded-md py-[0.5px] mt-0.5 flex px-[9px] items-center text-color-major hover:bg-bg-box',
                {
                  'bg-bg-box': pathname === '/'.concat(sidebar.keyx) || pathname === sidebar.keyx,
                }
              )}
            >
              <i className={classNames(sidebar.icon, 'w-4 h-4')}></i>
              <span className={'pl-[8px]'}>{getTitleByLocale(sidebar)}</span>
            </Link>
          ) : (
            <h6
              className={classNames(
                'md:min-w-full first:!pt-[0.625rem] text-color-minor text-xs block pl-2 pb-1 no-underline text-[12px]'
              )}
            ></h6>
          )}
          {sidebar.items && <ChildrenList items={sidebar.items} key={`child-${sidebar.id}`} />}
        </li>
      </ul>
      {/*{sidebar.showHr ? <hr className='my-4 md:min-w-full' /> : null}*/}
    </>
  );
};
export default SideBarList;
