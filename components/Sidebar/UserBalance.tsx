'use client';

import useBalanceStore from '@/store/balance';
import { Card, CardBody, Skeleton } from '@heroui/react';
import { useTranslations } from 'next-intl';

const UserBalance = () => {
  const t = useTranslations();
  const { balanceString } = useBalanceStore();
  return (
    <Card className={'bg-bg-general border border-color-line rounded-md'}>
      <CardBody>
        <div className={'flex items-center justify-between'}>
          <span className={'text-color-minor text-normal'}>{t('sidebar.balance')}</span>
          <div className={'text-yellow-100 flex items-center justify-end'}>
            <span>
              {balanceString !== '' ? balanceString : <Skeleton className={'w-5 h-3 rounded-md'} />}
            </span>
            <span className={'icon-diamond ml-3'}></span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};
export default UserBalance;
