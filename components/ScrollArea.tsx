'use client';

import classNames from 'classnames';
import useLayoutStore from 'store/layout';

const ScrollArea = ({ className, children, isEnabled = true }) => {
  const { enableScroll } = useLayoutStore();
  return (
    <div
      className={classNames(
        'scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general focus-visible:outline-none will-change-scroll',
        className,
        {
          '!overflow-y-scroll': enableScroll && isEnabled,
          'overflow-y-hidden': !enableScroll || !isEnabled,
        }
      )}
    >
      {children}
    </div>
  );
};
export default ScrollArea;
