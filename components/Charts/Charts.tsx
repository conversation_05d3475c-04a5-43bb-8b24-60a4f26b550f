import React, { ReactNode } from 'react';

import { ChartItem } from '@/types/component';
import { map } from 'lodash';
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import useChartConfig from '@/hooks/Ent/useChartConfig';

const Charts = ({
  charts = [],
  xKey = 'day',
  columns,
  tooltip = null,
  children = null,
  label = 'Point',
}: {
  charts: ChartItem[];
  xKey?: string;
  columns: [];
  tooltip?: ReactNode | Element | null;
  children?: ReactNode | null;
  label?: string;
}) => {
  const { colors } = useChartConfig();

  return (
    <ResponsiveContainer width={'100%'} height="100%">
      {!charts.length ? (
        <div className={'w-full h-full flex items-center justify-center border border-bg-box'}>
          Không có dữ liệu
        </div>
      ) : (
        <ComposedChart
          height={300}
          width={500}
          data={charts}
          barSize={20}
          margin={{
            top: 0,
            right: 0,
            left: -25,
            bottom: 5,
          }}
        >
          <CartesianGrid
            horizontal={true}
            vertical={false}
            stroke={colors.borderColor}
            strokeWidth={1}
            strokeDasharray="1 0"
          />
          <XAxis
            axisLine={{ stroke: colors.borderColor }}
            fill={colors.titleColor}
            tick={{ fill: colors.titleColor, fontSize: 10 }} //chữ
            dataKey={xKey}
            tickLine={false}
          />
          <YAxis
            domain={[0, (dataMax) => Math.ceil(dataMax * 1.1)]}
            axisLine={{ stroke: colors.borderColor }}
            fill={colors.titleColor}
            tick={{ fill: colors.titleColor, fontSize: 10 }} //chữ
            tickLine={false} //chữ
            label={{
              value: label,
              angle: -90,
              position: 'outsideLeft',
              dx: -2,
              style: { textAnchor: 'middle', fontSize: 10, fill: colors.titleColor },
            }}
          />
          <Tooltip
            // @ts-ignore
            content={tooltip ? tooltip : null}
            cursor={{ fill: 'none' }}
            contentStyle={{
              backgroundColor: colors.borderColor, // Màu nền
              color: colors.titleColor, // Màu chữ trắng
              border: `1px solid ${colors.borderColor}`, // Viền màu cam
              borderRadius: '8px', // Bo góc
              padding: '10px', // Khoảng cách bên trong
            }}
          />
          {children
            ? children
            : map(columns, (key, index) => (
                <Bar
                  barSize={30}
                  key={index}
                  dataKey={key}
                  stackId="a"
                  className={'text-color-minor'}
                  fill={colors.barColor[key]}
                />
              ))}
        </ComposedChart>
      )}
    </ResponsiveContainer>
  );
};

export default Charts;
