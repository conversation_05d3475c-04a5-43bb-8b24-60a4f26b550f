'use client';

import React, { useCallback } from 'react';

import useFavouriteStore from '@/store/favourite';
import classNames from 'classnames';
import useFavourite from 'hooks/Ent/useFavourite';

interface FavouriteItem {
  item: string;
  object_id: number;
}

interface FavouriteButtonProps {
  favouriteList: FavouriteItem[] | [];
  item: string;
  object_id: number;
}

const FavouriteButton: React.FC<FavouriteButtonProps> = ({ favouriteList, item, object_id }) => {
  const { addFavourite, removeFavourite } = useFavouriteStore();
  const { saveFavourite } = useFavourite();

  // Kiểm tra xem item có trong danh sách favourite không
  const isToggled = favouriteList.some(
    (fav: any) => fav.object_id === object_id && fav.item === item
  );

  const handleToggleFavourite = useCallback(async () => {
    if (isToggled) {
      removeFavourite({ item, object_id });
    } else {
      addFavourite({ item, object_id });
    }

    try {
      await saveFavourite(isToggled ? 1 : 2, item, object_id);
    } catch (error) {
      console.error('Lỗi khi cập nhật favourite:', error);
      // Rollback nếu API thất bại
      if (isToggled) {
        addFavourite({ item, object_id });
      } else {
        removeFavourite({ item, object_id });
      }
    }
  }, [isToggled, addFavourite, removeFavourite, item, object_id, saveFavourite]);

  return (
    <div className={classNames({ 'group-hover:flex hidden': !isToggled })}>
      <div
        onClick={handleToggleFavourite}
        className="flex items-center justify-center w-[28px] h-[27px] rounded-md ml-2 group-hover:bg-bg-box cursor-pointer"
      >
        <div
          className={classNames('text-[15px] group-hover:bg-grey-300 transition-all duration-200', {
            'icon-star-line fill-color-minor': !isToggled,
            'icon-star-fill text-yellow-100': isToggled,
          })}
        ></div>
      </div>
    </div>
  );
};

export default FavouriteButton;
