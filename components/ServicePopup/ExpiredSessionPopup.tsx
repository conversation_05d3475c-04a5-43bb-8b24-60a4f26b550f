'use client';

import React, { useState } from 'react';

import { useRouter } from 'next/navigation';

import Button from 'components/Button';
import Modal from 'components/Modal';
import EntRouters from 'configs/EntRouters';
import { useTranslations } from 'next-intl';

const ExpiredSessionPopup = () => {
  const [open, setOpen] = useState(false);
  const t = useTranslations();
  const router = useRouter();
  const handleClickLogin = () => {
    router.push(EntRouters.home);
  };

  return (
    <Modal opened={open} onOpenChange={setOpen}>
      <div className="text-normal text-black flex flex-col items-center text-center px-11 py-6">
        <p className="font-bold mt-4 mb-2">{t('expiredSession')}</p>
        <p className="font-normal mb-6">{t('expiredSessionDescription')}</p>

        <Button onClick={handleClickLogin} color={'primary'} size={'md'}>
          Login
        </Button>
      </div>
    </Modal>
  );
};

export default ExpiredSessionPopup;
