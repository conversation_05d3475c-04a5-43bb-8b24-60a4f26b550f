'use client';

import React, { useEffect, useState } from 'react';

import { useParams } from 'next/navigation';

import { ButtonGroup } from '@heroui/react';
import Button from 'components/Button';
import Input from 'components/Input';
import Modal from 'components/Modal';
import useCreatedGroups from 'hooks/Ent/useCreatedGroups';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';
import { GroupAddPopupProps } from 'types/popup';

const GroupAddPopup = ({ title, type, open, onOpen, onClose }: GroupAddPopupProps) => {
  const params = useParams();
  const parent_id = params.id?.toString() || '';
  const t = useTranslations();
  const [inputValue, setInputValue] = useState(''); // Quản lý trạng thái của Input
  const [error, setError] = useState(''); // Quản lý trạng thái lỗi
  const handleConfirm = async (type: number | undefined) => {
    if (!inputValue) {
      setError(t('Vui lòng điền tên.'));
    } else {
      setError('');
      // console.log(inputValue);
      try {
        const group_id = parent_id ? parseInt(parent_id, 10) : 0;
        const { doCreatedGroups } = useCreatedGroups();
        const groupIdParam = group_id > 0 ? group_id : -1;
        const res = await doCreatedGroups(group_id, type, groupIdParam, inputValue);
        if (res.success) {
          toast.success(t('Thành công!'));
          if (onClose) {
            // @ts-ignore
            onClose();
          } // Đảm bảo rằng onClose là một hàm và gọi nó
        } else {
          toast.error(t('member.error.exit'));
        }
      } catch (e) {
        toast.error(t('member.error.exit'));
      }
    }
  };
  useEffect(() => {
    if (open) {
      setError(''); // Reset lỗi khi modal được mở
    }
  }, [open]);

  return (
    <Modal
      opened={open}
      size={'xs'}
      onClose={() => onClose}
      onOpenChange={onOpen}
      classNames={{
        base: 'rounded-md max-w-[32%] w-[100%]',
        header: 'font-bold text-base text-color-major text-color-major py-5 px-4 pb-2',
        footer: 'border border-bg-box py-3 px-4 hidden',
        body: 'px-0 py-0',
      }}
      header={<h5 className={'text-sm font-medium'}>{title}</h5>}
    >
      <div className=" items-center px-4 pb-4">
        <Input
          removeLabel={true}
          labelPlacement={'outside'}
          classNames={{
            inputWrapper:
              'bg-inherit px-0 py-0 text-color-major hover:!bg-transparent focus-within:!bg-transparent shadow-none',
            input: '!font-medium text-[17px] placeholder:text-[rgba(107,111,118,0.5)]',
          }}
          onChange={(e) => setInputValue(e.target.value)}
          type={'text'}
          placeholder={'Điền tên ' + (type == 2 ? 'lớp' : 'tổ chức')}
        />
        {error && <p className="text-red ">{error}</p>}
      </div>

      <ButtonGroup className={'flex-row gap-2 justify-end border-t-[1px] border-bg-box py-3 px-4'}>
        <Button
          onClick={onClose}
          size={'xs'}
          variant={'bordered'}
          color={'default'}
          className={'shadow-md'}
        >
          {t('course.cancel_create')}
        </Button>
        <Button color={'primary'} size={'xs'} className={''} onClick={() => handleConfirm(type)}>
          {t('course.confirm_create')}
        </Button>
      </ButtonGroup>
    </Modal>
  );
};

export default GroupAddPopup;
