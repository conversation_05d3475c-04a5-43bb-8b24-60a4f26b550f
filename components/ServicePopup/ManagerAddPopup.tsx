'use client';

import React, { useEffect, useState } from 'react';

import { useParams } from 'next/navigation';

import { ButtonGroup } from '@heroui/react';
import Button from 'components/Button';
import Input from 'components/Input';
import Modal from 'components/Modal';
import useAccountByPhone from 'hooks/Ent/useAccountByPhone';
import useAddManager from 'hooks/Ent/useAddManager';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';
import { GroupAddPopupProps } from 'types/popup';

const isValidPhoneNumber = (phone: string): boolean => {
  return /^(03|05|07|08|09|01[2|6|8|9])+([0-9]{8})\b/.test(phone);
};

const ManagerAddPopup = ({ title, open, onOpen, onClose }: GroupAddPopupProps) => {
  const t = useTranslations();
  const [inputValue, setInputValue] = useState('');
  const [error, setError] = useState('');
  const [accountId, setAccountId] = useState<number | null>(null);

  const phone = isValidPhoneNumber(inputValue) ? inputValue : '';
  const { account, isLoading } = useAccountByPhone(phone);
  const { doAddManager } = useAddManager();
  const { id } = useParams();

  useEffect(() => {
    setAccountId(isValidPhoneNumber(inputValue) && account?.id ? account.id : null);
  }, [inputValue, account]);

  const handleConfirm = async () => {
    if (!inputValue) {
      setError(t('Vui lòng điền số điện thoại.'));
    } else if (!isValidPhoneNumber(inputValue)) {
      setError(t('Số điện thoại không hợp lệ.'));
    } else {
      setError('');
      try {
        const res = await doAddManager({
          account_id: accountId,
          group_id: parseInt(id as string, 10),
        });
        if (res.success) {
          toast.success(t('message.success.common'));

          if (onClose) {
            // @ts-ignore
            onClose();
          }
        } else {
          toast.error(
            t(
              res.message === 'account_id_invalid'
                ? 'message.error.manager.accountManager'
                : 'message.error.exit'
            )
          );
        }
      } catch {
        toast.error(t('member.error.exit'));
      }
    }
  };

  useEffect(() => {
    if (!open) {
      setInputValue('');
      setError('');
    }
  }, [open]);

  return (
    <Modal
      opened={open}
      size={'xs'}
      onClose={() => onClose}
      onOpenChange={onOpen}
      classNames={{
        base: 'rounded-md max-w-[32%] w-[100%]',
        header: 'font-bold text-base text-color-major py-5 px-4 pb-2',
        footer: 'border border-bg-box py-3 px-4 hidden',
        body: 'px-0 py-0',
      }}
      header={<h5 className="text-sm font-medium">{title}</h5>}
    >
      <div className="items-center px-4 pb-4">
        <Input
          removeLabel
          labelPlacement="outside"
          classNames={{
            inputWrapper:
              'bg-inherit px-0 py-0 text-color-major hover:!bg-transparent focus-within:!bg-transparent shadow-none',
            input: '!font-medium text-[17px] placeholder:text-[rgba(107,111,118,0.5)]',
          }}
          onChange={(e) => setInputValue(e.target.value.replace(/\D/g, ''))}
          value={inputValue}
          type="text"
          placeholder="Nhập số điện thoại"
        />
        {isLoading && <p className="text-blue-500">Đang tìm kiếm...</p>}
        {isValidPhoneNumber(inputValue) && account?.fullname && (
          <p className="text-green-500">{`Tên người dùng: ${account.fullname}`}</p>
        )}
        {error && <p className="text-red-500">{error}</p>}
      </div>
      <ButtonGroup className="flex-row gap-2 justify-end border-t-[1px] border-bg-box py-3 px-4">
        <Button
          onClick={onClose}
          size="xs"
          variant="bordered"
          color="default"
          className="shadow-md"
        >
          {t('course.cancel_create')}
        </Button>
        <Button color="primary" size="xs" onClick={handleConfirm}>
          {t('course.confirm_create')}
        </Button>
      </ButtonGroup>
    </Modal>
  );
};

export default ManagerAddPopup;
