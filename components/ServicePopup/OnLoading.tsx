'use client';

import { useRef } from 'react';

import dynamic from 'next/dynamic';

import classNames from 'classnames';
import loadingData from 'components/Icons/loading-animation.json';
import { useMounted } from 'hooks/common/useMounted';

const DynamicLottie = dynamic(() => import('lottie-react'), { ssr: false });

const OnLoading = ({
  size,
  className = '',
  containerClassName = '',
  fullClassName = true,
}: {
  size?: string;
  className?: string;
  containerClassName?: string;
  fullClassName?: boolean;
}) => {
  const loadingSize = useRef('');
  const mounted = useMounted();
  switch (size) {
    case 'sm':
      loadingSize.current = 'w-32';
      break;
    case 'md':
      loadingSize.current = 'w-48';
      break;
    case 'lg':
      loadingSize.current = 'w-64';
      break;
    case 'xl':
      loadingSize.current = 'w-128';
      break;
    case '2xl':
      loadingSize.current = 'w-256';
      break;
    case '3xl':
      loadingSize.current = 'w-512';
      break;
    default:
      loadingSize.current = 'w-12';
      break;
  }
  if (!mounted) return <></>;
  return (
    <div
      className={classNames(
        'flex h-full w-full items-center justify-center absolute z-[400] bg-bg-general',
        containerClassName,
        { 'top-0 left-0': fullClassName }
      )}
    >
      <div className={classNames('opacity-70', loadingSize.current, className)}>
        <DynamicLottie animationData={loadingData} loop={true} />
      </div>
    </div>
  );
};
export default OnLoading;
