'use client';

import React, { useState } from 'react';

import { <PERSON><PERSON>, <PERSON>dalBody, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, ModalHeader } from '@heroui/react';
import Button from 'components/Button';
import { OTP_MEMBER_LENGTH } from 'configs';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';
import OtpInput from 'react-otp-input';
import { MemberSetPassPopupPops } from 'types/popup';

const MemberSetPassPopup = ({ open, onOpen }: MemberSetPassPopupPops) => {
  const t = useTranslations();
  const [OTP, setOTP] = useState('');
  /* const { doChangePassMemberById } = useEditMember();*/
  const handleChangeOTP = (value: string) => {
    setOTP(value);
    // Kiểm tra nếu độ dài của OTP đạt đủ điều kiện để submit
    if (value.length === OTP_MEMBER_LENGTH) {
      //handleSetPass(value,id); // <PERSON><PERSON><PERSON> hàm submit khi điều kiện đạt được
    }
  };
  const handleSetPass = async (pass) => {
    if (pass.length !== OTP_MEMBER_LENGTH) {
      toast.error(t('auth.passWrong'));
      return;
    }
    /*doChangePassMemberById(id,pass).then(( res ) => {
          if (res.success === true) {
              alert('Đã đặt mật khẩu thành công.');
              onOpen(false);
                setOTP('');
          } else {
              alert('Có lỗi xảy ra.');
          }
      });*/
  };
  return (
    <Modal isOpen={open} onOpenChange={onOpen} size={'xs'} className={'rounded-md'}>
      <ModalContent>
        <ModalHeader className={'justify-between'}>
          <h4 className={'text-sm'}>Đặt mật khẩu</h4>
        </ModalHeader>
        <ModalBody>
          <div className="flex w-full flex-col items-center">
            <OtpInput
              value={OTP}
              onChange={handleChangeOTP}
              numInputs={OTP_MEMBER_LENGTH}
              placeholder="0000"
              inputType={'tel'}
              renderSeparator={''}
              renderInput={(props) => <input {...props} />}
              containerStyle={{
                fontSize: '40px',
              }}
              inputStyle={{
                color: '#00A758',
                width: '3rem',
                margin: '0 5px',
                border: '1px solid #E5E5E5',
                outline: 0,
                borderRadius: '8px',
                fontSize: 32,
              }}
            />
          </div>
        </ModalBody>
        <ModalFooter className={'gap-4'}>
          <Button onClick={() => handleSetPass(OTP)} className={'!w-auto'}>
            {t('course.confirm_create')}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default MemberSetPassPopup;
