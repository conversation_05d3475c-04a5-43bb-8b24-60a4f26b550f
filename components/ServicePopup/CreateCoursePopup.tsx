'use client';

import React from 'react';

import Input from 'components/Form/Input';
import Modal from 'components/Modal';
import { useTranslations } from 'next-intl';
import { CreateCoursePopupProps } from 'types/popup';

const CreateCoursePopup = ({ open, onOpen, onClose }: CreateCoursePopupProps) => {
  const t = useTranslations();
  return (
    <Modal
      opened={open}
      onOpenChange={onOpen}
      size={'xs'}
      onClose={() => onClose}
      header={<h4 className={'text-sm'}>{t('course.create_course')}</h4>}
    >
      <div className="flex items-center gap-4">
        <div>{t('course.upload_image')}</div>
        <div className="flex w-full flex-col items-end gap-2">
          <Input
            containerClassName={'text-color-major'}
            labelClassName={'!text-color-minor'}
            className={'text-color-major'}
            label={t('course.course_name')}
          />

          <Input
            containerClassName={'text-color-major'}
            labelClassName={'!text-color-minor'}
            className={'text-color-major'}
            label={t('course.course_description')}
          />
        </div>
      </div>
    </Modal>
  );
};

export default CreateCoursePopup;
