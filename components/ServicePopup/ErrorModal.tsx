'use client';

import React from 'react';

import Modal from 'components/Modal';
import { useTranslations } from 'next-intl';
import usePopupStore from 'store/popup';

const ErrorModal = () => {
  const t = useTranslations();

  const { openedErrorModal, errorMessage, title, setErrorModal } = usePopupStore();

  const handleCloseModal = () => {
    setErrorModal({ opened: false });
  };
  return (
    <Modal
      opened={openedErrorModal}
      buttonSize={'sm'}
      size={'sm'}
      onClose={handleCloseModal}
      onOpenChange={() => setErrorModal}
      header={
        <div>
          <p className="font-bold mb-2 text-color-major">
            {title !== '' ? title : t('message.errorNotice')}
          </p>
        </div>
      }
    >
      <div className="text-normal text-black flex flex-col items-center text-center px-10">
        {errorMessage && <p className="font-normal text-color-major mb-6">{t(errorMessage)}</p>}
      </div>
    </Modal>
  );
};

export default ErrorModal;
