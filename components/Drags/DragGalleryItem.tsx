'use client';

import { HTMLProps, ReactNode } from 'react';

import { AnimateLayoutChanges, defaultAnimateLayoutChanges, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import classNames from 'classnames';

// Custom animateLayoutChanges function
const animateLayoutChanges: AnimateLayoutChanges = (args) =>
  defaultAnimateLayoutChanges({ ...args, wasDragging: true });

type DragItemProps = {
  id: number | string;
  position: number;
  children: ReactNode;
  handleAddItem?: () => void;
  handleAddFirstItem?: () => void;
  className?: HTMLProps<HTMLElement>['className'];
  dragClassName?: HTMLProps<HTMLElement>['className'];
  onClickItem?: () => void;
  handleRemoveItem?: (id: number | string) => void;
  isEnabled: boolean;
};

const DragGalleryItem = ({
  id,
  children,
  handleAddItem,
  handleAddFirstItem,
  className = '',
  position,
  dragClassName = '',
  onClickItem,
  handleRemoveItem,
  isEnabled,
}: DragItemProps) => {
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition } =
    useSortable({
      id: id,
      animateLayoutChanges,
    });

  const handleRemove = () => {
    handleRemoveItem && handleRemoveItem(id);
  };

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  // return null;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      role={'listitem'}
      tabIndex={-1}
      className={classNames('flex flex-col relative', className)}
    >
      {position === 0 && (
        <div
          className={
            'w-full h-4 opacity-0 hover:opacity-100 flex items-center justify-center cursor-pointer relative'
          }
        >
          {handleAddFirstItem && isEnabled && (
            <i
              className={'text-base ml-8 cursor-pointer text-color-minor icon-add'}
              onClick={() => {
                handleAddFirstItem();
              }}
            />
          )}
        </div>
      )}

      <div className={classNames('flex-shrink-0 pt-2 absolute -right-7 bottom-4')}>
        {handleRemoveItem && isEnabled && (
          <i
            onClick={handleRemove}
            className={
              '!hidden group-hover:!flex text-base text-color-minor z-10 icon-delete-bin cursor-pointer'
            }
          ></i>
        )}
      </div>
      <div
        className={'w-full h-full relative pl-8 flex-1 group'}
        onClick={() => {
          onClickItem && onClickItem();
        }}
      >
        <div
          className={classNames('group-hover:block hidden absolute top-1.5 left-2', dragClassName)}
        >
          {isEnabled && (
            <i
              ref={setActivatorNodeRef} // This is the handle for dragging
              {...listeners}
              className={'text-base cursor-pointer text-color-minor icon-arrow-up-down-line'}
            />
          )}
        </div>
        {children}
      </div>
      <div
        className={
          'w-full h-4 flex opacity-0 hover:opacity-100 items-center justify-center cursor-pointer'
        }
        onClick={() => {
          handleAddItem && handleAddItem();
        }}
      >
        {handleAddItem && isEnabled && (
          <i className={'ml-8 text-base cursor-pointer text-color-minor icon-add'} />
        )}
      </div>
    </div>
  );
};

export default DragGalleryItem;
