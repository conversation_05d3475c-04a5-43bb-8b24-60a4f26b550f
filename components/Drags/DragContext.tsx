'use client';

import { ReactNode } from 'react';

import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  closestCenter,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext, arrayMove, sortableKeyboardCoordinates } from '@dnd-kit/sortable';
import { DragItemType } from 'types/component';

type DragContextProp = {
  setHasChange?: (change: boolean) => void;
  setItems: (items: DragItemType[]) => void;
  children: ReactNode;
  items: DragItemType[];
  handleChangePosition?: (activeId: number, moveToId: number) => void;
  onDragEnd?: (items: DragItemType[]) => void;
};
const DragContext = ({
  setHasChange,
  setItems,
  items,
  children,
  handleChangePosition,
  onDragEnd,
}: DragContextProp) => {
  // Sensor setup for drag-and-drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (over && active.id !== over.id) {
      const currentItems: DragItemType[] = [...items];
      const oldIndex = currentItems.findIndex((item) => item.id === active.id);
      const newIndex = currentItems.findIndex((item) => item.id === over.id);
      const newItems = arrayMove(currentItems, oldIndex, newIndex);
      setItems(newItems);
      setHasChange && setHasChange(true);
      handleChangePosition && handleChangePosition(active.id, over.id);
      onDragEnd && onDragEnd(newItems);
    }
  };

  return (
    <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
      <SortableContext items={items}>{children}</SortableContext>
    </DndContext>
  );
};

export default DragContext;
