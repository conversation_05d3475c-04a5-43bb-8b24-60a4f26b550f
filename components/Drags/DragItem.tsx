'use client';

import React, { HTMLProps, ReactNode } from 'react';

import { AnimateLayoutChanges, defaultAnimateLayoutChanges, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import classNames from 'classnames';

// Custom animateLayoutChanges function
const animateLayoutChanges: AnimateLayoutChanges = (args) =>
  defaultAnimateLayoutChanges({ ...args, wasDragging: true });

type DragItemProps = {
  id: number | string;
  position: number;
  children: ReactNode;
  handleAddItem?: (position: number) => void;
  handleRemoveItem?: (id: number | string) => void;
  className?: HTMLProps<HTMLElement>['className'];
  addItemClassName?: HTMLProps<HTMLElement>['className'];
  removeItemClassName?: HTMLProps<HTMLElement>['className'];
  dragClassName?: HTMLProps<HTMLElement>['className'];
};

const DragItem = ({
  id,
  position,
  children,
  handleRemoveItem,
  handleAddItem,
  className = '',
  removeItemClassName = '',
  addItemClassName = '',
  dragClassName = '',
}: DragItemProps) => {
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition } =
    useSortable({
      id: id,
      animateLayoutChanges,
    });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  const handleAdd = () => {
    handleAddItem && handleAddItem(position);
  };

  const handleRemove = () => {
    handleRemoveItem && handleRemoveItem(id);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      role={'listitem'}
      tabIndex={-1}
      className={classNames('flex relative group', className)}
    >
      {handleRemoveItem && (
        <div
          className={classNames(
            'w-6 hidden group-hover:!flex items-center mt-1 justify-center flex-shrink-0 hover:bg-bg-box hover:shadow-md rounded-full h-6',
            removeItemClassName
          )}
        >
          <i
            onClick={handleRemove}
            className={'text-medium text-color-minor z-10 icon-delete-bin cursor-pointer'}
          ></i>
        </div>
      )}
      {children}
      {handleAddItem && (
        <div
          className={classNames(
            'group-hover:flex hidden items-center rounded-full justify-center w-6 h-6 hover:bg-bg-box hover:shadow-md absolute -bottom-0.5 -right-2',
            addItemClassName
          )}
        >
          <i
            className={'text-medium cursor-pointer text-color-minor icon-add'}
            onClick={handleAdd}
          />
        </div>
      )}
      <div
        className={classNames(
          'group-hover:flex hidden items-center justify-center absolute top-1.5 right-0 hover:bg-bg-box hover:shadow-md rounded-full w-6 h-6',
          dragClassName
        )}
      >
        <i
          ref={setActivatorNodeRef} // This is the handle for dragging
          {...listeners}
          className={'text-medium cursor-pointer text-color-minor icon-arrow-up-down-line'}
        />
      </div>
    </div>
  );
};

export default DragItem;
