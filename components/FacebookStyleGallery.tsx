import React, { useEffect, useRef, useState } from 'react';

import CropImage from '@/components/CropImage';

const FacebookStyleGallery = ({ images }: { images: { image: string }[] }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [targetWidth, setTargetWidth] = useState<number>(540);
  const maxVisibleImages = 4;

  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setTargetWidth(containerRef.current.offsetWidth);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  if (!images || images.length === 0) return null;
  return (
    <div className="grid grid-cols-1 max-w-[540px]">
      {images.length === 1 ? (
        <div className="relative col-span-2 cursor-pointer">
          <div className="relative max-w-[540px] aspect-[16/9]">
            <CropImage
              key={1}
              src={images[0].image}
              targetWidth={targetWidth}
              targetHeight={(targetWidth * 9) / 16}
            />
          </div>
        </div>
      ) : (
        <>
          {images.length === 3 ? (
            <>
              <div className="grid grid-rows-1 gap-2 mb-2">
                <div className="relative cursor-pointer" key={1}>
                  <div className="relative max-w-[540px] aspect-[16/9]">
                    <CropImage
                      src={images[0].image}
                      targetWidth={targetWidth}
                      targetHeight={(targetWidth * 9) / 16}
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="relative cursor-pointer" key={2}>
                  <div className="relative w-full aspect-[16/9]">
                    <CropImage
                      src={images[1].image}
                      targetWidth={targetWidth}
                      targetHeight={(targetWidth * 9) / 16}
                    />
                  </div>
                </div>
                <div className="relative cursor-pointer aspect-[16/9]" key={3}>
                  <div className="relative w-full aspect-[16/9]">
                    <CropImage
                      src={images[2].image}
                      targetWidth={targetWidth}
                      targetHeight={(targetWidth * 9) / 16}
                    />
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              {images.length === 4 ? (
                <div className="grid grid-cols-2 gap-2">
                  {images.map((image, index) => (
                    <div className="relative cursor-pointer" key={index}>
                      <div className="relative w-full aspect-[16/9]">
                        <CropImage
                          src={image.image}
                          targetWidth={targetWidth}
                          targetHeight={(targetWidth * 9) / 16}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-2 gap-2 mb-2">
                    {images.slice(0, 2).map((image, index) => (
                      <div className="relative cursor-pointer" key={index}>
                        <div className="relative w-full aspect-[16/9]">
                          <CropImage
                            src={image.image}
                            targetWidth={targetWidth}
                            targetHeight={(targetWidth * 9) / 16}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="grid grid-cols-3 gap-2 sm:col-span-2">
                    {images.slice(2, maxVisibleImages).map((image, index) => (
                      <div className="relative cursor-pointer" key={index + 2}>
                        <div className="relative w-full aspect-[16/9]">
                          <CropImage
                            src={image.image}
                            targetWidth={targetWidth}
                            targetHeight={(targetWidth * 9) / 16}
                          />
                        </div>
                      </div>
                    ))}
                    {/* Ảnh thứ 5 trở đi gộp lại */}
                    {images.length === 5 && (
                      <div className="relative cursor-pointer" key={5}>
                        <div className="relative w-full aspect-[16/9]">
                          <CropImage
                            src={images[maxVisibleImages].image}
                            targetWidth={targetWidth}
                            targetHeight={(targetWidth * 9) / 16}
                          />
                        </div>
                      </div>
                    )}
                    {images.length > 5 && (
                      <div className="relative cursor-pointer" key={6}>
                        <div className="relative w-full aspect-[16/9]">
                          <div className="w-full h-full object-cover rounded-lg brightness-50">
                            <CropImage
                              src={images[maxVisibleImages].image}
                              targetWidth={targetWidth}
                              targetHeight={(targetWidth * 9) / 16}
                            />
                          </div>
                          {images[maxVisibleImages].image && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <span className="text-white font-bold text-lg">
                                +{images.length - maxVisibleImages}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};

export default FacebookStyleGallery;
