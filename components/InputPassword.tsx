import React from 'react';

import { InputProps } from '@heroui/react';

import Input from '@/components/Input';

function InputPassword(props: InputProps) {
  const [isVisible, setIsVisible] = React.useState(false);

  const toggleVisibility = () => setIsVisible(!isVisible);
  return (
    // @ts-ignore
    <Input
      {...props}
      endContent={
        <button
          aria-label="toggle password visibility"
          className="focus:outline-none bg-transparent cursor-pointer rounded-full w-8 h-8 mt-1 hover:bg-bg-box"
          type="button"
          onClick={toggleVisibility}
        >
          {isVisible ? (
            <i className={'text-base icon-eye text-color-major'} />
          ) : (
            <i className={'text-base icon-eye-close'} />
          )}
        </button>
      }
      labelPlacement={'outside'}
      type={isVisible ? 'text' : 'password'}
    />
  );
}
export default InputPassword;
