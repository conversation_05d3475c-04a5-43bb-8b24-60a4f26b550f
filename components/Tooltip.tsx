'use client';

import { Tooltip as OriginalTooltip, TooltipProps, extendVariants } from '@heroui/react';

const UiTooltip = extendVariants(OriginalTooltip, {
  variants: {
    color: {
      foreground: {
        base: 'before:bg-bg-box dark:before:bg-bg-box',
        content: '',
        arrow: '',
      },
    },
  },
  defaultVariants: {
    color: 'foreground',
    radius: 'sm',
  },
});

export const Tooltip = (props: TooltipProps) => {
  return (
    // @ts-ignore
    <UiTooltip
      showArrow
      className={'px-2 bg-bg-box shadow-md text-color-major'}
      color={'foreground'}
      {...props}
    >
      {props?.children}
    </UiTooltip>
  );
};
