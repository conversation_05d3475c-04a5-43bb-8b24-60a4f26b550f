'use client';

import { Button as NextUiButton, extendVariants } from '@heroui/react';

const Button = extendVariants(NextUiButton, {
  variants: {
    // <- modify/add variants
    color: {
      primary:
        'bg-primary px-8 font-[Inter] text-white cursor-pointer !shadow-[inset_0px_-2px_1px_0px_#01AE4E]',
      default: 'bg-bg-button text-color-major border-color-border shadow !shadow-color-white',
      violet: 'bg-[#8b5cf6] text-[#fff]',
      white: 'bg-white border-color-border font-[Inter] text-black cursor-pointer',
    },
    isDisabled: {
      true: '!bg-[#01AE4E] text-[#fff] opacity-50 cursor-not-allowed',
      primary: '!bg-[#01AE4E] text-[#fff] opacity-50 cursor-not-allowed',
    },
    size: {
      icon: 'h-8 w-8 text-small min-w-0 gap-0 px-0 rounded-full bg-transparent border-transparent shadow-none hover:bg-bg-general hover:!shadow',
      xs: 'h-[26px] px-2 min-w-12 text-tiny gap-1 rounded-small',
      sm: 'h-7 min-w-12 h-6 text-tiny gap-1 rounded-small shadow-md border border-color-border',
      md: 'h-[1.75rem] text-small shadow-medium px-6 min-w-20 rounded-medium',
      lg: 'h-[2.1875rem] text-base rounded-medium',
      xl: 'h-[2.5rem] px-8 min-w-28 h-14 text-large gap-4 rounded-medium',
      '2xl': 'h-[2.5rem] px-8 min-w-28 h-14 text-large gap-4 rounded-medium',
    },
  },
  defaultVariants: {
    // <- modify/add default variants
    color: 'default',
    size: 'md',
  },
  compoundVariants: [
    // <- modify/add compound variants
    {
      isDisabled: false,
      color: 'default',
      class: 'bg-bg-button',
    },
    {
      isDisabled: true,
      color: 'default',
      class: 'bg-bg-button',
    },
  ],
});

// const Button = (props: ButtonProps) => {
//   return <ButtonStyle {...props} />;
// };

export default Button;
