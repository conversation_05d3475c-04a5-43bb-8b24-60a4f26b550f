'use client';

import React, { ChangeEvent, ClipboardEvent, FocusEvent, KeyboardEvent, ReactNode } from 'react';

import { StaticImageData } from 'next/image';

import classNames from 'classnames';
import Image from 'components/Image';

export type InputMode =
  | 'search'
  | 'none'
  | 'text'
  | 'tel'
  | 'url'
  | 'email'
  | 'numeric'
  | 'decimal'
  | undefined;

export enum InputType {
  NORMAL = 'normal',
  LABEL = 'label',
}
export interface InputProps {
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  onPaste?: (e: ClipboardEvent<HTMLInputElement>) => void;
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (e: KeyboardEvent<HTMLInputElement>) => void;
  onKeyUp?: (e: KeyboardEvent<HTMLInputElement>) => void;
  onRightIconClick?: () => void;
  onClick?: (val?: any) => void;
  className?: string;
  rightIconClass?: string;
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
  rows?: number;
  disabled?: boolean;
  value?: string;
  defaultValue?: ReactNode;
  label?: string;
  placeholder?: string;
  name?: string;
  type?: string;
  pattern?: string;
  errorMessage?: string | boolean;
  leftIcon?: ReactNode;
  inputMode?: InputMode;
  maxLength?: number;
  showIcon?: string;
  hideIcon?: string;
  rightIcon?: string | StaticImageData;
  errorIcon?: string | StaticImageData;
  readOnly?: boolean;
  required?: boolean;
  inputType?: InputType;
  autoFocus?: boolean;
  activeWhenFocus?: boolean;
  requiredClassName?: string;
  checkIcon?: ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(function Input(
  {
    onChange,
    onKeyDown,
    onKeyUp,
    onRightIconClick,
    rightIconClass,
    requiredClassName,
    name,
    label,
    placeholder,
    value,
    defaultValue,
    className = '',
    containerClassName = '',
    labelClassName = '',
    inputClassName = '',
    disabled = false,
    errorMessage = '',
    type,
    leftIcon,
    errorIcon,
    inputMode = 'text',
    maxLength,
    rightIcon,
    pattern,
    readOnly = false,
    inputType = InputType.LABEL,
    activeWhenFocus = true,
    checkIcon,
    ...props
  },
  ref
) {
  const normalInput = inputType === InputType.NORMAL;
  const labelInput = inputType === InputType.LABEL;

  return (
    <>
      <div onClick={props?.onClick} className={classNames('relative', containerClassName)}>
        {label && (
          <label
            className={classNames(
              {
                'bg-bg-general text-sm leading-[18px] absolute left-3 top-[-8px] px-2 z-[2] rounded':
                  labelInput,
                'leading-6 text-[#2B2B2B]': normalInput,
              },
              labelClassName
            )}
          >
            {label}{' '}
            {props?.required ? (
              <span className={classNames('text-red', requiredClassName)}>*</span>
            ) : (
              ''
            )}
          </label>
        )}
        <div
          className={classNames(
            'relative border border-color-border rounded-lg',
            'leading-5 flex items-center',
            {
              '!border-red': errorMessage,
              'pl-4': leftIcon,
              'focus-within:border-border-input-focus': activeWhenFocus,
            },
            className,
            { 'bg-gray-200': disabled }
          )}
        >
          {leftIcon && leftIcon}

          {defaultValue && <p className="pl-4 w-full">{defaultValue}</p>}
          <input
            ref={ref}
            type={type ?? 'text'}
            name={name}
            placeholder={placeholder || ''}
            value={value}
            disabled={disabled}
            onChange={onChange}
            className={classNames(
              'outline-none px-4 w-full rounded-md',
              {
                'pl-1': defaultValue || leftIcon,
              },
              inputClassName,
              { 'bg-gray-200': disabled }
            )}
            onKeyDown={onKeyDown}
            onKeyUp={onKeyUp}
            inputMode={inputMode}
            maxLength={maxLength}
            pattern={pattern}
            readOnly={readOnly}
            {...props}
          />

          {rightIcon && (
            <button type="button" onClick={onRightIconClick}>
              <Image
                alt="right-action"
                src={rightIcon}
                width={10}
                height={8}
                className={classNames(
                  'absolute right-4 max-xs:bottom-[11px] top-1/2 -translate-y-1/2',
                  rightIconClass
                )}
              />
            </button>
          )}

          {checkIcon && !errorMessage && checkIcon}
        </div>
      </div>
      {errorMessage && (
        <div className="text-sm text-red flex mt-1 text-[0.75rem] leading-4">
          {errorIcon && (
            <Image alt="right-action" src={errorIcon} width={14} height={14} className="mr-1" />
          )}
          {errorMessage}
        </div>
      )}
    </>
  );
});

export default Input;
