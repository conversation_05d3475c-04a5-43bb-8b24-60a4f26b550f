'use client';

import React from 'react';

import classNames from 'classnames';
import CheckCardIcon from 'components/Icons/CheckCardIcon';

interface ICheckboxProps {
  size?: number;
  containerClassName?: string;
  className?: string;
  labelClassName?: string;
  isSuffix?: boolean;
  isError?: boolean;
  onClick?: (checkId: number) => void;
  checkBoxItem: { id: number; label: string; isChecked: boolean; isError?: boolean };
  questionType: number; // Add this prop to handle different question types
}

const CheckboxCard: React.FunctionComponent<ICheckboxProps> = ({
  size = 22,
  checkBoxItem,
  containerClassName,
  labelClassName,
  isSuffix = false,
  onClick, // Add this prop to handle different question types
}) => {
  const handleClick = () => {
    if (onClick) onClick(checkBoxItem.id);
  };

  return (
    <div
      className={classNames(
        'inline-flex items-center justify-between relative rounded-[5px] h-12 p-3 cursor-pointer',
        containerClassName,
        {
          'shadow-[0px_1px_4px_0px_rgba(0,0,0,0.09)] bg-bg-general': checkBoxItem.isChecked,
          'bg-bg-box': !checkBoxItem.isChecked,
        }
      )}
      onClick={handleClick}
    >
      {!isSuffix ? (
        <span className={classNames('pr-2 text-color-major', labelClassName)}>
          {checkBoxItem.label}
        </span>
      ) : null}
      {checkBoxItem.isChecked ? (
        <CheckCardIcon
          className={classNames('w-[22px] h-[22px]', {
            'fill-red': checkBoxItem.isError,
            'fill-primary-100': !checkBoxItem.isError,
          })}
        />
      ) : (
        <div className="border rounded-full" style={{ height: size, width: size }} />
      )}
      {isSuffix ? (
        <span className={classNames('pl-2 text-color-major', labelClassName)}>
          {checkBoxItem.label}
        </span>
      ) : null}
    </div>
  );
};

export default CheckboxCard;
