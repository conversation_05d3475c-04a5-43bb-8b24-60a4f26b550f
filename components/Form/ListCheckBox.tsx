'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';

import OnLoading from 'components/ServicePopup/OnLoading';
import useOnBoarding from 'hooks/Ent/useOnBoarding';
import OnboardingStoreProps from 'store/onboarding';

import CheckboxCard from './CheckboxCard';

interface Answer {
  id: number;
  content: string;
}

const ListCheckBox = ({ answers }: { answers: any }) => {
  const { questionId, setAnswerReqs, answerReqs, questionType } = OnboardingStoreProps();
  // const { saveAnswers, cancelAnswers } = useOnBoarding({});
  // console.log(77775577);
  // console.log('questionId', questionId);
  const { quizzSaveAnswers, isValidatingGetSaveAnswer, cancelAnswers, saveAnswers } = useOnBoarding(
    {
      quest_id: questionId,
    }
  );
  // console.log('quizzSaveAnswers', quizzSaveAnswers);
  // Khởi tạo danh sách câu trả lời ban đầu với trạng thái isChecked dựa trên answerReqs
  const [checkList, setChecklist] = useState(() => {
    const answerData = answerReqs[questionId];
    let savedAnswers: Answer[] = [];

    if (Array.isArray(answerData)) {
      savedAnswers = answerData as Answer[];
    } else if (answerData) {
      savedAnswers = [answerData as Answer];
    }

    return answers.map((answer: any) => ({
      id: answer.id,
      label: answer.title,
      isChecked: savedAnswers.some((savedAnswer) => savedAnswer.id === answer.id),
      isError: answer.isError || false,
    }));
  });

  // Tạo cờ để ngăn việc cập nhật checkList trong handleOnChange
  const isHandleOnChangeRef = useRef(false);

  // Cập nhật danh sách khi questionId hoặc answers thay đổi
  useEffect(() => {
    if (isHandleOnChangeRef.current) {
      isHandleOnChangeRef.current = false;
      return;
    }

    const answerData = answerReqs[questionId];
    let savedAnswers: Answer[] = [];

    if (Array.isArray(answerData)) {
      savedAnswers = answerData as Answer[];
    } else if (answerData) {
      savedAnswers = [answerData as Answer];
    }

    let updatedCheckList = answers.map((answer: any) => ({
      id: answer.id,
      label: answer.title,
      isChecked: savedAnswers.some((savedAnswer) => savedAnswer.id === answer.id),
      isError: answer.isError || false,
    }));

    if (quizzSaveAnswers && quizzSaveAnswers.length > 0) {
      updatedCheckList = updatedCheckList.map((answer: any) => {
        const isChecked = quizzSaveAnswers.some(
          (savedAnswer: any) => savedAnswer.answer_id === answer.id
        );
        return {
          ...answer,
          isChecked: isChecked || answer.isChecked,
        };
      });
    }

    setChecklist(updatedCheckList);
  }, [answers, questionId, quizzSaveAnswers, answerReqs]);

  const handleOnChange = useCallback(
    async (checkId: number, checkLabel: string) => {
      isHandleOnChangeRef.current = true;

      const updatedList = checkList.map((item: any) =>
        item.id === checkId
          ? { ...item, isChecked: questionType == 8 ? !item.isChecked : true }
          : { ...item, isChecked: questionType == 8 ? item.isChecked : false }
      );

      // Cập nhật UI ngay lập tức
      setChecklist(updatedList);

      const selectedAnswers = updatedList
        .filter((item: any) => item.isChecked)
        .map((item: any) => ({
          id: item.id,
          content: item.label,
        }));

      setAnswerReqs((prevAnswerReqs) => ({
        ...prevAnswerReqs,
        [questionId]: selectedAnswers,
      }));

      // Xử lý API gọi sau
      try {
        if (questionType == 8) {
          const selectedItem = updatedList.find((item: any) => item.id === checkId);
          if (selectedItem?.isChecked) {
            const response = await saveAnswers({
              question_id: questionId,
              quiz_id: 1,
              answer_id: checkId,
              content: checkLabel,
            });
            if (!response.success) {
              console.error('Error saving answer:', response.message);
            }
          } else {
            await cancelAnswers({ question_id: questionId, answer_id: checkId });
          }
        } else {
          // Cho lựa chọn đơn
          const previouslyCheckedItem = checkList.find(
            (item: any) => item.isChecked && item.id !== checkId
          );
          if (previouslyCheckedItem) {
            await cancelAnswers({
              question_id: questionId,
              answer_id: previouslyCheckedItem.id,
            });
          }

          const response = await saveAnswers({
            question_id: questionId,
            quiz_id: 1,
            answer_id: checkId,
            content: checkLabel,
          });
          if (!response.success) {
            console.error('Error saving answer:', response.message);
          }
        }
      } catch (error) {
        console.error('Error processing the answer:', error);
      }
    },
    [checkList, questionId, questionType, saveAnswers, cancelAnswers, setAnswerReqs]
  );

  return isValidatingGetSaveAnswer ? (
    <OnLoading />
  ) : (
    <div className="grid grid-cols-2 gap-4">
      {checkList.map((item: any) => (
        <CheckboxCard
          key={item.id}
          onClick={() => handleOnChange(item.id, item.label)}
          containerClassName="w-full"
          checkBoxItem={item}
          questionType={questionType}
        />
      ))}
    </div>
  );
};

export default ListCheckBox;
