'use client';

import NextImage from 'next/image';

import { Image as UIImage, extendVariants } from '@heroui/react';

const ImageVariants = extendVariants(UIImage, {
  defaultVariants: {
    classNames: {
      // @ts-ignore
      wrapper:
        'w-full h-full border-none flex-1 rounded-none bg-no-repeat bg-center bg-contain flex items-center justify-center !max-w-full',
      img: 'rounded-none',
    },
  },
});

const NextImageWithFallback = (props: any) => {
  const { src, objectFit, width, height, fallbackSrc = '/images/placeholder.png', ...rest } = props;

  return (
    <ImageVariants
      as={NextImage}
      src={!src || src === '' ? fallbackSrc : src}
      width={width}
      height={height}
      {...rest}
      fallbackSrc={fallbackSrc}
      sizes="(max-width: 100%) 100vw, (max-width: 100%) 50vw, 33vw"
      style={{
        objectFit: objectFit || 'contain',
      }}
    />
  );
};

export default NextImageWithFallback;
