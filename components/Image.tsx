'use client';

import NextImage from 'next/image';

import { Image as UIImage, extendVariants } from '@heroui/react';

const ImageVariants = extendVariants(UIImage, {
  defaultVariants: {
    classNames: {
      // @ts-ignore
      wrapper: 'bg-bg-box border-none rounded-small bg-center bg-no-repeat',
    },
  },
});

const Image = (props) => {
  const { src } = props;
  if (!src || src === '') return <></>;
  return <ImageVariants as={NextImage} fallbackSrc={'/images/placeholder.jpeg'} {...props} />;
};
export default Image;
