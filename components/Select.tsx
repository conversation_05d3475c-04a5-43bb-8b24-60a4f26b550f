'use client';

import { ReactNode } from 'react';

import { OptionItem } from '@/types/component';
import {
  Select as NextUiSelect,
  SelectItem,
  SelectProps,
  SelectSection,
  extendVariants,
} from '@heroui/react';
import classNames from 'classnames';
import { map } from 'lodash';

const SelectStyle = extendVariants(NextUiSelect, {
  variants: {
    // <- modify/add variants
    color: {
      default: {
        base: 'bg-transparent',
        trigger: 'border border-color-border bg-bg-box hover:bg-bg-box min-h-7 h-auto',
        innerWrapper: 'group-data-[has-label=true]:pt-0',
        label: 'hidden',
        popoverContent: 'rounded-sm',
        listbox: '',
      },
    },
    isDisabled: {},
    size: {},
    base: '',
  },
  defaultVariants: {
    color: 'default',
    size: 'md',
    rounded: 'sm',
  },
  compoundVariants: [],
});
type CustomSelectProps = Omit<SelectProps, 'children' | 'isRequired'> & {
  sectionLabel?: string;
  itemIcon?: ReactNode | string;
  itemClassName?: string;
  children?: ReactNode;
  items: Iterable<OptionItem>;
  value?: string;
};

// export {SelectStyle as default, type SelectProps, SelectItem, SelectSection};

const Select = (props: CustomSelectProps) => {
  const { items, itemIcon, sectionLabel, itemClassName, size, value = 'id' } = props;
  let itemSizeClass = '';
  if (size && size === 'sm') {
    itemSizeClass = 'py-1';
  }
  const itemsArray = Array.from(items);
  return (
    // @ts-ignore
    <SelectStyle
      scrollShadowProps={{
        isEnabled: false,
      }}
      listboxProps={{
        classNames: {
          list: '',
        },
        itemClasses: {
          base: `rounded-sm data-[hover=true]:!bg-bg-box focus:!bg-bg-box ${itemSizeClass}`,
        },
      }}
      popoverProps={{
        classNames: {
          base: 'bg-bg-general rounded-sm',
          content: 'px-0 rounded-sm',
        },
      }}
      {...props}
    >
      {sectionLabel && sectionLabel != '' ? (
        <SelectSection title={sectionLabel}>
          {map(itemsArray, (item) => (
            <SelectItem key={item[value]} textValue={item.title}>
              <div className={classNames('flex items-center gap-x-2', itemClassName)}>
                {itemIcon ? itemIcon : null}
                <span className={'whitespace-nowrap'}>{item.title}</span>
              </div>
            </SelectItem>
          ))}
        </SelectSection>
      ) : (
        map(itemsArray, (item) => (
          <SelectItem key={item[value]} textValue={item.title}>
            <span className={classNames('flex items-center gap-x-2', itemClassName)}>
              {itemIcon ? itemIcon : null}
              <span className={'whitespace-nowrap'}>{item.title}</span>
            </span>
          </SelectItem>
        ))
      )}
    </SelectStyle>
  );
};

export default Select;
