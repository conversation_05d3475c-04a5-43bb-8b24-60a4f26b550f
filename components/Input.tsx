'use client';

import { Input as NextUiInput, extendVariants } from '@heroui/react';

const Input = extendVariants(NextUiInput, {
  variants: {
    // <- modify/add variants
    color: {},
    size: {
      xs: {
        inputWrapper: 'h-6 shadow-none min-h-6 px-1',
        input: 'text-tiny',
      },
      sm: {
        inputWrapper:
          'h-7 autofill:shadow-none group-data-[hover=true]:bg-bg-box min-h-7 border-0 bg-bg-box focus-within:bg-bg-box hover:bg-bg-box',
        input: '!text-small',
      },
      md: {
        inputWrapper:
          'h-9 autofill:shadow-none group-data-[hover=true]:bg-bg-box min-h-9 border-0 bg-bg-box focus-within:bg-bg-box hover:bg-bg-box',
        input: 'text-small',
      },
      xl: {
        inputWrapper: 'h-14 min-h-14',
        input: 'text-medium',
      },
    },
    radius: {
      xs: {
        inputWrapper: 'rounded',
      },
      sm: {
        inputWrapper: 'rounded-[4px]',
      },
    },
    textSize: {
      base: {
        input: 'text-base',
      },
    },
    removeLabel: {
      true: {
        label: 'hidden',
      },
      false: {},
    },
  },
  defaultVariants: {
    color: 'default',
    textSize: 'base',
    size: 'md',
    removeLabel: true,
  },
});
export default Input;
