'use client';

import { useRef } from 'react';

import dynamic from 'next/dynamic';

import classNames from 'classnames';

import loadingData from '@/components/Icons/loading-animation.json';

import { useMounted } from '@/hooks/common/useMounted';

const DynamicLottie = dynamic(() => import('lottie-react'), { ssr: false });

const LoadingPage = ({
  size,
  className = '',
  containerClassName = '',
}: {
  size?: string;
  className?: string;
  containerClassName?: string;
}) => {
  const loadingSize = useRef('');
  const mounted = useMounted();
  switch (size) {
    case 'sm':
      loadingSize.current = 'w-24';
      break;
    case 'md':
      loadingSize.current = 'w-32';
      break;
    case 'lg':
      loadingSize.current = 'w-48';
      break;
    case 'xl':
      loadingSize.current = 'w-64';
      break;
    case '2xl':
      loadingSize.current = 'w-128';
      break;
    case '3xl':
      loadingSize.current = 'w-256';
      break;
    default:
      loadingSize.current = 'w-16';
      break;
  }
  if (!mounted) return <></>;
  return (
    <div
      className={classNames(
        'flex h-screen w-screen items-center justify-center fixed z-[9999] top-0 left-0 bg-bg-general bg-opacity-75',
        containerClassName
      )}
    >
      <div className={classNames(loadingSize.current, className)}>
        <DynamicLottie animationData={loadingData} loop={true} />
      </div>
    </div>
  );
};
export default LoadingPage;
