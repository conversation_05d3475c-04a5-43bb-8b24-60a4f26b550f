'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import fetchAudioAction from '@/actions/fetchAudio';
import CategoryEnum from '@/configs/CategoryEnum';
import useCategoryStore from '@/store/category';
import { CategoryEntity } from '@/types/model';
import { useTheme } from 'next-themes';
import { useEventListener } from 'usehooks-ts';
import WaveSurfer from 'wavesurfer.js';

const WavePlayer = (props) => {
  const { categories } = useCategoryStore();
  const containerRef = useRef<HTMLDivElement | null>(null);
  const wavesurfer = useRef<WaveSurfer | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [blobUrl, setBlobUrl] = useState<string>('');

  const { theme } = useTheme();
  const computedWaveProps = useMemo(() => {
    const themesConfigList: CategoryEntity[] =
      categories?.filter((item: CategoryEntity) => item.parent_id === CategoryEnum.THEME_LIST) ||
      [];
    const activeTheme = themesConfigList?.find((themeItem) => themeItem.keyx === theme);
    const colorMajor = activeTheme?.items?.find((color) => color.keyx === '--color-major');
    const colorMinor = activeTheme?.items?.find((color) => color.keyx === '--color-minor');
    const waveProps = { ...props };
    delete waveProps.url;
    return {
      ...waveProps,
      height: props.height ?? 40,
      normalize: true,
      barWidth: 1,
      barGap: 4,
      waveColor: colorMajor ? colorMajor.valuex : '#ccc',
      progressColor: colorMinor ? colorMinor.valuex : '#ff0',
    };
  }, [props, theme]);

  useEffect(() => {
    const handleError = (error) => {
      console.error('WaveSurfer load error:', error);
    };

    if (wavesurfer.current) {
      wavesurfer.current.on('error', handleError);
      wavesurfer.current.destroy();
      wavesurfer.current = null;
    }

    return () => {
      wavesurfer.current?.un('error', handleError);
    };
  }, []);

  useEffect(() => {
    if (!props.url) return;
    fetchAudioAction(props.url).then((res) => {
      setBlobUrl(URL.createObjectURL(res));
    });
  }, [props.url]);

  useEffect(() => {
    if (!containerRef.current || blobUrl === '') return;
    try {
      wavesurfer.current = WaveSurfer.create({
        ...computedWaveProps,
        container: containerRef.current,
      });
      wavesurfer.current.load(blobUrl);
      wavesurfer.current.on('error', (error) => console.error('WaveSurfer load error:', error));
    } catch (error) {
      console.error('WaveSurfer initialization error:', error);
    }

    return () => {
      if (wavesurfer.current) {
        wavesurfer.current.destroy();
        wavesurfer.current = null;
      }
      if (blobUrl) URL.revokeObjectURL(blobUrl);
    };
  }, [blobUrl]);

  const onPlayClick = useCallback(() => {
    if (wavesurfer.current) {
      wavesurfer.current.isPlaying() ? wavesurfer.current.pause() : wavesurfer.current.play();
    }
  }, [wavesurfer]);

  // Handle space key press to play/pause
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Check if space key is pressed and target is not an input element
      event.preventDefault();
      onPlayClick();
    },
    [onPlayClick]
  );

  useEventListener(
    'keydown',
    (e) => {
      if (e.key === ' ' && props.isEnableKeyboard) {
        console.log('🚀 ~ useEventListener ~ e.key:', e.key);

        handleKeyDown(e);
      }
    },
    undefined,
    { capture: true }
  );

  useEffect(() => {
    if (!wavesurfer.current) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    wavesurfer.current.on('play', handlePlay);
    wavesurfer.current.on('pause', handlePause);

    return () => {
      wavesurfer.current?.un('play', handlePlay);
      wavesurfer.current?.un('pause', handlePause);
    };
  }, [wavesurfer.current]);

  const marginBottomClass = props.mb !== undefined ? `mb-${props.mb}` : 'mb-4';

  return (
    <div className={`flex items-center ${marginBottomClass}`}>
      <i
        onClick={onPlayClick}
        className={`mr-2 text-base text-color-minor ${isPlaying ? 'icon-stop' : 'icon-play'} `}
      />
      {/*@ts-ignore*/}
      <div ref={containerRef} className="w-full"></div>
    </div>
  );
};

export default WavePlayer;
