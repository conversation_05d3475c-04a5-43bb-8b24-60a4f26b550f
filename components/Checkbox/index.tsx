'use client';

import React, { memo } from 'react';

import Image from 'next/image';

import CheckedIcon from 'public/ent/icons/checked.svg';

interface ICheckboxProps {
  checked: boolean;
  size?: number;
  containerClassName?: string;
  className?: string;
}

const Checkbox: React.FunctionComponent<ICheckboxProps> = ({
  checked,
  size = 20,
  className,
  containerClassName,
}) => {
  return (
    <div className={containerClassName}>
      {checked ? (
        <Image src={CheckedIcon} className={className} width={size} height={size} alt={'checked'} />
      ) : (
        <div className="border rounded-[4px]" style={{ height: size, width: size }} />
      )}
    </div>
  );
};

export default memo(Checkbox);
