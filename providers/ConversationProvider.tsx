'use client';

import { ReactNode, createContext, useContext, useEffect, useMemo, useState } from 'react';

import { useParams, useRouter } from 'next/navigation';

import { EntRouters, LEARN_TAB, SENTENCE_GROUPS_TABS } from '@/configs';
import { AccessEnum } from '@/configs/CanAccess';
import { ConversationTypeEnum } from '@/configs/ConversationEnum';
import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { RoleEnum } from '@/configs/RoleEnum';
import { SentenceProcessEnum } from '@/configs/SentenceProcessEnum';
import { StatusEnum } from '@/configs/StatusEnum';
import { SentenceGroup } from '@/interfaces/sentence-groups.interface';
import useLearnStore from '@/store/learn';
import useSpeakingStore from '@/store/speaking';
import { Character, DocumentEntity, ParagraphEntity, SentenceEntity } from '@/types/model';
import checkAccess, { checkAccessOrOwner } from '@/utils/checkAccess';
import { useGlobalAudioPlayer } from 'react-use-audio-player';

import { useConversations } from '@/hooks/Ent/useConversations';
import { useDocuments } from '@/hooks/Ent/useDocuments';
import { useParagraph } from '@/hooks/Ent/useParagraph';
import { useSentenceGroups } from '@/hooks/Ent/useSentenceGroups';
import { useSession } from '@/hooks/useSession';

// Create context
interface ConversationContextValue {
  isUserValidToAccess: boolean;
  paragraph: ParagraphEntity | undefined;
  documents: DocumentEntity[] | undefined;
  activeDocument: DocumentEntity | undefined;
  isLoading: boolean;
  sentences: SentenceEntity[];
  characters: Character[];
  sentenceGroups: SentenceGroup[];
  isLoadingConversations: boolean;
  isLoadingSentenceGroup: boolean;
  activeTab: LearnTypeEnum;
  tabs: { id: LearnTypeEnum; title: string }[];
  setActiveTab: React.Dispatch<React.SetStateAction<LearnTypeEnum>>;
  listenCurrentId: number;
  paragraphs: ParagraphEntity[];
  isConfirmChangeExitPage: boolean;
  setIsConfirmChangeExitPage: React.Dispatch<React.SetStateAction<boolean>>;
}

const ConversationContext = createContext<ConversationContextValue | undefined>(undefined);

// Provider component
interface ConversationProviderProps {
  children: ReactNode;
}

export function ConversationProvider({ children }: ConversationProviderProps) {
  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();
  const { stop } = useGlobalAudioPlayer();

  const paragraphId = params.paragraph_id?.toString();

  // Initial setup and cleanup
  useEffect(() => {
    if (!paragraphId) router.back();

    // Set token on mount
    const token = params.token?.toString() || '';
    if (token) {
      const learnStore = useLearnStore.getState();
      learnStore.setExerciseToken(token);
    }

    return () => {
      stop();
      // Reset speaking store
      const speakingStore = useSpeakingStore.getState();
      speakingStore.setSentence(null);
      speakingStore.setTimestamp(Date.now());
      speakingStore.setSentenceProcess(SentenceProcessEnum.START);

      // Reset learn store
      // const learnStore = useLearnStore.getState();
      // learnStore.setSpeakings([]);
      // learnStore.setConversations([]);
      // learnStore.setSelectedSentence(null);
    };
  }, [params]);

  const [isConfirmChangeExitPage, setIsConfirmChangeExitPage] = useState(false);

  const { data: paragraph, isLoading: isLoadingParagraph } = useParagraph({
    keyx: paragraphId || '',
  });

  const [activeTab, setActiveTab] = useState<LearnTypeEnum>(
    checkAccessOrOwner(session, AccessEnum.LEARN_APPROVE, paragraph?.account_id)
      ? LearnTypeEnum.APPROVE
      : LearnTypeEnum.LISTEN
  );

  const tabs = useMemo(() => {
    const initTabs =
      paragraph?.item === ConversationTypeEnum.GALLERY ? SENTENCE_GROUPS_TABS : LEARN_TAB;
    if (checkAccessOrOwner(session, AccessEnum.LEARN_APPROVE, paragraph?.account_id)) {
      return [...initTabs, { id: LearnTypeEnum.APPROVE, title: 'Xem' }];
    }
    return initTabs;
  }, [paragraph]);

  useEffect(() => {
    if (paragraph && session) {
      const isApproved = paragraph.process_approve === StatusEnum.ON;
      const isAuthor = session?.member?.account_id === paragraph?.account_id;
      if (!isApproved && !isAuthor && !checkAccess(session, AccessEnum.LEARN_APPROVE)) {
        router.push(EntRouters.page404);
      }
    }
  }, [paragraph, session]);

  const { data: documents, isLoading: isLoadingDocuments } = useDocuments({
    course_id: paragraph?.course_id || 0,
  });

  const isUserValidToAccess = useMemo(
    () =>
      session?.user?.role?.list_access?.learn?.includes(LearnTypeEnum.APPROVE) ||
      session?.user?.role_id === RoleEnum.ROOT,
    [session]
  );

  const activeDocument =
    documents && documents.length > 0
      ? documents.find((doc) => doc.id === paragraph?.document_id)
      : undefined;

  const { data: sentenceResponse, isFetching: isLoadingConversations } = useConversations({
    paragraphId: paragraph?.id || 0,
    documentId: activeDocument?.id || 0,
    courseId: paragraph?.course_id || 0,
    type: activeTab,
  } );
  
  const sentences = sentenceResponse?.sentences || [];
  const characters = sentenceResponse?.characters || [];
  const listenCurrentId =
    activeTab === LearnTypeEnum.LISTEN ? sentenceResponse?.listen_sentence_current_id || 0 : 0;

  const { data: sentenceGroupData, isLoading: isLoadingSentenceGroup } = useSentenceGroups({
    paragraph_id: paragraph?.id || 0,
    item: paragraph?.item,
  });

  const sentenceGroups = sentenceGroupData?.sentence_groups || [];

  // useEffect(() => {
  //   const handleBeforeUnload = (e: BeforeUnloadEvent) => {
  //     if (activeTab === LearnTypeEnum.LISTEN || activeTab === LearnTypeEnum.SPEAKING) {
  //       e.preventDefault();
  //       setIsConfirmChangeExitPage(true);
  //     }
  //   };

  //   window.addEventListener('beforeunload', handleBeforeUnload);

  //   return () => {
  //     window.removeEventListener('beforeunload', handleBeforeUnload);
  //   };
  // }, [activeTab]);

  return (
    <ConversationContext.Provider
      value={{
        sentences,
        characters,
        sentenceGroups,
        isUserValidToAccess,
        paragraph,
        documents,
        activeDocument,
        isLoading: isLoadingParagraph || isLoadingDocuments,
        isLoadingConversations,
        isLoadingSentenceGroup,
        activeTab,
        tabs,
        setActiveTab,
        listenCurrentId,
        paragraphs: activeDocument?.paragraphs || [],
        isConfirmChangeExitPage,
        setIsConfirmChangeExitPage,
      }}
    >
      {children}
    </ConversationContext.Provider>
  );
}

// Custom hook for using the context
export function useConversationContext() {
  const context = useContext(ConversationContext);

  if (context === undefined) {
    throw new Error('useConversation must be used within a ConversationProvider');
  }

  return context;
}
