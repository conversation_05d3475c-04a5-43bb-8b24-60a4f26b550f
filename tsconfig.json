{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "noUnusedLocals": true, "paths": {"@/*": ["./*"]}, "plugins": [{"name": "next"}], "strictNullChecks": true}, "include": ["**/*.mp4", "**/*.ts", "**/*.tsx", "next-env.d.ts", "next.config.js", ".next/types/**/*.ts", "nextauth.d.ts", "process.d.ts", "tailwind.config.js", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next"]}