export enum QuestionDisplay {
  NORMAL = 1,
  SORT = 2,
}

export enum ExerciseOption {
  NOT_SELECTED = 'not_selected',
  SELECTED = 'selected',
  CORRECT = 'correct',
  WRONG = 'wrong',
}

export enum QuestionStatus {
  DEFAULT = 'default',
  CORRECT = 'correct',
  WRONG = 'wrong',
}

export enum QuestionDescription {
  WORD = 'word',
  IMAGE = 'image',
  SOUND = 'sound',
  MIXED = 'mixed',
  SENTENCE = 'sentence',
  PASSAGE = 'passage',
}

export enum AnswerFormat {
  WORD = 'word',
  IMAGE = 'image',
  SOUND = 'sound',
  SORT = 'sort',
  SENTENCE = 'sentence',
  PASSAGE = 'passage',
}

export const QUESTION_CONFIGS = Object.freeze({
  '11_1': {
    title_en: 'Find the sound that matches the following word',
    title_vi: 'Tìm âm thanh phù hợp với từ sau',
    question_description: QuestionDescription.WORD,
    answer_format: AnswerFormat.SOUND,
  },
  '11_2': {
    title_en: 'Find the picture that matches the following word',
    title_vi: 'Tìm hình ảnh phù hợp với từ sau',
    question_description: QuestionDescription.WORD,
    answer_format: AnswerFormat.IMAGE,
  },
  '11_3': {
    title_en: 'Find the picture that matches the following sound',
    title_vi: 'Tìm hình ảnh phù hợp với âm thanh sau',
    question_description: QuestionDescription.SOUND,
    answer_format: AnswerFormat.IMAGE,
  },
  '11_4': {
    title_en: 'Find the word that matches the following sound',
    title_vi: 'Tìm từ phù hợp với âm thanh sau',
    question_description: QuestionDescription.SOUND,
    answer_format: AnswerFormat.WORD,
  },
  '11_5': {
    title_en: 'Find the word that matches the following picture',
    title_vi: 'Tìm từ phù hợp với hình ảnh sau',
    question_description: QuestionDescription.IMAGE,
    answer_format: AnswerFormat.WORD,
  },
  '11_6': {
    title_en: 'Find the sound that matches the following picture',
    title_vi: 'Tìm âm thanh phù hợp với hình ảnh sau',
    question_description: QuestionDescription.IMAGE,
    answer_format: AnswerFormat.SOUND,
  },
  '12_1': {
    title_en: 'Arrange the letters to form words that match the following sound',
    title_vi: 'Sắp xếp các ký tự thành từ phù hợp với âm thanh sau',
    question_description: QuestionDescription.SOUND,
    answer_format: AnswerFormat.SORT,
  },
  '12_2': {
    title_en: 'Arrange the letters to form words that match the following picture',
    title_vi: 'Sắp xếp các ký tự thành từ phù hợp với hình ảnh sau',
    question_description: QuestionDescription.IMAGE,
    answer_format: AnswerFormat.SORT,
  },
  '13_1': {
    title_en: 'Which word belongs to the same category as the highlight word in the sentence',
    title_vi: 'Từ nào thuộc cùng thể loại với từ bôi đậm trong câu',
    question_description: QuestionDescription.SENTENCE,
    answer_format: AnswerFormat.WORD,
  },
  '21_1': {
    title_en: 'Which word is synonymous with the highlight word in the sentence',
    title_vi: 'Từ nào đồng nghĩa với từ bôi đậm trong câu',
    question_description: QuestionDescription.SENTENCE,
    answer_format: AnswerFormat.WORD,
  },
  '22_1': {
    title_en: 'Which word is antonym with the highlight word in the sentence',
    title_vi: 'Từ nào trái nghĩa với từ bôi đậm trong câu',
    question_description: QuestionDescription.SENTENCE,
    answer_format: AnswerFormat.WORD,
  },
  '23_1': {
    title_en: 'Arrange the words into complete sentences',
    title_vi: 'Sắp xếp các từ thành câu hoàn chỉnh',
    question_description: QuestionDescription.MIXED,
    answer_format: AnswerFormat.SORT,
  },
  '24_1': {
    title_en: 'Choose the best word to complete the sentence.',
    title_vi: 'Chọn từ thích hợp nhất để hoàn thành câu',
    question_description: QuestionDescription.SENTENCE,
    answer_format: AnswerFormat.WORD,
  },
  '25_1': {
    title_en: 'Choose the correct form of the word to complete the sentence.',
    title_vi: 'Chọn dạng đúng của từ để hoàn thành câu',
    question_description: QuestionDescription.SENTENCE,
    answer_format: AnswerFormat.WORD,
  },
  '26_1': {
    title_en: 'Which sentence has the same meaning as the given sentence?',
    title_vi: 'Câu nào có cùng nghĩa với câu đã cho?',
    question_description: QuestionDescription.SENTENCE,
    answer_format: AnswerFormat.SENTENCE,
  },
  '27_1': {
    title_en: 'Choose the best response to the given sentence.',
    title_vi: 'Chọn câu trả lời đúng nhất cho câu cho sẵn.',
    question_description: QuestionDescription.SENTENCE,
    answer_format: AnswerFormat.SENTENCE,
  },
  '31_1': {
    title_en: 'What is the main idea of the passage?',
    title_vi: 'Ý chính của đoạn văn là gì?',
    question_description: QuestionDescription.PASSAGE,
    answer_format: AnswerFormat.SENTENCE,
  },
});

export enum AnswerType {
  WRONG = 1,
  CORRECT = 2,
}

export const IS_CORRECT = AnswerType.CORRECT;
