/**
 * Enum cho các loại quyền truy cập
 * Sử dụng để kiểm tra quyền truy cập trong ứng dụng
 */
export enum AccessEnum {
  // Quyền truy cập cho module LEARN
  LEARN_APPROVE = 'learn.approve',
  LEARN_EDIT = 'learn.edit',
  LEARN_DELETE = 'learn.delete',

  // Quyền truy cập cho module DOCUMENT
  DOCUMENT_CREATE = 'document.create',
  DOCUMENT_EDIT = 'document.edit',
  DOCUMENT_DELETE = 'document.delete',

  // Quyền truy cập cho module MEMBER
  MEMBER_VIEW = 'member.view',
  MEMBER_CREATE = 'member.create',
  MEMBER_EDIT = 'member.edit',
  MEMBER_DELETE = 'member.delete',

  // Quyền truy cập cho module GROUP
  GROUP_CREATE = 'group.create',
  GROUP_EDIT = 'group.edit',
  GROUP_DELETE = 'group.delete',
}

/**
 * <PERSON><PERSON><PERSON> trúc quyền truy cập theo module
 * Sử dụng để lưu trữ trong database và kiểm tra quyền
 */
const CanAccess = {
  LEARN: {
    APPROVE: 'approve',
    EDIT: 'edit',
    DELETE: 'delete',
  },
  DOCUMENT: {
    CREATE: 'create',
    EDIT: 'edit',
    DELETE: 'delete',
  },
  MEMBER: {
    VIEW: 'view',
    CREATE: 'create',
    EDIT: 'edit',
    DELETE: 'delete',
  },
  GROUP: {
    CREATE: 'create',
    EDIT: 'edit',
    DELETE: 'delete',
  },
};

export default CanAccess;
