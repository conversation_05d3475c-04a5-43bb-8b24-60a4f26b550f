import { SIDEBAR_MAIN_PARENT_ID, SIDEBAR_SETTING_PARENT_ID } from '@/configs';
import { QuestionStatus } from '@/configs/Exercise';
import { SidebarEnum } from '@/configs/SidebarEnum';
import { BASE_GALLERY_IMAGE_URL } from '@/constant';
import { CategoryEntity } from '@/types/model';
import { MenuItem } from '@/types/sidebar';
import EntRouters from 'configs/EntRouters';
import moment from 'moment';
import { SearchResultItem } from 'types/hooks';
import { useEventListener } from 'usehooks-ts';

export const createRouterLearn = (item: SearchResultItem) => {
  if (item.item === 'document') {
    return `${EntRouters.learn_redirect}/${item.id}`;
  } else if (item.item === 'course') {
    return `${EntRouters.course}/${item.course_id}`;
  } else {
    return `${EntRouters.learn}/${item.keyx}`;
  }
};

export const decodeHtmlEntities = (text: string) => {
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
};
export const getResizeImageUrl = ({
  keyx,
  size,
  created_at,
  format,
}: {
  keyx: string;
  size?: number;
  created_at: number;
  format: string;
}): string => {
  const createdTimeFormat = moment(created_at * 1000).format('YYYY/MM/DD');
  if (size) {
    return `${BASE_GALLERY_IMAGE_URL}${createdTimeFormat}/${size}/${created_at}_${keyx}.${format}`;
  } else {
    return `${BASE_GALLERY_IMAGE_URL}${createdTimeFormat}/${created_at}_${keyx}.${format}`;
  }
};

const generateTreeList = (lists: MenuItem[]) => {
  // Create a map to store posts by their id for fast lookup
  const categoryMap = new Map();
  for (const category of lists) {
    category.items = [];
    categoryMap.set(category.id, category);
  }

  // Initialize the root of the tree
  const root: MenuItem[] = [];

  // Iterate through the categorys to build the tree
  for (const category of lists) {
    if (category.parent_id !== null && categoryMap.has(category.parent_id)) {
      const parentPost = categoryMap.get(category.parent_id);
      parentPost.items.push(category);
    } else {
      // If a category has no parent, it's a root level category
      root.push(category);
    }
  }

  return root;
};

export const getSidebar = (_categories: CategoryEntity[], sidebarType: number) => {
  const treeList = generateTreeList(_categories ?? []);
  if (sidebarType === SidebarEnum.MAIN) {
    return treeList.find((item: CategoryEntity) => item.id === SIDEBAR_MAIN_PARENT_ID);
  }
  if (sidebarType === SidebarEnum.SETTING) {
    return treeList.find((item: CategoryEntity) => item.id === SIDEBAR_SETTING_PARENT_ID);
  }
};

export function verifyPassword(pw) {
  if (/\s/.test(pw)) return false; // Kiểm tra khoảng trắng

  let hasNumber = false;
  let hasUpper = false;
  let hasLower = false;
  let hasSpecial = false;

  for (const char of pw) {
    if (!isNaN(char)) {
      hasNumber = true;
    } else if (char === char.toUpperCase() && char !== char.toLowerCase()) {
      hasUpper = true;
    } else if (char === char.toLowerCase() && char !== char.toUpperCase()) {
      hasLower = true;
    } else if (/[\W_]/.test(char)) {
      hasSpecial = true;
    } else {
      return false;
    }
  }

  return hasNumber && hasUpper && hasLower && hasSpecial && pw.length >= 6 && pw.length <= 255;
}

export const useQuestionKeyboardHandler = (
  questionStatus: QuestionStatus | null,
  handleNextQuestion: () => void,
  handleCheckAnswer: () => Promise<void>
) => {
  const handleKeyboardPress = async (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (questionStatus === QuestionStatus.CORRECT || questionStatus === QuestionStatus.WRONG) {
        handleNextQuestion();
      } else {
        await handleCheckAnswer();
      }
    }
  };

  useEventListener('keydown', (e) => e.key === 'Enter' && handleKeyboardPress(e), undefined, {
    capture: true,
  });
};
