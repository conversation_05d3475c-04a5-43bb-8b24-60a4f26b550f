import { SentenceEntity } from '@/types/model';

export const groupSentencesByKey = (
  sentences: Array<SentenceEntity>,
  key: keyof SentenceEntity
): Array<Array<SentenceEntity>> => {
  const sortedSentences = sortSentencesByPosition(sentences);
  // Create a map to store sentences by their key value
  const groupMap = new Map<any, Array<SentenceEntity>>();

  // Group sentences by the specified key
  sortedSentences.forEach((sentence) => {
    const keyValue = sentence[key];
    if (!groupMap.has(keyValue)) {
      groupMap.set(keyValue, []);
    }
    groupMap.get(keyValue)?.push(sentence);
  });

  return Array.from(groupMap.values());
};

export const sortSentencesByPosition = (
  sentences: Array<SentenceEntity>
): Array<SentenceEntity> => {
  return sentences.sort((a, b) => {
    const posA = typeof a.position === 'number' ? a.position : 0;
    const posB = typeof b.position === 'number' ? b.position : 0;
    return posA - posB;
  });
};

export const groupConversationSentences = (
  sentences: Array<SentenceEntity>
): Array<Array<SentenceEntity>> => {
  // First group by character_id
  const characterGroups = groupSentencesByKey(sentences, 'character_id');

  // For each character group, further group by the 'group' field
  return characterGroups.flatMap((charGroup) => {
    // Create a map to store sentences by group
    const groupMap = new Map<number, Array<SentenceEntity>>();

    // Group sentences by the 'group' field
    charGroup.forEach((sentence) => {
      // Handle undefined group by treating it as 0
      const groupKey = typeof sentence.group === 'number' ? sentence.group : 0;
      if (!groupMap.has(groupKey)) {
        groupMap.set(groupKey, []);
      }
      groupMap.get(groupKey)?.push(sentence);
    });

    // Sort groups by their key values (numerically)
    const sortedGroups = Array.from(groupMap.entries())
      .sort((a, b) => a[0] - b[0])
      .map(([, group]) => group);

    // Sort sentences within each group by position
    return sortedGroups;
  });
};
