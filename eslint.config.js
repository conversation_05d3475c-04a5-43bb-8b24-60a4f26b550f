import { FlatCompat } from '@eslint/eslintrc';
import next from '@next/eslint-plugin-next';
import typescript from '@typescript-eslint/eslint-plugin';
import prettier from 'eslint-plugin-prettier';
import react from 'eslint-plugin-react';

const compat = new FlatCompat({
  extends: 'eslint:recommended',
  plugins: ['@typescript-eslint', 'react', 'prettier', '@next/next'],
  recommendedConfig: {
    '@typescript-eslint': 'plugin:@typescript-eslint/recommended',
    react: 'plugin:react/recommended',
    prettier: 'plugin:prettier/recommended',
    '@next/next': 'plugin:@next/next/recommended',
  },
});
export default [
  ...compat.extends(
    'plugin:@next/next/recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:prettier/recommended'
  ),
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    plugins: {
      '@typescript-eslint': typescript,
      react: react,
      prettier: prettier,
      '@next/next': next,
    },
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/ban-ts-comment': [
        'error',
        {
          'ts-expect-error': false,
          'ts-ignore': false,
        },
      ],
      'react/react-in-jsx-scope': 'off',
      'react/jsx-uses-react': 'off',
      'no-useless-catch': 'off',
      'prettier/prettier': 'off',
      'react/prop-types': 'off',
      'no-undef': 'off',
      'linebreak-style': 'off',
      'no-console': 'off',
      quotes: ['error', 'single'],
      semi: ['error', 'always'],
      'no-extra-semi': 'error',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'error',
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
];
