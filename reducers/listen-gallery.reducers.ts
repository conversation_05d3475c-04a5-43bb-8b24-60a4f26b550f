import { SentenceGroup } from '@/interfaces/sentence-groups.interface';
import { Character } from '@/types/model';
import { SentenceEntity } from '@/types/model';

export interface IListenGalleryState {
  isStartedLearn: boolean;
  isFinishLearn: boolean;
  isEndSentences: boolean;
  isLastSentence: boolean;
  activeCharacter: Character | null;
  activeSentenceGroup: SentenceGroup | null;
  currentSentences: SentenceEntity[];
  nextSentences: SentenceEntity[];
}

export enum ListenGalleryActionType {
  SET_STARTED_LEARN = 'SET_STARTED_LEARN',
  SET_FINISH_LEARN = 'SET_FINISH_LEARN',
  SET_END_SENTENCES = 'SET_END_SENTENCES',
  SET_LAST_SENTENCE = 'SET_LAST_SENTENCE',
  SET_ACTIVE_CHARACTER = 'SET_ACTIVE_CHARACTER',
  SET_ACTIVE_SENTENCE_GROUP = 'SET_ACTIVE_SENTENCE_GROUP',
  SET_CURRENT_SENTENCES = 'SET_CURRENT_SENTENCES',
  SET_NEXT_SENTENCES = 'SET_NEXT_SENTENCES',
  RESET_CANCEL_AUTO_READING = 'RESET_CANCEL_AUTO_READING',
  LEARN_AGAIN = 'LEARN_AGAIN',
  FINISH_CONVERSATION = 'FINISH_CONVERSATION',
  INITIALIZE = 'INITIALIZE',
  NEXT_SENTENCE = 'NEXT_SENTENCE',
  PREV_SENTENCE = 'PREV_SENTENCE',
  CLICK_GALLERY_ITEM = 'CLICK_GALLERY_ITEM',
}

export type ListenGalleryAction =
  | { type: ListenGalleryActionType.SET_STARTED_LEARN; payload: boolean }
  | { type: ListenGalleryActionType.SET_FINISH_LEARN; payload: boolean }
  | { type: ListenGalleryActionType.SET_END_SENTENCES; payload: boolean }
  | { type: ListenGalleryActionType.SET_ACTIVE_CHARACTER; payload: Character | null }
  | { type: ListenGalleryActionType.SET_ACTIVE_SENTENCE_GROUP; payload: SentenceGroup | null }
  | { type: ListenGalleryActionType.SET_CURRENT_SENTENCES; payload: SentenceEntity[] }
  | { type: ListenGalleryActionType.SET_NEXT_SENTENCES; payload: SentenceEntity[] }
  | { type: ListenGalleryActionType.RESET_CANCEL_AUTO_READING; payload: boolean }
  | { type: ListenGalleryActionType.LEARN_AGAIN; payload: SentenceEntity[] }
  | { type: ListenGalleryActionType.FINISH_CONVERSATION }
  | { type: ListenGalleryActionType.SET_LAST_SENTENCE; payload: boolean }
  | {
      type: ListenGalleryActionType.INITIALIZE;
      payload: {
        sentences: SentenceEntity[];
        sortedSentenceGroups: SentenceGroup[];
        listenCurrentId?: number;
      };
    }
  | {
      type: ListenGalleryActionType.NEXT_SENTENCE;
      payload: {
        sortedSentences: SentenceEntity[];
        sortedSentenceGroups: SentenceGroup[];
      };
    }
  | {
      type: ListenGalleryActionType.PREV_SENTENCE;
      payload: {
        sortedSentences: SentenceEntity[];
        sortedSentenceGroups: SentenceGroup[];
        onSaveBackSentence: ({ sentence_id }: { sentence_id: number }) => void;
      };
    }
  | {
      type: ListenGalleryActionType.CLICK_GALLERY_ITEM;
      payload: { sentenceGroup: SentenceGroup; sortedSentences: SentenceEntity[] };
    };

export const listenGalleryReducer = (
  state: IListenGalleryState,
  action: ListenGalleryAction
): IListenGalleryState => {
  switch (action.type) {
    case ListenGalleryActionType.SET_STARTED_LEARN:
      return { ...state, isStartedLearn: action.payload };
    case ListenGalleryActionType.SET_FINISH_LEARN:
      return { ...state, isFinishLearn: action.payload };
    case ListenGalleryActionType.SET_END_SENTENCES:
      return { ...state, isEndSentences: action.payload };
    case ListenGalleryActionType.SET_ACTIVE_CHARACTER:
      return { ...state, activeCharacter: action.payload };
    case ListenGalleryActionType.SET_ACTIVE_SENTENCE_GROUP:
      return { ...state, activeSentenceGroup: action.payload };
    case ListenGalleryActionType.SET_CURRENT_SENTENCES:
      return { ...state, currentSentences: action.payload };
    case ListenGalleryActionType.SET_NEXT_SENTENCES:
      return { ...state, nextSentences: action.payload };
    case ListenGalleryActionType.FINISH_CONVERSATION:
      return { ...state, isFinishLearn: true };
    case ListenGalleryActionType.SET_LAST_SENTENCE:
      return { ...state, isLastSentence: action.payload };
    case ListenGalleryActionType.LEARN_AGAIN:
      return {
        ...state,
        isFinishLearn: false,
        isStartedLearn: true,
        isEndSentences: true,
        currentSentences: [],
        nextSentences: action.payload.length > 0 ? [action.payload[0]] : [],
        activeSentenceGroup: null,
      };
    case ListenGalleryActionType.INITIALIZE: {
      const { sentences, sortedSentenceGroups, listenCurrentId } = action.payload;

      if (listenCurrentId) {
        const currentSentenceIndex = sentences.findIndex(
          (sentence) => sentence.id === listenCurrentId
        );
        const currentSentenceGroup = sortedSentenceGroups.find(
          (group) => group.id === sentences[currentSentenceIndex]?.sentence_group_id
        );

        if (currentSentenceIndex > 0) {
          return {
            ...state,
            activeSentenceGroup: currentSentenceGroup || sortedSentenceGroups[0],
            currentSentences: [],
            nextSentences: [sentences[currentSentenceIndex]],
          };
        } else {
          return {
            ...state,
            activeSentenceGroup: currentSentenceGroup || sortedSentenceGroups[0],
            currentSentences: [],
            nextSentences: [sentences[0]],
          };
        }
      } else {
        return {
          ...state,
          currentSentences: [],
          nextSentences: sentences.length > 0 ? [sentences[0]] : [],
          activeSentenceGroup: sortedSentenceGroups[0],
        };
      }
    }
    case ListenGalleryActionType.NEXT_SENTENCE: {
      const { sortedSentences, sortedSentenceGroups } = action.payload;

      // Find current sentence index
      const currentSentenceId = state.nextSentences[0]?.id;
      if (!currentSentenceId) {
        
        // No current sentence, start from beginning
        return {
          ...state,
          currentSentences: [],
          nextSentences: sortedSentences.length > 0 ? [sortedSentences[0]] : [],
          activeSentenceGroup: sortedSentenceGroups[0] || null,
        };
      }

      const currentSentenceIndex = sortedSentences.findIndex((s) => s.id === currentSentenceId);
      const nextSentenceIndex = currentSentenceIndex + 1;

      // Check if we have a next sentence
      if (nextSentenceIndex < sortedSentences.length) {
        const nextSentence = sortedSentences[nextSentenceIndex];
        const nextSentenceGroup = sortedSentenceGroups.find(
          (group) => group.id === nextSentence.sentence_group_id
        );

        // Set isLastSentence flag if this is the last sentence

        return {
          ...state,
          currentSentences: state.nextSentences,
          nextSentences: [nextSentence],
          activeSentenceGroup: nextSentenceGroup || state.activeSentenceGroup,
          isEndSentences: true, // Reset to allow audio playing
        };
      } else {
        // We're at the end of sentences
        return {
          ...state,
          currentSentences: state.nextSentences,
          nextSentences: [],
          isEndSentences: true,
        };
      }
    }
    case ListenGalleryActionType.PREV_SENTENCE: {
      const { sortedSentences, sortedSentenceGroups, onSaveBackSentence } = action.payload;

      // If we're at the beginning or have no current sentences, do nothing
      if (!state.currentSentences.length && !state.nextSentences.length) {
        return state;
      }

      // Find the current position in the sequence
      const currentId =
        state.currentSentences.length > 0
          ? state.currentSentences[0].id
          : state.nextSentences.length > 0
            ? state.nextSentences[0].id
            : sortedSentences[0].id;

      const currentIndex = sortedSentences.findIndex((s) => s.id === currentId);

      // If we're at the first sentence or couldn't find current index, do nothing
      if (currentIndex <= 0) {
        return {
          ...state,
          currentSentences: [],
          nextSentences: [sortedSentences[0]],
          activeSentenceGroup: sortedSentenceGroups[0],
        };
      }

      // Get the previous sentence
      const prevIndex = currentIndex - 1;
      const prevSentence = prevIndex > 0 ? sortedSentences[prevIndex] : sortedSentences[0];
      // Find the sentence group for the previous sentence
      const prevSentenceGroup = sortedSentenceGroups.find(
        (group) => group.id === prevSentence.sentence_group_id
      );

      onSaveBackSentence({
        sentence_id: prevSentence.id,
      });

      return {
        ...state,
        currentSentences: [],
        nextSentences: [prevSentence],
        activeSentenceGroup: prevSentenceGroup || state.activeSentenceGroup,
        isEndSentences: true,
      };
    }
    case ListenGalleryActionType.CLICK_GALLERY_ITEM: {
      const { sentenceGroup, sortedSentences } = action.payload;
      const sentencesInGroup = sortedSentences.filter(
        (s) => s.sentence_group_id === sentenceGroup.id
      );

      if (sentencesInGroup.length) {
        const currentSentenceIndex = sortedSentences.findIndex(
          (s) => s.sentence_group_id === sentenceGroup.id
        );

        return {
          ...state,
          activeSentenceGroup: sentenceGroup,
          currentSentences: [],
          nextSentences: [sortedSentences[currentSentenceIndex]],
          isEndSentences: true,
        };
      }
      return state;
    }
    default:
      return state;
  }
};
