import { getUserLocale } from '@/services/locale';
import { getRequestConfig } from 'next-intl/server';

export default getRequestConfig(async () => {
  const locale = await getUserLocale();
  // let locale = await requestLocale;
  // if (!locale || !routing.locales.includes(locale)) {
  //   locale = routing.defaultLocale;
  // }
  return {
    locale,
    messages: (await import(`../language/${locale}.js`)).default,
  };
});
