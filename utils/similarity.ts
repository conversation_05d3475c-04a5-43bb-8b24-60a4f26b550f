import * as fuzzball from 'fuzzball';

/**
 * Calculates the similarity score between two strings using fuzzball.ratio.
 *
 * @param {string} content The first string.
 * @param {string} template The second string.
 * @returns {number} the similarity score (0-100).
 */
export const similarity = (content: string, template: string): number => {
  const lowerContent = content.toLowerCase();
  const lowerTemplate = template.toLowerCase();
  return fuzzball.ratio(lowerContent, lowerTemplate);
};
