import { formatDistanceToNow } from 'date-fns';
import * as Locales from 'date-fns/locale';

const getValidLocale = (locale: string) => {
  if (Locales[locale]) return Locales[locale];
  const matchedLocale = Object.keys(Locales).find((key) => key.startsWith(locale));
  return matchedLocale ? Locales[matchedLocale] : Locales['vi'];
};

export const formatDateDay = (date: number, locale = 'vi') => {
  const timestampInMilliseconds = date * 1000;
  const result = formatDistanceToNow(timestampInMilliseconds, {
    locale: getValidLocale(locale),
    addSuffix: true,
    includeSeconds: true,
  });
  return result.replace('khoảng ', '').replace('dưới ', '');
};
export const dateUTC = (date: Date) => {
  return new Date(
    Date.UTC(
      date.getUTCFullYear(),
      date.getUTCMonth(),
      date.getUTCDate(),
      date.getUTCHours(),
      date.getUTCMinutes(),
      date.getUTCSeconds(),
      date.getUTCMilliseconds()
    )
  );
};
