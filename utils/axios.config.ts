import { auth } from '@/auth';
import axios from 'axios';
import { ApiEndpoints, EntRouters } from 'configs';
import { getSession } from 'next-auth/react';

const axiosConfig = axios.create({
  baseURL: ApiEndpoints.BASE_URI,
});

axiosConfig.interceptors.request.use(
  async (config) => {
    config.withCredentials = false;
    if (![EntRouters.login].includes(config.url!)) {
      if (typeof window === 'undefined') {
        const session = await auth();
        if (session && session.user) {
          config.headers['x-access-token'] = session.accessToken;
        }
      } else {
        if (!config?.headers['x-access-token'] || config.headers['x-access-token'] === '') {
          const session = await getSession();
          console.log(config.url);
          config.headers['x-access-token'] = session?.accessToken || '';
        }
      }
    }
    return config;
  },
  (error) => Promise.resolve(error)
);
axiosConfig.interceptors.response.use(
  (response) => response.data,
  async (error) => {
    return Promise.reject(error.response || error);
  }
);

export default axiosConfig;
