import CryptoJS from 'crypto-js';
import { getCookie } from 'cookies-next';

function getKeyFromSources() {
  const keyParts = [
    
    // document.body.dataset.secretPart || '',
    localStorage.getItem('ent.app-name') || '',
    getCookie('ent.member-part') || '',
    process.env.NEXT_PUBLIC_3DES || ''
  ];
  return keyParts.join('');
}
export function encryptTripleDES(plaintext: string) {
  const keyString = getKeyFromSources();
  try {
    const key = CryptoJS.enc.Utf8.parse(keyString);

    const encrypted = CryptoJS.TripleDES.encrypt(plaintext, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7, // PKCS5 tương đương PKCS7 khi blockSize = 8
    });
    console.log('encrypted', plaintext, encrypted.toString());
    return encrypted.toString();
  }catch (e) {
    console.log('exception', e.message ?? 'Khng rõ lỗi');
    return plaintext;
  }
}
