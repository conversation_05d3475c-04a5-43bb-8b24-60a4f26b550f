import CanAccess, { AccessEnum } from '@/configs/CanAccess';
import { RoleEnum } from '@/configs/RoleEnum';
import { Session } from 'next-auth';

/**
 * Kiểm tra xem người dùng có quyền truy cập vào một tính năng cụ thể không
 * @param session Session của người dùng hiện tại
 * @param accessType Loại quyền truy cập cần kiểm tra (có thể sử dụng AccessEnum hoặc chuỗi 'category.permission')
 * @returns boolean - true nếu có quyền, false nếu không có quyền
 */
export const checkAccess = (session: Session | null, accessType: AccessEnum | string): boolean => {
  if (!session || !session.user) {
    return false;
  }

  // Nếu là admin (ROOT), luôn có quyền truy cập
  if (session.user.role_id === RoleEnum.ROOT) {
    return true;
  }

  // Phân tách accessType thành các phần (ví dụ: 'learn.approve' -> ['learn', 'approve'])
  const [category, permission] = accessType.toString().split('.');

  // Kiểm tra xem người dùng có quyền truy cập vào danh mục này không
  if (!session.user.canAccess || !session.user.canAccess[category]) {
    return false;
  }

  // Kiểm tra quyền cụ thể
  const permissionValue = CanAccess[category.toUpperCase()]?.[permission.toUpperCase()];
  if (!permissionValue) {
    return false;
  }

  return session.user.canAccess[category].includes(permissionValue);
};

/**
 * Kiểm tra xem người dùng có quyền truy cập vào một tính năng cụ thể hoặc là chủ sở hữu của một đối tượng
 * @param session Session của người dùng hiện tại
 * @param accessType Loại quyền truy cập cần kiểm tra (có thể sử dụng AccessEnum hoặc chuỗi 'category.permission')
 * @param ownerId ID của chủ sở hữu đối tượng (nếu có)
 * @returns boolean - true nếu có quyền hoặc là chủ sở hữu, false nếu không
 */
export const checkAccessOrOwner = (
  session: Session | null,
  accessType: AccessEnum | string,
  ownerId?: number | null
): boolean => {
  // Kiểm tra quyền truy cập thông thường
  const hasAccess = checkAccess(session, accessType);

  // Nếu có quyền truy cập hoặc không có ownerId, trả về kết quả
  if (hasAccess || !ownerId) {
    return hasAccess;
  }

  // Kiểm tra xem người dùng có phải là chủ sở hữu không
  return session?.user?.id === ownerId;
};

/**
 * Hàm rút gọn để kiểm tra quyền truy cập với AccessEnum
 * @param session Session của người dùng hiện tại
 * @param access Quyền truy cập cần kiểm tra từ AccessEnum
 * @returns boolean - true nếu có quyền, false nếu không có quyền
 */
export const hasAccess = (session: Session | null, access: AccessEnum): boolean => {
  return checkAccess(session, access);
};

/**
 * Hàm rút gọn để kiểm tra quyền truy cập hoặc chủ sở hữu với AccessEnum
 * @param session Session của người dùng hiện tại
 * @param access Quyền truy cập cần kiểm tra từ AccessEnum
 * @param ownerId ID của chủ sở hữu đối tượng (nếu có)
 * @returns boolean - true nếu có quyền hoặc là chủ sở hữu, false nếu không
 */
export const hasAccessOrOwner = (
  session: Session | null,
  access: AccessEnum,
  ownerId?: number | null
): boolean => {
  return checkAccessOrOwner(session, access, ownerId);
};

export default checkAccess;
