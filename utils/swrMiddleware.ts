const swrMiddleware = (token: string | null) => {
  return (useSWRNext: any) => {
    return (key: string, fetcher: any, config: any) => {
      const extendedFetcher = (...args: any) => {
        if (token) {
          args[1] = {
            ...args[1],
            headers: {
              ...(args[1]?.headers || {}),
              'x-access-token': token,
            },
          };
        }

        return fetcher(...args).finally(() => {});
      };
      // Handle the next middleware, or the `useSWR` hook if this is the last one.
      const swr = useSWRNext(key, extendedFetcher, config);
      // After hook runs...
      return swr;
    };
  };
};
export default swrMiddleware;
