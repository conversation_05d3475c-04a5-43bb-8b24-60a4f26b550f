/** 🔹 <PERSON>à<PERSON> lấy userId từ localStorage */
export const getStoredUserId = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('favourite_user_id');
  }
  return null;
};

/** 🔹 Hàm lưu userId vào localStorage */
export const saveStoredUserId = (userId: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('favourite_user_id', userId);
  }
};
