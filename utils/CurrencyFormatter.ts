import numeral from 'numeral';

export const formatCurrencyWithVNDSymbol = (number: number | string | undefined) => {
  if (!number || !['string', 'number'].includes(typeof number)) {
    return '0đ';
  }

  return numeral(number).format('0,0')?.replace(/,/g, '.') + 'đ';
};

export const formatCurrencyWithComma = (number: number | string | undefined) => {
  if (!number) {
    return '0';
  }
  return numeral(number).format('0,0');
};
