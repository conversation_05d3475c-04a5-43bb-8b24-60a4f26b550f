export function nonAccentVietnamese(str: string) {
  str = str.toLowerCase();
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  // Some system encode vietnamese combining accent as individual utf-8 characters
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // Huyền sắc hỏi ngã nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // Â, Ê, Ă, Ơ, Ư
  return str;
}

export function convertToSlug(text: string) {
  return text
    .toLowerCase() // Chuyển tất cả chữ cái thành chữ thường
    .replace(/\s+/g, '-') // Thay tất cả khoảng trắng bằng dấu gạch ngang
    .replace(/[^\w\-/]+/g, '') // Loại bỏ các ký tự không phải chữ cái, chữ số hoặc dấu gạch ngang
    .replace(/\/-\/-+/g, '-') // Thay nhiều dấu gạch ngang liên tiếp bằng một dấu gạch ngang
    .replace(/^-+/, '') // Loại bỏ dấu gạch ngang ở đầu chuỗi
    .replace(/-+$/, ''); // Loại bỏ dấu gạch ngang ở cuối chuỗi
}

export const delay = (ms: number = 100): Promise<any> => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const typeToIconMap: { [key: number]: string } = {
  9: 'icon-home',
  128: 'icon-history',
  159: 'icon-star-line',
  129: 'icon-briefcase',
  130: '',
  10: 'icon-course',
  11: 'icon-conversation',
  162: 'icon-gallery',
  163: 'icon-music',
  196: 'icon-essay',
  197: 'icon-presentation',
  198: 'icon-sentence',
  199: 'icon-word',
  119: '',
  160: 'icon-voiceprint-line',
  161: 'icon-knowledge',
};

export const typeStringToIconMap: { [key: string]: string } = {
  '9': 'icon-home',
  history: 'icon-history',
  favourite: 'icon-star-line',
  histori: 'icon-briefcase',
  paragraph: 'icon-conversation',
  course: 'icon-course',
  conversation: 'icon-conversation',
  gallery: 'icon-gallery',
  lyric: 'icon-music',
  essay: 'icon-essay',
  presentation: 'icon-presentation',
  sentence: 'icon-sentence',
  word: 'icon-word',
  document: 'icon-document',
  pronounce: 'icon-voiceprint-line',
  knowledge: 'icon-knowledge',
  groups: 'icon-group',
  workspace: 'icon-workspace',
  tag: 'icon-flag-2-line',
};
