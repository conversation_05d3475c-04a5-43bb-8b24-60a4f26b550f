import fs from 'fs';
import JavaScriptObfuscator from 'javascript-obfuscator';
import path from 'path';

const inputFilePath = path.resolve('utils/obf/encryptor.raw.ts');
const outputFilePath = path.resolve('public/js/encrypt.js');

const inputCode = fs.readFileSync(inputFilePath, 'utf8');

const obfuscationResult = JavaScriptObfuscator.obfuscate(inputCode, {
  compact: true,
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 0.8,
  deadCodeInjection: true,
  deadCodeInjectionThreshold: 0.4,
  disableConsoleOutput: true,
  stringArray: true,
  stringArrayEncoding: ['rc4'],
  stringArrayThreshold: 0.75,
  selfDefending: true,
  transformObjectKeys: true,
  unicodeEscapeSequence: true,
});

fs.writeFileSync(outputFilePath, obfuscationResult.getObfuscatedCode());

console.log('✅ Obfuscation complete: lib/obf/encrypt.js');
