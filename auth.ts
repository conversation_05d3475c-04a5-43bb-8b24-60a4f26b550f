import { ApiEndpoints, OTP_MEMBER_LENGTH } from '@/configs';
import { StatusEnum } from '@/configs/StatusEnum';
import { IUser } from '@/nextauth';
import { apiRequest } from '@/services';
import { Member } from '@/types/auth';
import type { NextAuthConfig } from 'next-auth';
import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

export const authConfig = {
  trustHost: true,
  session: {
    strategy: 'jwt',
    maxAge: 86400, // Session sống trong 24 giờ
  },
  jwt: {
    maxAge: 86400, // JWT sống trong 24 giờ
  },
  pages: {
    signIn: '/auth/login', // Displays signin buttons
    error: '/auth/login', // Error code passed in query string as ?error=
    verifyRequest: '/auth/verify-request', // (used for check email message)
    newUser: '/auth/register',
  },
  providers: [
    CredentialsProvider({
      id: 'account-login',
      name: 'Account Login',
      credentials: {
        phone: { label: 'Phone', type: 'text' },
        password: { label: 'Password', type: 'password' },
      },
      authorize: async (credentials) => {
        const { phone, password } = credentials ?? null;
        if (
          password === undefined ||
          password === null ||
          password === '' ||
          password.toString().length < 8
        ) {
          throw new Error('Login failed!!');
        }
        if (
          phone === undefined ||
          phone === null ||
          phone === '' ||
          !/(84|0[35789])+(\d{8})\b/.test(phone.toString())
        ) {
          throw new Error('Missing username or password');
        }
        const params = {
          phone: phone,
          password: password,
        };
        try {
          const { success, data, message } = await apiRequest(ApiEndpoints.LOGIN, {
            method: 'post',
            data: params,
          });
          if (!success) {
            // Throw error với message từ API
            throw new Error(data?.message || message || 'Login failed');
          }
          return data;
        } catch (error: any) {
          if (error instanceof Error) {
            throw error;
          }
          throw new Error(error?.message || 'Login failed!!!');
        }
      },
    }),
    CredentialsProvider({
      id: 'member-login',
      name: 'Member Login',
      credentials: {
        password: { label: 'Password', type: 'password' },
        token: { label: 'Token', type: 'text' },
      },
      // @ts-ignore
      async authorize({ password, token }) {
        if (
          password === undefined ||
          password === null ||
          password === '' ||
          password.toString().length !== OTP_MEMBER_LENGTH
        ) {
          throw new Error('passWrong');
        }
        if (typeof token === 'undefined' || token === null || token === '') {
          throw new Error('tokenWrong');
        }
        const params = {
          token: token,
          password: password,
        };
        try {
          const { success, data, message } = await apiRequest(ApiEndpoints.LOGIN_MEMBER, {
            method: 'post',
            data: params,
          });
          if (!success) {
            throw new Error(data?.message || message || 'Login failed..');
          }
          return data;
        } catch (error: any) {
          if (error instanceof Error) {
            throw error;
          }
          throw new Error(error?.message || 'Login failed.');
        }
      },
    }),
  ],
  callbacks: {
    authorized: async ({ auth }) => {
      return !!auth;
    },
    async signIn({ user }) {
      return !!user;
    },
    jwt: ({ token, user, trigger, session }) => {
      if (trigger === 'update') {
        // @ts-ignore
        token.user.member = session.member;
      }
      if (user) {
        // @ts-ignore
        token.user = user;
      }
      return token;
    },
    session: ({ session, token }) => {
      if (token.user) {
        // Only update if the user data actually changed
        const currentUser = token.user.account || null;
        if (!session.user || session.user.id !== currentUser?.id) {
          // @ts-ignore
          session.user = currentUser ? ({ ...currentUser } as IUser) : null;
        }
        session.member_categories = token.user.member_categories || null;

        // Only update these if they're different
        if (session.member !== token.user.member) {
          session.member = token.user.member || null;
        }

        if (session.members !== token.user.members) {
          session.members = token.user.members || null;
        }

        if (session.accessToken !== token.user.member_token) {
          session.accessToken = token.user.member_token;
        }

        if (session.account_token !== token.user.account_token) {
          session.account_token = token.user.account_token || '';
        }

        if (session.member_token !== token.user.member_token) {
          session.member_token = token.user.member_token;
        }

        // Only update canAccess if user exists and role changed
        if (session.user?.id && !session.user.canAccess) {
          session.user.canAccess = token.user.role?.list_access || {};
        }

        // Only find main member if session.member is null
        if (!session.member && session.members) {
          session.member =
            session.members.find((member: Member) => member.is_main === StatusEnum.ON) || null;
        }

        if (token.error && session.error !== token.error) {
          session.error = token.error;
        }
      }
      return session;
    },
  },
} satisfies NextAuthConfig;

export const { handlers, signIn, signOut, auth } = NextAuth(authConfig);
