# Build dependencies
FROM node:22.13.0-alpine AS deps
WORKDIR /app
RUN apk add --no-cache tzdata bash jq gettext util-linux curl
COPY package.json yarn.lock* pnpm-lock.lock* ./
RUN yarn cache clean && yarn

# Build application
FROM node:22.13.0-alpine AS builder
WORKDIR /app
ARG GIT_TAG
ARG GIT_COMMIT
ARG COMPILED_AT

# Copy dependencies and source code
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate version info and build
RUN echo "{\"data\":{\"commit\":\"${GIT_COMMIT}\",\"date\":\"${COMPILED_AT}\",\"version\":\"${GIT_TAG}\"},\"meta\":{}}" > public/version.json && \
    yarn build && \
    rm -rf node_modules && \
    yarn install --production --frozen-lockfile --ignore-scripts --prefer-offline

# Production image
FROM node:22.13.0-alpine AS runner
WORKDIR /app
EXPOSE 3000

ARG NODE_ENV=dev
ENV NODE_ENV=${NODE_ENV}
ENV PORT=3000

# Copy only necessary files from builder
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.js ./next.config.js
COPY . .

# Start the application
CMD ["yarn", "start"]
