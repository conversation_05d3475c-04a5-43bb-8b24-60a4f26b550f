import { ApiEndpoints } from 'configs';
import { SidebarEnum } from 'configs/SidebarEnum';
import { apiRequest } from 'services';
import { CategoryEntity } from 'types/model';

const categoryAction = async (sidebarType = SidebarEnum.MAIN) => {
  const { data } = await apiRequest(`${ApiEndpoints.CATEGORY_LIST}?status=2`);
  const _categories = data?.categories || [];
  _categories &&
    _categories.map((item: CategoryEntity) => {
      item.keyx = item.keyx === undefined ? '' : item.keyx;
      item.title = item.title === undefined ? '' : item.title;
      item.icon = item.icon === undefined ? '' : item.icon;
      return item;
    });
  return _categories;
};
export default categoryAction;
