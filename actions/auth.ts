'use server';

import { signIn, signOut } from '@/auth';
import { COOKIE_PHONE } from '@/configs';
import { LoginSchema } from '@/utils/schemas';
import { deleteCookie, } from 'cookies-next';
import { AuthError } from 'next-auth';
import { z } from 'zod';

export async function loginAction(prevState: any, formData: z.infer<typeof LoginSchema>) {
  const values = {
    phone: formData.phone,
    password: formData.password,
    remember: formData.remember,
  };

  try {
    const result = await signIn('account-login', {
      ...values,
      redirect: false,
    });
    if (result?.error) {
      return { error: result.error };
    }
    return { success: true };
  } catch (error) {
    if (error instanceof AuthError) {
      return { error: error.cause?.err?.message || error.message };
    }
    return { error: 'An unexpected error occurred' };
  }
}
export const logout = async () => {
  // Xóa cookies khi đăng xuất
  deleteCookie(COOKIE_PHONE);
  deleteCookie('ent.rememberMe');

  await signOut();
};
