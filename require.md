#sample data

## $conversations

[
[
{
"id": 27,

            "character_id": 3,
            "content": "I love to mix colors.",

        },
        {
            "id": 28,

            "character_id": 3,
            "content": "Red, blue, and yellow are primary colors.",

        }
    ],
    [
        {
            "id": 29,

            "character_id": 3,
            "content": "When I mix red and blue, I get purple.",

        },
        {
            "id": 30,

            "character_id": 3,
            "content": "When I mix blue and yellow, I get green.",

        }
    ],
    [
        {
            "id": 31,

            "character_id": 4,
            "content": "I can blend my own colors.",

        },
        {
            "id": 32,
            "character_id": 4,
            "content": "I use paint to create a new color.",
        }
    ]

]

## $activeChar

{
"id": 3,
"fullname": "<PERSON>. Alice",
"sentence_id": 30
}

#requirement
tôi muốn hiển thị danh sách dạng chatbox với data là $conversations. nếu $activeChar.id === $conversations.item.character_id thì hiển thị bên phải, nếu không thì hiển thị bên trái.
danh sách sẽ foreach cho tới khi $conversations.id === $activeChar.sentence_id thì dừng lại không hiển thị ra nữa.

Khi tôi thực hiện event Enter, hiển thị ra item $conversations.

- Nếu block ở bên trái, hiển thị hết toàn bộ các item con của nó ra.
- Nếu block ở bên phải, mỗi lần bấm enter chỉ hiển thị 1 item nhỏ.

Khi tôi bấm ArrowLeft, thực hiện xóa từng item trong block gần nhất. Khi block gần nhất được xóa hết item, chuyển sang xóa item của block trước đó
