import { NextResponse } from 'next/server';

import { auth } from 'auth';
import EntRouters from 'configs/EntRouters';
import { NextAuthRequest } from 'next-auth/lib';

export default auth(async (req: NextAuthRequest) => {
  try {
    const { nextUrl } = req;
    const isAuthenticated = !!req.auth;
    const isPublicRoute = [
      EntRouters.otp,
      EntRouters.login,
      EntRouters.member_login,
      EntRouters.register,
    ].some(
      (route) =>
        nextUrl.pathname.startsWith(route) && !nextUrl.pathname.startsWith(EntRouters.member_detail)
    );

    if (isPublicRoute && isAuthenticated) {
      return NextResponse.redirect(new URL(EntRouters.home, nextUrl));
    }

    if (!isAuthenticated && !isPublicRoute) {
      return NextResponse.redirect(new URL(EntRouters.login, nextUrl));
    }
    if (
      req.auth &&
      req.auth.member?.is_onboard &&
      req.auth.member?.is_onboard !== 2 &&
      !nextUrl.pathname.startsWith(EntRouters.welcome)
    ) {
      return NextResponse.redirect(new URL(EntRouters.welcome, req.nextUrl));
    }
    return NextResponse.next();
  } catch (error) {
    console.error('Error in auth middleware:', error.message);
    return NextResponse.redirect(new URL(EntRouters.page401, req.nextUrl));
  }
});
export const config = {
  matcher: [
    // Match all routes except the ones that start with /login and api and the static folder
    '/((?!api|_next/static|_next/image|_next|favicon.ico|favicon.png|login|logo-rectangle.png|version.json).*)',
    // '/onboarding/:path*',
  ],
};
