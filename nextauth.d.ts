// @ts-ignore
import { DefaultSession, Session } from 'next-auth';
// @ts-ignore
import { JWT } from 'next-auth/jwt';

import { Account, IRole, Member } from './types/auth';
import { CategoryMemberEntity } from './types/model';

export type IUser = {
  id: number;
  role_id?: number;
  role?: IRole;
  phone: string;
  created_at: number;
  status: number;
  type?: number;
  account_token: string;
  member_token: string;
  account: Account;
  member: Member | null;
  members: Array<Member>;
  canAccess: Array[string];
};

declare module 'next-auth' {
  interface Session {
    user: IUser & Omit<DefaultSession['user'], 'id|account_id'>;
    member: Member | null;
    members: Array<Member>;
    accessToken: string;
    member_token: string;
    account_token: string;
    member_categories: Array<CategoryMemberEntity>;
    error?: string;
  }
}
declare module 'next-auth/jwt' {
  interface JWT {
    user: IUser & {
      account_token: string;
      member_token: string;
      account: Account;
      member: Member | null;
      members: Array<Member>;
      member_categories: Array<CategoryMemberEntity>;
    };
    error?: string;
  }
}
