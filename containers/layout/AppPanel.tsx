'use client';

import React, { useEffect, useState } from 'react';

import { Button } from '@/components';
import { useAppContext } from '@/store/contexts/AppContext';
import classNames from 'classnames';
import useLearnStore from 'store/learn';
import { AppContextProps } from 'types/theme';

const AppPanel = () => {
  const { setPanel, panel, setOpenPanel }: AppContextProps = useAppContext();
  const { setSelectedSentence } = useLearnStore();
  const handleClosePanel = () => {
    setPanel(null);
    setOpenPanel(false);
    setSelectedSentence(null);
  };
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup event listener
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  return (
    <div
      className={classNames('', {
        'fixed md:w-panel inset-y-0 right-0 transform md:relative md:translate-x-0 h-screen top-0 z-modal transition-all ease-in-out duration-500':
          isMobile,
        'relative h-screen top-0 z-modal transition-all ease-in-out duration-500': !isMobile,
        'w-panel translate-x-0 bg-bg-general py-0 top-0 border-l border-bg-box h-screen pl-4 flex flex-col':
          panel,
        'translate-x-full w-0': !panel,
      })}
    >
      <>
        <div className={'text-left w-6 pt-[10px]'}>
          <Button size={'icon'} className={'hover:bg-bg-box'} onClick={handleClosePanel}>
            <i className={'text-2xl text-color-minor !text-left icon-close'} />
          </Button>
        </div>
        {panel ? panel : ''}
      </>
    </div>
  );
};
export default AppPanel;
