'use client';

import React, { useState } from 'react';

import { useParams, useRouter } from 'next/navigation';

// import useGroupMemberByToken from 'hooks/Ent/useGroupMemberByToken';
// import { useMounted } from 'hooks/common/useMounted';
import classNames from 'classnames';
import Avatar from 'components/Avatar';
import Button from 'components/Button';
import ScrollArea from 'components/ScrollArea';
import useAddMemberGroups from 'hooks/Ent/useAddMemberGroups';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import { useSession } from '@/hooks/useSession';

const InviteMemberContainer = ({ group }) => {
  const [selectedMembers, setSelectedMembers] = useState<number[]>([]);
  const { data: session } = useSession();
  const router = useRouter();
  const params = useParams();
  const t = useTranslations();
  const token = params.token?.toString() || '';
  if (!token) router.back();
  // console.log('group', group);
  // const { group } = useGroupMemberByToken(token);
  // const mounted = useMounted();
  const { doInviteMembersGroup } = useAddMemberGroups();
  const toggleMember = (id: number) => {
    setSelectedMembers((prev) =>
      prev.includes(id) ? prev.filter((m) => m !== id) : [...prev, id]
    );
  };

  const [isHidden, setIsHidden] = useState(false);
  const handleInvite = async (group_id: number) => {
    if (group_id === 0 || selectedMembers.length === 0) {
      toast.error(t('group.class.invalid_group_or_member'));
      return;
    }
    setIsHidden(true);

    try {
      const res = await doInviteMembersGroup({ group_id: group_id, member_ids: selectedMembers }); // sửa hàm để chấp nhận mảng member
      if (res.success) {
        toast.success(t('group.class.request_sent_sus'));
      } else {
        toast.error(res.message || t('group.class.request_sent_false'));
        setIsHidden(false);
      }
    } catch (e) {
      toast.error(t('group.class.request_sent_false'));
      setIsHidden(false);
    }
    setIsHidden(false);
  };
  console.log('session.members', session.members);
  return (
    <div className={'flex w-full  flex-col'}>
      <div className={' text-center mb-0'}>
        <ScrollArea className={' relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
          {session.members && (
            <div className={'flex flex-col  mt-5'}>
              <div className="flex flex-wrap -mx-2">
                {map(session.members, (member) => {
                  const isSelected = selectedMembers.includes(member.id);
                  return (
                    <div
                      key={member.id}
                      onClick={() => toggleMember(member.id)}
                      className={classNames('w-1/2 px-2 mb-4 cursor-pointer')}
                    >
                      <div
                        className={classNames(
                          'flex items-center justify-between rounded-[5px] h-12 p-3',
                          {
                            'border border-color-line bg-bg-general': isSelected,
                            'border border-none bg-bg-box': !isSelected,
                          }
                        )}
                      >
                        <div className="flex items-center">
                          <Avatar name={member.fullname} size={32} className="mr-3" />
                          <div className="flex flex-col justify-center text-color-major">
                            <span>{member.fullname}</span>
                          </div>
                        </div>
                        {isSelected ? (
                          <div className="rounded-full w-4 h-4 bg-white border-2 border-purple-100">
                            <i className="icon-ok-circled text-purple-100 text-base w-[12px] h-[12px]" />
                          </div>
                        ) : (
                          <div className="border-color-border border-2 rounded-full w-4 h-4" />
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </ScrollArea>
      </div>
      <div className={'text-center mb-2'}>
        <Button
          color={'primary'}
          size={'xs'}
          className={'h-[35px] font-medium text-base'}
          onClick={() => handleInvite(group?.id)}
          disabled={isHidden}
        >
          {isHidden ? <i className={'animate-spin text-white text-sm w-3 h-3 icon-spin2'} /> : null}{' '}
          {t('group.class.request')}
        </Button>
      </div>
    </div>
  );
};
export default InviteMemberContainer;
