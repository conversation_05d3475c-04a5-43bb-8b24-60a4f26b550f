'use client';

import React, { memo } from 'react';

import { Slider } from 'components/Slider';
import useSpeakingStore from 'store/speaking';

const VolumeControl = () => {
  const { volume, setVolume } = useSpeakingStore();

  const handleChangeVolume = (val: number) => {
    setVolume(val);
  };
  const handleChangeEnd = () => {
    // @ts-ignore
    document.activeElement?.blur();
  };
  return (
    <div className={'flex items-center w-[150px] relative'} tabIndex={-1}>
      <div
        className={'flex flex-col gap-2 w-full h-full max-w-md items-start justify-center'}
        tabIndex={-1}
      >
        <Slider
          tabIndex={-1}
          classNames={{
            track: 'h-2 rounded-md',
            filler: 'rounded-md',
          }}
          aria-label={'Volume'}
          size={'base'}
          color={'success'}
          value={volume}
          hideThumb
          onChangeEnd={handleChangeEnd}
          onChange={handleChangeVolume}
          startContent={<i className={'icon-volume-up text-base text-color-minor'} />}
          className={'max-w-md'}
        />
      </div>
    </div>
  );
};
export default memo(VolumeControl);
