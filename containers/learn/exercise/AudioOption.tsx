import { ExerciseOption } from '@/configs/Exercise';
import { CheckIcon } from '@/containers/learn/exercise/Check';
import { useEventListener } from 'usehooks-ts';

import WavePlayer from '@/components/Audio/WavePlayer';

type AudioOptionProps = {
  status: ExerciseOption;
  audioUrl: string;
  index: number;
  onClick: () => void;
};

export const AudioOption = ({ status, onClick, audioUrl, index }: AudioOptionProps) => {
  useEventListener('keydown', (e) => e.key === (index + 1).toString() && onClick(), undefined, {
    capture: true,
  });

  return (
    <div
      className={`w-full transition-all duration-200 ease-in-out justify-between h-16 border border-solid items-center relative  pr-1.5 border-color-border shadow-lg ${status === ExerciseOption.NOT_SELECTED ? 'bg-bg-general' : 'bg-bg-box'} rounded-[5px] flex`}
    >
      <div className={'w-[calc(100%-40px)] z-20 h-10 rounded-[5px] bg-color'}>
        <WavePlayer url={audioUrl} mb={0} />
      </div>
      <div
        className={'flex-1 w-full h-full z-10 flex justify-end items-center'}
        onClick={() => onClick()}
      >
        <CheckIcon status={status} />
      </div>
    </div>
  );
};
