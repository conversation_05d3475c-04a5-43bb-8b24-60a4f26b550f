import React from 'react';

import { ExerciseOption } from '@/configs/Exercise';
import { CheckIcon } from '@/containers/learn/exercise/Check';
import { useEventListener } from 'usehooks-ts';

type TextOptionProps = {
  text: string;
  status: ExerciseOption;
  index: number;
  onClick: () => void;
};

export const TextOption = ({ text, status, onClick, index }: TextOptionProps) => {
  useEventListener('keydown', (e) => e.key === (index + 1).toString() && onClick(), undefined, {
    capture: true,
  });

  return (
    <div
      onClick={() => onClick()}
      className={`w-full transition-all duration-200 ease-in-out justify-between h-16 border border-solid items-center pl-4 pr-1.5 border-color-border shadow-lg ${status === ExerciseOption.NOT_SELECTED ? 'bg-bg-general' : 'bg-bg-box'} rounded-[5px] flex`}
    >
      <p className={'text-color-major text-sm font-medium'}>{text}</p>
      <div>
        <CheckIcon status={status} />
      </div>
    </div>
  );
};
