import React from 'react';

import { Skeleton } from '@heroui/react';

export const QuestionSkeleton = () => {
  return (
    <div
      className={
        'px-[calc((100%_-550px)_/_2)] h-[calc(100vh-200px)] w-full pt-[30px] relative flex flex-col space-y-4'
      }
    >
      <div className="w-full flex flex-col gap-2.5">
        <Skeleton className="h-5 w-3/5 rounded-[5px]" />
        <Skeleton className="h-5 w-2/5 rounded-[5px]" />
      </div>
      <div className="w-full flex flex-col gap-2.5 mt-8">
        <Skeleton className="h-5 w-full rounded-[5px]" />
        <Skeleton className="h-5 w-full rounded-[5px]" />
        <Skeleton className="h-5 w-full rounded-[5px]" />
      </div>

      <div className="w-full flex flex-col gap-2.5 mt-8">
        <Skeleton className="h-5 w-full rounded-[5px]" />
        <Skeleton className="h-5 w-full rounded-[5px]" />
        <Skeleton className="h-5 w-full rounded-[5px]" />
      </div>
    </div>
  );
};
