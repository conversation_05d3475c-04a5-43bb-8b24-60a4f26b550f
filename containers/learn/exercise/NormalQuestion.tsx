import React from 'react';

import { AnswerFormat, ExerciseOption } from '@/configs/Exercise';
import { AudioOption } from '@/containers/learn/exercise/AudioOption';
import { ImageOption } from '@/containers/learn/exercise/ImageOption';
import { TextOption } from '@/containers/learn/exercise/TextOption';
import { Answer, Question } from '@/interfaces/exercise.interface';

type NormalQuestionProps = {
  answers: Answer[];
  activeQuestion: Question;
  selectedAnswer: number[];
  setSelectedAnswer: React.Dispatch<React.SetStateAction<number[]>>;
  answerFormat: AnswerFormat;
  correctAnswers: number[];
};

const MOCK_AUDIO =
  'https://file.langenter.com/course/audio/2024/12/23/1734951254_MQo2_V_TDzcT1A.mp3';

export const NormalQuestion = ({
  answers,
  activeQuestion,
  setSelectedAnswer,
  selectedAnswer,
  answerFormat,
  correctAnswers,
}: NormalQuestionProps) => {
  const isLocked = correctAnswers.length > 0;

  function handleSelectAnswer(answerId: number) {
    if (isLocked) return;

    setSelectedAnswer((prev) =>
      prev.includes(answerId) ? prev.filter((item) => item !== answerId) : [...prev, answerId]
    );
  }

  function getAnswerStatus(answerId: number): ExerciseOption {
    const isSelected = selectedAnswer.includes(answerId);
    const isCorrect = correctAnswers.includes(answerId);

    if (isSelected) {
      return isLocked
        ? isCorrect
          ? ExerciseOption.CORRECT
          : ExerciseOption.WRONG
        : ExerciseOption.SELECTED;
    }

    if (isCorrect && isLocked) {
      return ExerciseOption.CORRECT;
    }

    return ExerciseOption.NOT_SELECTED;
  }

  function renderOption(answer: Answer, index: number) {
    const status = getAnswerStatus(answer.id);
    const commonProps = {
      status,
      onClick: () => handleSelectAnswer(answer.id),
      index,
    };

    switch (answerFormat) {
      case AnswerFormat.SOUND:
        return (
          <AudioOption key={answer.id} {...commonProps} audioUrl={answer?.audio || MOCK_AUDIO} />
        );
      case AnswerFormat.IMAGE:
        return <ImageOption key={answer.id} {...commonProps} imageUrl={answer?.image ?? ''} />;
      default:
        return <TextOption key={answer.id} {...commonProps} text={answer.content} />;
    }
  }

  return (
    <div className={'grid grid-cols-2 gap-x-[22px] gap-y-[14px] mt-10'}>
      {answers.map((answer, index) => renderOption(answer, index))}
    </div>
  );
};
