import React from 'react';

import { ExerciseOption } from '@/configs/Exercise';

type CheckIconProps = {
  additionalClassName?: string;
  status: ExerciseOption;
};

export const CheckIcon = ({ additionalClassName, status }: CheckIconProps) => {
  const checkIconClassName = {
    [ExerciseOption.NOT_SELECTED]: 'icon-checkbox-circle text-color-line',
    [ExerciseOption.SELECTED]: 'icon-ok-circled text-purple-100',
    [ExerciseOption.CORRECT]: 'icon-ok-circled text-primary',
    [ExerciseOption.WRONG]: 'icon-ok-circled text-red',
  };

  return (
    <div className={`w-6 h-6 ${additionalClassName || ''}`}>
      <i className={`text-2xl cursor-pointer ${checkIconClassName[status]}`} />
    </div>
  );
};
