import React, { useCallback, useMemo } from 'react';

import { SentenceComponent } from '@/containers/learn/exercise/SentenceComponent';
import { Answer } from '@/interfaces/exercise.interface';

type SortQuestionProps = {
  answers: Answer[];
  selectedAnswer: number[];
  setSelectedAnswer: React.Dispatch<React.SetStateAction<number[]>>;
  correctAnswers: number[];
};

export const SortQuestion = ({
  answers,
  selectedAnswer,
  setSelectedAnswer,
  correctAnswers,
}: SortQuestionProps) => {
  const isLocked = correctAnswers.length > 0;

  const handleAddItem = useCallback(
    (answerId: number) => {
      if (isLocked) return;
      setSelectedAnswer((prev) => [...prev, answerId]);
    },
    [isLocked, setSelectedAnswer]
  );

  const handleRemoveItem = useCallback(
    (answerId: number) => {
      if (isLocked) return;
      setSelectedAnswer((prev) => prev.filter((id) => id !== answerId));
    },
    [isLocked, setSelectedAnswer]
  );

  const selectedItems = useMemo(() => {
    return selectedAnswer.map((itemId) => {
      const answer = answers.find((answer) => answer.id === itemId);
      return {
        id: itemId,
        content: answer ? answer.content : itemId.toString(),
      };
    });
  }, [answers, selectedAnswer]);

  const availableItems = useMemo(() => {
    return answers
      .filter((item) => !selectedAnswer.includes(item.id))
      .map((item) => ({
        ...item,
      }));
  }, [answers, selectedAnswer]);

  return (
    <div className={'w-full mt-10'}>
      <div
        className={`flex flex-row flex-wrap gap-y-4 justify-center border-b-4 border-color-line border-solid ${selectedItems.length > 0 ? 'mt-auto' : 'mt-[46px]'}`}
      >
        {selectedItems.map((item) => (
          <div
            key={item.id}
            className={'flex items-center flex-col py-2.5 justify-center relative'}
          >
            <SentenceComponent label={item.content} onClick={() => handleRemoveItem(item.id)} />
            <div className={'mt-1 bg-color-line h-1 w-full absolute -bottom-1'} />
          </div>
        ))}
      </div>
      <div className={'flex gap-1 mt-10 flex-wrap items-center justify-center'}>
        {availableItems.map((item) => (
          <SentenceComponent
            label={item.content}
            key={item.id}
            onClick={() => handleAddItem(item.id)}
            isEnabledKeyBoard
          />
        ))}
      </div>
    </div>
  );
};
