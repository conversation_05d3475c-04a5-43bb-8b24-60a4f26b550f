import { useEventListener } from 'usehooks-ts';

export const SentenceComponent = ({
  label,
  onClick,
  isEnabledKeyBoard,
}: {
  label: string;
  onClick?: () => void;
  isEnabledKeyBoard?: boolean;
}) => {
  useEventListener(
    'keydown',
    (event) => {
      if (isEnabledKeyBoard && event.key === label) {
        onClick && onClick();
      }
    },
    undefined,
    { capture: true }
  );

  return (
    <div
      className={
        'px-2 py-0.5 mx-1 bg-bg-general border border-solid border-color-border cursor-pointer rounded-[5px]'
      }
      onClick={() => onClick && onClick()}
    >
      {label}
    </div>
  );
};
