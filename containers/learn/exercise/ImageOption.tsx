import { ExerciseOption } from '@/configs/Exercise';
import { CheckIcon } from '@/containers/learn/exercise/Check';
import { useEventListener } from 'usehooks-ts';

import NextImageWithFallback from '@/components/NextImageWithFallback';

type ImageOptionProps = {
  imageUrl: string;
  status: ExerciseOption;
  index: number;
  onClick: () => void;
};

export const ImageOption = ({ imageUrl, status, onClick, index }: ImageOptionProps) => {
  console.log('🚀 ~ ImageOption ~ imageUrl:', imageUrl);
  useEventListener('keydown', (e) => e.key === (index + 1).toString() && onClick(), undefined, {
    capture: true,
  });

  return (
    <div
      onClick={() => onClick()}
      className={`w-full cursor-pointer transition-all duration-200 ease-in-out justify-between aspect-video border border-solid items-center border-color-border shadow-lg ${status === ExerciseOption.NOT_SELECTED ? 'bg-bg-general' : 'bg-bg-box'} rounded-[5px] flex relative`}
    >
      <div>
        <CheckIcon status={status} additionalClassName={'absolute right-1.5 top-1.5 z-20'} />
      </div>

      <div className={'relative z-10 w-full h-full rounded-[5px] overflow-hidden'}>
        <NextImageWithFallback src={imageUrl} alt="Option" fill objectFit="cover" />
      </div>
    </div>
  );
};
