import { useState } from 'react';

import { QuestionContainer } from '@/containers/learn/exercise/QuestionContainer';
import { QuestionSkeleton } from '@/containers/learn/exercise/QuestionSkeleton';
import StartExercise from '@/containers/learn/exercise/StartExercise';
import { ParagraphEntity } from '@/types/model';
import { useTranslations } from 'next-intl';

import { useExercises } from '@/hooks/Ent/useExercise';

export const Exercise = ({
  paragraph,
  setExerciseProgress,
}: {
  paragraph: ParagraphEntity;
  setExerciseProgress: React.Dispatch<React.SetStateAction<number>>;
}) => {
  const t = useTranslations();
  const { data, isLoading } = useExercises({ paragraph_id: paragraph.id });
  const questions = data?.data?.questions || [];
  const [isStarted, setIsStarted] = useState(false);
  if (isLoading) return <QuestionSkeleton />;

  if (!questions.length) {
    return (
      <div className="flex items-center justify-center h-72 flex-1 mx-auto">
        <span dangerouslySetInnerHTML={{ __html: t('learn.no_exercise') }} />
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-between w-full h-full mb-[100px]">
      {isStarted ? (
        <QuestionContainer
          questions={questions}
          setIsStarted={setIsStarted}
          setExerciseProgress={setExerciseProgress}
        />
      ) : (
        <StartExercise activeParagraph={paragraph} setIsStarted={setIsStarted} />
      )}
    </div>
  );
};
