import { Dispatch, SetStateAction, useEffect, useMemo, useRef } from 'react';

import Image from 'next/image';

import { Button } from '@/components';
import { STATUS_IMAGE_URL } from '@/constant';
import useBalanceStore from '@/store/balance';
import { ParagraphEntity } from '@/types/model';
import { useTranslations } from 'next-intl';
import { toast } from 'react-hot-toast';
import { useEventListener } from 'usehooks-ts';

type StartExerciseProps = {
  activeParagraph: ParagraphEntity;
  setIsStarted: Dispatch<SetStateAction<boolean>>;
};

const StartExercise = ({ activeParagraph, setIsStarted }: StartExerciseProps) => {
  const t = useTranslations();
  const { balance } = useBalanceStore();
  const containerRef = useRef<HTMLDivElement>(null);
  useEventListener(
    'keydown',
    (e) => {
      if (e.key === 'Enter') {
        if (balance <= 0) {
          toast.error(t('learn.balance_not_enough'));
          return;
        }
        setIsStarted(true);
      }
    },
    undefined,
    { capture: true }
  );

  useEffect(() => {
    // Focus the button element when component mounts
    if (containerRef.current) {
      const buttonElement = containerRef.current.querySelector('button');
      if (buttonElement) {
        buttonElement.focus();
      }
    }
  }, []);

  const statusImageUrl = useMemo(() => {
    return STATUS_IMAGE_URL[Math.random() > 0.5 ? 'NA.FINISH' : 'XO.FINISH'];
  }, []);

  return (
    <div className={'flex w-full items-center justify-center flex-col'} ref={containerRef}>
      <h5
        className={
          'text-color-major text-[0.9375rem] py-0 mb-2.5 text-center font-[SVN-Poppins-Regular]'
        }
      >
        {activeParagraph?.title}
      </h5>
      <Image
        src={statusImageUrl}
        className={'mx-auto border-none rounded-md'}
        width={148}
        alt="exercise-image"
        height={148}
        objectFit="contain"
      />
      <>
        <h5
          className={
            'text-color-major text-[0.9375rem] mb-3 mt-5 text-center font-[SVN-Poppins-Regular]'
          }
        >
          {t('learn.startExercise')}
        </h5>
        <Button
          size={'md'}
          color={'primary'}
          className={'!shadow-[0px_-1px_0px_0px_#01AE4E_inset] ml-2'}
          onPress={() => {
            if (balance <= 0) {
              toast.error(t('learn.balance_not_enough'));
              return;
            }
            setIsStarted(true);
          }}
        >
          {t('learn.start')}
        </Button>
        <span className="text-color-minor text-[0.625rem] mt-2">Enter</span>
      </>
    </div>
  );
};

export default StartExercise;
