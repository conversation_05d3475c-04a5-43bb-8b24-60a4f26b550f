import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const SentenceSkeleton = () => {
  return (
    <>
      <div
        className={
          'w-full flex justify-between items-center px-[30px] pb-2.5 border-b-[10px] border-b-bg-box relative z-hero mt-2'
        }
      >
        <div className={'flex items-center gap-3'}>
          <Skeleton className="rounded-sm h-4 w-24" />
          <Skeleton className="rounded-sm h-4 w-4" />
        </div>
        <div className={'flex items-center gap-3'}>
          <Skeleton className="rounded-sm h-5 w-40" />
          <Skeleton className="rounded-sm h-5 w-24" />
        </div>
      </div>
      <div
        className={
          'w-full px-[calc((100%_-_615px)_/_2)] !overflow-y-scroll flex flex-col gap-5 py-5'
        }
      >
        {map(range(1, 12), (index) => (
          <div key={index} className={'w-full flex items-end gap-3'}>
            <Skeleton className="rounded-full shrink h-10 w-10" />
            <div className={'w-full flex flex-1 items-start flex-col gap-2'}>
              <Skeleton className={'rounded-sm w-1/5 h-2'} />
              <Skeleton className={'rounded-sm w-2/5 h-4'} />
              <Skeleton className={'rounded-sm w-3/5 h-4'} />
            </div>
          </div>
        ))}
      </div>
    </>
  );
};
export default SentenceSkeleton;
