'use client';
import classNames from 'classnames';
import React from 'react';
import Avatar from 'components/Avatar';

const Character = ({ character, onClick, selectedCharacterId = 0 }) => {
  const handleClick = (character) => {
    onClick(character);
  };
  return (
    <div
      className={classNames(
        'flex flex-shrink-0 items-center justify-between relative rounded-[5px] max-w-[180px] mx-[10px] w-full h-12 p-3 cursor-pointer',
        {
          'border border-color-line bg-bg-general': character.id === selectedCharacterId,
          'border border-none bg-bg-box': character.id !== selectedCharacterId,
        },
      )}
      onClick={() => handleClick(character)}
    >
      <div className={'flex items-center'}>
        <Avatar name={character.fullname} size={32} className={'mr-3'} />
        <div className={classNames('flex flex-col justify-center text-color-major')}>
          <span>{character.fullname}</span>
          <span>{character.description}</span>
        </div>
      </div>
      {character.id === selectedCharacterId ? (
        <div className="rounded-full w-4 h-4 bg-white border-2 border-purple-100">
          <i className={'-mt-0.5 text-base icon-ok-circled text-purple-100'} />
        </div>
      ) : (
        <div className="border-color-border border-2 rounded-full w-4 h-4"></div>
      )}
    </div>
  );
};

export default Character;
