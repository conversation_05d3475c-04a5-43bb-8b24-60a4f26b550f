'use client';
import React, { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

const CountDownSpeaking = ({ onFinishCountDown }) => {
  const t = useTranslations();
  const INITIAL_COUNTDOWN = 3;
  const [countDown, setCountDown] = useState(INITIAL_COUNTDOWN);
  useEffect(() => {
    let t = INITIAL_COUNTDOWN;
    const timer = setInterval(() => {
      t--;
      setCountDown(t);
      if (t < 0) {
        clearInterval(timer);
      }
    }, 1000);

  }, []);
  useEffect(() => {
    if (countDown === 0) onFinishCountDown(false);
  }, [countDown]);
  return (
    <div className={'flex flex-col justify-center items-center mt-12'}>
      <span className={'text-center pb-10'}>{t('learn.countDownLabel')}</span>
      <div className={'flex justify-center w-full text-[108px]'}>{countDown}</div>
    </div>
  );
};
export default CountDownSpeaking;
