import { SentenceProcessEnum } from '@/configs/SentenceProcessEnum';
import Button from '@/components/Button';
import { StatusEnum } from '@/configs/StatusEnum';
import classNames from 'classnames';
import React from 'react';
import { useTranslations } from 'next-intl';
import useSpeakingStore from '@/store/speaking';
import useLearnStore from '@/store/learn';
import useBalanceStore from '@/store/balance';
import { RecordProcessEnum } from '@/configs/RecordProcessEnum';
import { useConversationContext } from '@/providers/ConversationProvider';
import { groupConversationSentences } from '@/helpers/sentence';
import { useListenGroupSentences } from '@/hooks/Ent/useListenGroupSentences';

const SubmitAction = ({ isSendingRecord, stopMicrophone }) => {
  const t = useTranslations();
  const { sentence, sentenceProcess, recordProcess } = useSpeakingStore();
  const { balanceStatus } = useBalanceStore();
  const { sentences } = useConversationContext();
  const conversations = groupConversationSentences(sentences);

  const {
    activeCharacter,
    speakings,
    setCharacter,
    setFinishLearn,
    activeParagraph
  } = useLearnStore();

  const { handleFinishConversation } = useListenGroupSentences({
    groupSentences: [],
    listenCurrentId: 0,
    paragraph: activeParagraph,
    setActiveTab: () => { },
  });
  const handleFinishSpeaking = () => {
    setFinishLearn(true);
    setCharacter(null);
    handleFinishConversation();
  };

  const renderCheckButton = (sentenceCharacterId, activeCharacterId) => {
    const handleCheckRecord = () => {
      stopMicrophone(false);
    };
    const isDisabled = isSendingRecord ||
      recordProcess !== RecordProcessEnum.PROCESS ||
      balanceStatus != StatusEnum.ON;

    const buttonText = activeCharacterId === sentenceCharacterId ? t('learn.btnCheck') : t('learn.continue');
    return (
      <Button
        isDisabled={isDisabled}
        color={'primary'}
        size={'md'}
        onClick={handleCheckRecord}
      >
        {buttonText}
      </Button>
    );
  };

  const renderFinishButton = () => {
    const isDisabled = balanceStatus != StatusEnum.ON ||
      isSendingRecord ||
      (recordProcess !== RecordProcessEnum.FINISH && activeCharacter?.id === sentence?.character_id) ||
      (sentenceProcess !== SentenceProcessEnum.FINISH && activeCharacter?.id !== sentence?.character_id);
    return (
      <Button
        isDisabled={isDisabled}
        color={'primary'}
        size={'md'}
        onClick={handleFinishSpeaking}
        className={'!inline-flex !w-auto'}
      >
        {t('learn.finish')}
      </Button>
    );
  };

  const renderHelpContent = () => {
    return (
      <div className={'flex justify-end w-[150px]'}>
        <span
          className={classNames('inline-block text-[0.625rem] w-[150px] text-right mr-9 mt-2', {
            '!mr-12':
              speakings.length === conversations.length &&
              sentenceProcess === SentenceProcessEnum.FINISH,
          })}
        >
          Enter
        </span>
      </div>
    );
  };

  const isFinishLearnParagraph =
    sentenceProcess === SentenceProcessEnum.FINISH &&
    conversations.length === speakings.length &&
    conversations.length > 0;

  return (
    <div className={'text-right -mb-2'}>
      {isFinishLearnParagraph ?
        renderFinishButton() :
        renderCheckButton(sentence?.character_id, activeCharacter?.id)
      }
      {renderHelpContent()}
    </div>);
};

export default SubmitAction;
