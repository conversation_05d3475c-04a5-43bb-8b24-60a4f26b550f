import { useConversationContext } from '@/providers/ConversationProvider';
import { ConversationTypeEnum } from 'configs/ConversationEnum';

import { VideoLesson } from '../lesson/VideoLesson';
import { ListenConversation } from './ListenConversation';
import { ListenEssay } from './ListenEssay';
import { ListenGallery } from './ListenGallery';

export const ListenContainer = () => {
  const { paragraph } = useConversationContext();

  return (
    <>
      <div className={` flex-1 justify-between flex flex-col px-5  w-full`}>
        {paragraph?.item === ConversationTypeEnum.CONVERSATION && <ListenConversation />}
        {paragraph?.item === ConversationTypeEnum.ESSAY && <ListenEssay />}
        {paragraph?.item === ConversationTypeEnum.GALLERY && <ListenGallery />}
        {paragraph?.item === ConversationTypeEnum.VIDEO && <VideoLesson />}
      </div>
    </>
  );
};
