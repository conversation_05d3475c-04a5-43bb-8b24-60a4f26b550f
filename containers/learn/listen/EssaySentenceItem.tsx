import { SentenceEntity } from '@/types/model';
import classNames from 'classnames';

interface EssaySentenceItemProps {
  sentence: SentenceEntity;
  isActive: boolean;
}

export const EssaySentenceItem = ({ sentence, isActive }: EssaySentenceItemProps) => {
  return (
    <div
      className={classNames('mb-6 p-4 rounded-lg', {
        'bg-bg-box': isActive,
      })}
    >
      <div className="flex items-start gap-4">
        {sentence.character?.avatar && (
          <div className="w-10 h-10 rounded-full overflow-hidden flex-shrink-0">
            <img
              src={sentence.character.avatar}
              alt={sentence.character?.name || ''}
              className="w-full h-full object-cover"
            />
          </div>
        )}
        <div className="flex-1">
          {sentence.character?.name && (
            <div className="font-semibold text-purple mb-2">{sentence.character.name}</div>
          )}
          <p
            className={classNames('text-lg leading-relaxed', {
              'font-medium': isActive,
            })}
          >
            {sentence.content}
          </p>
        </div>
      </div>
    </div>
  );
};
