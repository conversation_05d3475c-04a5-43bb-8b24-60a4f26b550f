'use client';

import { memo } from 'react';

import classNames from 'classnames';
import Avatar from 'components/Avatar';
import { map } from 'lodash';
import useLearnStore from 'store/learn';
import useSpeakingStore from 'store/speaking';
import { ConversationItemProps } from 'types/component';

import PlayAudio from './PlayAudio';

const ConversationItem = ({
  sentences,
  className,
  characters,
  isLastItem,
  currentGroupIndex,
}: ConversationItemProps) => {
  const { showWord, setSelectedSentence } = useLearnStore();
  const { autoReading } = useSpeakingStore();
  const character = characters[currentGroupIndex];
  return (
    <div className="conversation-item">
      <div className={classNames('flex items-end mb-4')}>
        <div
          className={classNames(
            'flex flex-col space-y-1 text-[0.9375rem] mx-2 order-1 items-start'
          )}
        >
          <div className="px-4 text-[0.75rem] text-color-minor">{character?.fullname || ''}</div>
          {sentences &&
            map(sentences, (sentence, index) => (
              <div
                key={index}
                className={classNames(
                  'flex items-center group col-span-6 relative pr-8 max-w-[410px]'
                )}
              >
                <div
                  onClick={() => setSelectedSentence(sentence)}
                  className={classNames(
                    'px-4 order-0 pr-6 cursor-pointer py-1 relative rounded-[23px] inline-block shadow-small text-color-major bg-bg-box',
                    {
                      'rounded-tl-md': index > 0 && sentences.length > 1,
                      'rounded-bl-md': index < sentences.length - 1,
                    }
                  )}
                >
                  <span
                    className={classNames(
                      className,
                      'transition-all ease-in-out duration-1 hover:!opacity-100 font-svn',
                      {
                        '!opacity-100': showWord, // endAudioIds.includes(sentence.id) || isLearned ||
                        // @ts-ignore
                      }
                    )}
                    dangerouslySetInnerHTML={{ __html: sentence.content || '' }}
                  />
                </div>
                {!autoReading && (
                  <PlayAudio
                    key={sentence.id}
                    onFinishAudio={() => {}}
                    audios={sentence.audios}
                    isLeftConversation={true}
                  />
                )}
              </div>
            ))}
        </div>

        <Avatar name={sentences[0].character_id?.toString()} size={32} className={'order-0'} />
      </div>
    </div>
  );
};
export default memo(ConversationItem);
