import { useCallback, useMemo } from 'react';

import { SPECIAL_DOCUMENT_ID } from '@/configs';
import { groupConversationSentences } from '@/helpers/sentence';
import { useConversationContext } from '@/providers/ConversationProvider';
import useLearnStore from '@/store/learn';
import { Divider } from '@heroui/react';
import classNames from 'classnames';

import ScrollArea from '@/components/ScrollArea';

import { useListenGroupSentences } from '@/hooks/Ent/useListenGroupSentences';

import FinishScreen from '../FinishScreen';
import StartScreen from '../StartScreen';
import ConversationControl from './ConversationControl';
import ConversationItem from './ConversationItem';

export const ListenConversation = () => {
  const { exerciseToken } = useLearnStore();
  const {
    paragraph,
    sentences: conversations,
    activeTab,
    isLoadingConversations,
    setActiveTab,
    listenCurrentId,
    characters,
  } = useConversationContext();

  // Memoize groupSentences with stable dependencies to prevent infinite loops
  const groupSentences = useMemo(() => {
    if (!conversations?.length) return [];
    return groupConversationSentences(conversations);
  }, [
    conversations?.length,
    conversations?.map((s) => s.id).join(','), // Stable string representation of sentence IDs
  ]);

  const {
    isFinishLearn,
    handleFinishConversation,
    handleNextSentence,
    isStartedLearn,
    isEndSentences,
    handleRestart,
    handleBackSentence,
    isLastSentence,
    currentGroupIndex,
    handleStartListen,
  } = useListenGroupSentences({
    groupSentences,
    listenCurrentId,
    paragraph,
  });

  // Memoize computed values
  const scrollAreaClassName = useMemo(
    () =>
      classNames('w-full px-[calc((100%_-_615px)_/_2)]', {
        '!h-[calc(100vh-170px)]':
          paragraph?.document_id !== SPECIAL_DOCUMENT_ID && exerciseToken === '',
        '!h-[calc(100vh-129px)]':
          paragraph?.document_id !== SPECIAL_DOCUMENT_ID && exerciseToken !== '',
        '!h-[calc(100vh-127px)]':
          paragraph?.document_id === SPECIAL_DOCUMENT_ID && exerciseToken === '',
        '!h-[calc(100vh-86px)]':
          paragraph?.document_id === SPECIAL_DOCUMENT_ID && exerciseToken !== '',
      }),
    [paragraph?.document_id, exerciseToken]
  );

  const handleDoNewExercise = useCallback(() => {
    setActiveTab(activeTab);
  }, [setActiveTab, activeTab]);

  const groupsToRender = useMemo(() => {
    if (currentGroupIndex < 0) return [];
    return Array.from({ length: currentGroupIndex + 1 }, (_, index) => index);
  }, [currentGroupIndex]);

  if (!isStartedLearn) {
    return (
      <StartScreen
        activeTab={activeTab}
        onStart={handleStartListen}
        setSelectedCharacter={() => {}}
        selectedCharacter={null}
        isLoadingConversation={isLoadingConversations}
      />
    );
  }

  if (isFinishLearn)
    return (
      <FinishScreen
        handleRestart={handleRestart}
        handleDoNewExercise={handleDoNewExercise}
        isExercise={false}
      />
    );

  return (
    <>
      <ScrollArea className={scrollAreaClassName} isEnabled>
        <div
          className={classNames(
            'flex flex-col font-[Helvetica] font-normal space-y-4 relative overflow-hidden pt-8 pr-8',
            {
              'justify-center': isFinishLearn,
            }
          )}
        >
          {/* Display groups from 0 to currentGroupIndex (inclusive) */}
          {/* If currentGroupIndex is -1, don't display any groups */}
          {groupsToRender.map((groupIndex) => {
            if (!groupSentences[groupIndex]) return null;
            return (
              <ConversationItem
                characters={characters}
                currentGroupIndex={groupIndex}
                key={groupIndex}
                sentences={groupSentences[groupIndex]}
                isLastItem={groupIndex === groupSentences.length - 1}
                className={classNames({
                  'opacity-80': groupSentences.length - 3 === groupIndex,
                  'opacity-60': groupSentences.length - 2 === groupIndex,
                  'opacity-10': groupSentences.length - 1 === groupIndex,
                })}
              />
            );
          })}
        </div>
      </ScrollArea>
      <Divider orientation={'horizontal'} className={'absolute bottom-[71px] bg-color-line'} />

      <div
        className={`absolute bottom-0 left-0 right-0 bg-bg-general block w-full px-[calc((100%_-_615px)_/_2)]`}
      >
        <ConversationControl
          isLastSentence={isLastSentence}
          isFinishConversation={isFinishLearn}
          endSentence={isEndSentences}
          onFinish={handleFinishConversation}
          onNextSentence={handleNextSentence}
          onLeanAgain={handleRestart}
          onBackSentence={handleBackSentence}
        />
      </div>
    </>
  );
};
