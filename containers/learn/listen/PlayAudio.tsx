'use client';

import { useEffect, useState } from 'react';

import classNames from 'classnames';
import { SentenceProcessEnum } from 'configs/SentenceProcessEnum';
import { useGlobalAudioPlayer } from 'react-use-audio-player';
import useSpeakingStore from 'store/speaking';
import { PlayAudioProps } from 'types/component';
import { AudioEntity } from 'types/model';

const PlayAudio = ({ audios, onFinishAudio, isLeftConversation }: PlayAudioProps) => {
  const [audio, setAudio] = useState<AudioEntity>();
  const { volume, autoReading, setSentenceProcess } = useSpeakingStore();
  const { load, setVolume } = useGlobalAudioPlayer();

  useEffect(() => {
    setVolume(volume / 100);
  }, [volume]);

  let timeout: NodeJS.Timeout | null = null;

  useEffect(() => {
    if (audios && audios.length) {
      setAudio(audios[0]);
    }
  }, [audios]);

  const playAudio = () => {
    if (autoReading) return;
    setSentenceProcess(SentenceProcessEnum.PROCESS);
    if (audio && audio.url) {
      load(audio.url, {
        autoplay: true,
        initialVolume: volume / 100,
      });
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(
        () => {
          onFinishAudio(audio);
        },
        audio.duration * 1000 + 300
      );
    }
  };
  return (
    <div
      className={classNames('hidden absolute right-0 items-center group-hover:flex', {
        'ml-2': isLeftConversation,
        'mr-2 rotate-180': !isLeftConversation,
      })}
    >
      <div
        className={
          'w-6 h-6 flex items-center cursor-pointer justify-center rounded-full hover:bg-bg-box'
        }
      >
        <span onClick={playAudio}>
          <i className={'icon-volume-up text-small'} />
        </span>
      </div>
    </div>
  );
};

export default PlayAudio;
