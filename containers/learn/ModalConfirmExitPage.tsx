'use client';

import React, { memo, useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import LearnTypeEnum from '@/configs/LearnTypeEnum';
import useLearnStore from '@/store/learn';
import { useTranslations } from 'next-intl';

import Modal from '@/components/Modal';

interface ModalConfirmExitPageProps {
  openModalConfirmQuit: boolean;
  setOpenModalConfirmQuit: (confirm: boolean) => void;
  onConfirm: (confirm: boolean) => void;
  activeTab: LearnTypeEnum;
}

const ModalConfirmExitPage: React.FC<ModalConfirmExitPageProps> = ({
  openModalConfirmQuit,
  setOpenModalConfirmQuit,
  onConfirm,
  activeTab,
}) => {
  const t = useTranslations();
  const [isOpen, setOpen] = useState(false);
  const [nextRouterPath, setNextRouterPath] = useState<string | null>(null);
  const router = useRouter();
  const { setActiveTab } = useLearnStore();

  useEffect(() => {
    if (openModalConfirmQuit) setOpen(true);
    setOpenModalConfirmQuit(false);
  }, [openModalConfirmQuit]);
  const cancelQuit = () => {
    setNextRouterPath(null);
    setOpen(false);
    setActiveTab(activeTab);
  };

  const confirmQuit = () => {
    setOpen(false);
    onConfirm(true);
    if (nextRouterPath) router.push(nextRouterPath);
  };

  useEffect(() => {
    // const handler = () => {
    //   // if (!window.confirm('Nghỉ à')) {
    //   //   throw "Route Canceled";
    //   // }
    // };
    // router.events.on('routeChangeStart', handler);
    return () => {
      // setOpenModalConfirmQuit(true);
      // router.events.off('routeChangeStart', handler);
    };
  }, [router]);

  return (
    <Modal
      opened={isOpen}
      onOpenChange={setOpen}
      size={'xs'}
      onClose={cancelQuit}
      onSubmit={confirmQuit}
      submitLabel={t('learn.modal.confirmExit.btnConfirm')}
      header={<h4 className={'text-sm mt-1'}>{t('learn.modal.confirmExit.heading')}</h4>}
      classNames={{
        footer: 'px-4',
      }}
    >
      <div className="flex w-full flex-col items-center">
        <p>{t('learn.modal.confirmExit.content')}</p>
      </div>
    </Modal>
  );
};

export default memo(ModalConfirmExitPage);
