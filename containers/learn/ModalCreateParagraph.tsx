'use client';

import { Dispatch, SetStateAction, useMemo, useState } from 'react';

import { useRouter } from 'next/navigation';

import { Input } from '@/components';
import { ConversationTypeEnum } from '@/configs/ConversationEnum';
import { useConversationContext } from '@/providers/ConversationProvider';
import { Select, SelectItem, Textarea } from '@heroui/react';
import { AxiosError } from 'axios';
import Modal from 'components/Modal';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import { useCreateParagraphMutation } from '@/hooks/Ent/useCreateParagraph';

const ModalCreateParagraph = ({
  open,
  setOpen,
  documentId,
  courseId,
}: {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  documentId: number;
  courseId: number;
}) => {
  const router = useRouter();
  const t = useTranslations();
  const [title, setTitle] = useState<string>('');
  const options = useMemo(
    () =>
      Object.values(ConversationTypeEnum).map((value) => ({
        key: value,
        label: t(`${value}.title`),
      })),
    []
  );

  const { paragraphs } = useConversationContext();

  const [selected, setSelected] = useState<string>(options[0].key);
  const [description, setDescription] = useState<string>('');
  const [error, setError] = useState<boolean>(false);

  const createParagraphMutation = useCreateParagraphMutation();

  const handleCreateParagraph = async () => {
    try {
      if (!documentId || !courseId) {
        toast.error('Not found document or course');
        return;
      }
      if (!title || !selected) {
        toast.error('Not found title or selected');
        setError(true);
        return;
      }
      const response = await createParagraphMutation.mutateAsync({
        document_id: documentId,
        course_id: courseId,
        title: title,
        item: selected,
        description,
        pre_paragraph_id:
          paragraphs && paragraphs.length > 0 ? paragraphs[paragraphs.length - 1].id : 0,
      });
      toast.success(t('message.success.create_paragraph'));
      setError(false);
      setOpen(false);
      router.push(`/learn/${response.data?.data.id}`);
    } catch (e: unknown) {
      console.log(e);
      setError(false);
      toast.error(e instanceof AxiosError ? e.response?.data?.message : t('message.error.unknown'));
    }
  };
  return (
    <Modal
      size={'lg'}
      buttonSize={'md'}
      classNames={{
        header: 'py-2',
      }}
      scrollBehavior={'inside'}
      submitLabel={t('learn.createParagraph')}
      onSubmit={handleCreateParagraph}
      onOpenChange={setOpen}
      opened={open}
      cancelLabel={t('gallery.cancel')}
      header={<div className={'text-lg font-semibold'}>{t('learn.createParagraph')}</div>}
      onClose={() => {
        setError(false);
        setOpen(false);
      }}
      disabledButton={createParagraphMutation.isPending || error}
    >
      <div className={'flex flex-col gap-6 items-baseline '}>
        <Input
          isRequired
          className={'text-small pt-2'}
          classNames={{
            input: 'text-small',
            innerWrapper: 'pb-0',
            inputWrapper: '',
            label: 'text-sm font-medium',
          }}
          value={title}
          onValueChange={(e) => {
            setError(false);
            setTitle(e);
          }}
          removeLabel={false}
          labelPlacement={'outside'}
          placeholder={' '}
          label={t('learn.modal.createParagraph.title')}
          required
          errorMessage={t('learn.modal.createParagraph.error.title')}
          isInvalid={error}
        />
        <Select
          isRequired
          className="w-full"
          classNames={{
            trigger: 'bg-bg-box',
            label: 'text-sm font-medium',
            value: 'text-sm font-medium',
          }}
          size={'lg'}
          radius={'sm'}
          onChange={(e) => {
            setError(false);
            setSelected(e.target.value);
          }}
          label={t('learn.modal.createParagraph.type')}
          labelPlacement={'outside'}
          placeholder={t('learn.modal.createParagraph.error.type')}
          required
          errorMessage={t('learn.modal.createParagraph.error.type')}
          isInvalid={error}
        >
          {options.map((option) => (
            <SelectItem key={option.key}>{option.label}</SelectItem>
          ))}
        </Select>
        <Textarea
          className="w-full"
          label={t('learn.modal.createParagraph.description')}
          labelPlacement="outside"
          placeholder={t('learn.modal.createParagraph.error.description')}
          value={description}
          onValueChange={(e) => {
            setError(false);
            setDescription(e);
          }}
          classNames={{
            input: 'text-small',
            innerWrapper: 'pb-0',
            inputWrapper: 'bg-bg-box',
            label: 'text-sm font-medium',
          }}
        />
      </div>
    </Modal>
  );
};
export default ModalCreateParagraph;
