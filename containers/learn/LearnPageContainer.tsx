'use client';

import Head from 'next/head';

import LearnContainerSkeleton from '@/containers/learn/skeleton/LearnContainerSkeleton';
import { useConversationContext } from '@/providers/ConversationProvider';
// import HeaderLearn from 'containers/learn/HeaderLearn';


import LearnContainer from './LearnContainer';

const LearnPageContainer = () => {
  // Determine loading state

  const { paragraph, isLoading } = useConversationContext();

  return (
    <>
      <Head>
        <title>Học bài {paragraph?.title ? ` - ${paragraph.title}` : ''}</title>
      </Head>
      <div className="flex flex-col min-h-screen">
        {isLoading ? (
          <div className="relative">
            <LearnContainerSkeleton />
          </div>
        ) : (
          <>
            {/* <HeaderLearn /> */}
            <LearnContainer />
          </>
        )}
      </div>
    </>
  );
};

export default LearnPageContainer;
