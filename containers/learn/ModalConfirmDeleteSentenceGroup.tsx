'use client';

import React from 'react';

import { useTranslations } from 'next-intl';

import Modal from '@/components/Modal';

type ModalConfirmDeleteSentenceGroupProps = {
  openAction: boolean;
  setOpenAction: React.Dispatch<React.SetStateAction<boolean>>;
  onSubmitAction: () => void;
};

export const ModalConfirmDeleteSentenceGroup = ({
  openAction,
  setOpenAction,
  onSubmitAction,
}: ModalConfirmDeleteSentenceGroupProps) => {
  const t = useTranslations();
  return (
    <Modal
      opened={openAction}
      onOpenChange={setOpenAction}
      size={'xs'}
      onClose={() => setOpenAction(false)}
      onSubmit={onSubmitAction}
      submitLabel={t('learn.modal.confirmExit.btnConfirm')}
      header={<h4 className={'text-sm mt-1'}>{t('gallery.delete.title')}</h4>}
      classNames={{
        footer: 'px-4',
      }}
    >
      <div className="flex w-full">
        <p>{t('gallery.delete.content')}</p>
      </div>
    </Modal>
  );
};
