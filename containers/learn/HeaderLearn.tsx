'use client';

import React, { useMemo } from 'react';

import { useRouter } from 'next/navigation';

import { AccessEnum } from '@/configs/CanAccess';
import { StatusEnum } from '@/configs/StatusEnum';
import { useConversationContext } from '@/providers/ConversationProvider';
import { checkAccessOrOwner } from '@/utils/checkAccess';
import Select from 'components/Select';
import Tabs from 'components/Tabs';
import EntRouters from 'configs/EntRouters';
// import AppHeader from 'containers/layout/AppHeader';
import useLearnStore from 'store/learn';

import { useSession } from '@/hooks/useSession';

const HeaderLearn = () => {
  const router = useRouter();
  const { data: session } = useSession();
  const { exerciseToken } = useLearnStore();
  const { documents, activeDocument, paragraph } = useConversationContext();

  // Build paragraph tabs
  const paragraphTabs = useMemo(() => {
    if (!activeDocument || !paragraph) return [];

    return activeDocument.paragraphs
      .filter(
        (item) =>
          checkAccessOrOwner(session, AccessEnum.LEARN_APPROVE, item.account_id) ||
          item.process_approve === StatusEnum.ON
      )
      .map((item, index) => ({
        id: item.keyx,
        title: index + 1,
        label: `${item.id} - ${item.title}`,
      }));
  }, [activeDocument, paragraph]);

  const handleChangeDocument = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const documentId = e.target.value ?? 0;
    router.push(`${EntRouters.learn_redirect}/${documentId}`);
  };

  const handleChangeParagraph = (paragraphToken: string) => {
    router.push(`${EntRouters.learn}/${paragraphToken}`);
  };

  return (
    <>
      {exerciseToken === '' ? (
        <div className="pl-[30px] lg:pl-0 h-[53px] w-full grid grid-cols-12 items-center gap-3">
          <div className="col-span-6 flex items-center">
            {documents && documents.length > 0 ? (
              <Select
                label={activeDocument?.title || ''}
                sectionLabel={activeDocument?.title || ''}
                selectedKeys={[activeDocument?.id.toString() ?? '']}
                items={documents}
                onChange={handleChangeDocument}
                itemIcon={<i className="w-4 h-4 mr-1 icon-document" />}
                startContent={<i className="w-4 h-4 mr-1 icon-document" />}
                classNames={{
                  base: 'h-[29px] w-72 mr-4',
                  trigger: 'h-[29px] min-h-7 w-72',
                }}
              />
            ) : (
              <div className="mr-2">{activeDocument?.title ?? ''}</div>
            )}

            <div className="h-[25px] rounded-[5px] w-auto gap-x-5 inline-flex items-center">
              {paragraphTabs.length > 0 && (
                <Tabs
                  tabs={paragraphTabs}
                  classNames={{
                    tab: 'px-[9px] justify-center',
                  }}
                  key={paragraph?.id ?? 0}
                  activeTabId={paragraph?.keyx}
                  onSelectionChange={handleChangeParagraph}
                  showTooltip={true}
                />
              )}

            </div>
          </div>
        </div>
      ) : (
        <div className={'pb-3'}></div>
      )}
    </>
  );
};

export default HeaderLearn;
