import React, { Dispatch, SetStateAction } from 'react';

import { decodeHtmlEntities } from '@/helpers';
import { Textarea } from '@heroui/react';
import Modal from 'components/Modal';
import { useTranslations } from 'next-intl';

const ModalEditGalleryContent = ({
  open,
  setOpen,
  newContent,
  setNewContent,
  handleSaveGallery,
  isLoading,
}: {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  newContent: string;
  setNewContent: Dispatch<SetStateAction<string>>;
  handleSaveGallery: () => void;
  isLoading: boolean;
}) => {
  const t = useTranslations();

  return (
    <Modal
      size={'lg'}
      buttonSize={'md'}
      classNames={{
        header: 'py-2',
        base: 'max-w-2xl',
      }}
      scrollBehavior={'inside'}
      submitLabel={t('gallery.save')}
      onSubmit={handleSaveGallery}
      onOpenChange={setOpen}
      opened={open}
      cancelLabel={t('gallery.cancel')}
      header={<div className={'text-lg font-semibold'}>{t('gallery.form.edit_content')}</div>}
      onClose={() => {
        setOpen(false);
      }}
      disabledButton={isLoading}
    >
      <div className={'flex flex-col gap-6 items-baseline mb-2.5'}>
        <Textarea
          className="w-full"
          placeholder={t('gallery.form.edit_content_placeholder')}
          value={decodeHtmlEntities(newContent)}
          classNames={{
            input: 'text-small',
            innerWrapper: 'pb-0',
            inputWrapper: 'bg-bg-box',
            label: 'text-sm font-medium',
          }}
          onChange={(e) => setNewContent(e.target.value)}
        />
      </div>
    </Modal>
  );
};
export default ModalEditGalleryContent;
