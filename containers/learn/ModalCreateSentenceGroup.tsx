'use client';

import { Dispatch, SetStateAction, useState } from 'react';

import NextImage from 'next/image';

import { UploadImage } from '@/containers/gallery/UploadImage';
import { ParagraphEntity } from '@/types/model';
import { Textarea } from '@heroui/react';
import { AxiosError } from 'axios';
import Modal from 'components/Modal';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import { useCreateSentenceGroupMutation } from '@/hooks/Ent/useGallery';

const ModalCreateSentenceGroup = ({
  open,
  setOpen,
  activeParagraph,
  prevSentenceGroupId,
}: {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  activeParagraph: ParagraphEntity;
  prevSentenceGroupId: number;
}) => {
  console.log('🚀 ~ prevSentenceGroupId:', prevSentenceGroupId);
  const t = useTranslations();

  const [content, setContent] = useState<string>('');
  const [error, setError] = useState<boolean>(false);
  const [file, setFile] = useState<File | null>(null);

  const handleChange = (file: File) => {
    setError(false);
    setFile(file);
  };

  const onDelete = () => {
    setError(false);
    setFile(null);
  };

  const createSentenceGroupMutation = useCreateSentenceGroupMutation();

  const handleCreateGallery = async () => {
    try {
      if (!file || !content) {
        toast.error(t('message.error.gallery.empty_content'));
        return;
      }
      await createSentenceGroupMutation.mutateAsync({
        pre_sentence_group_id: prevSentenceGroupId.toString(),
        course_id: activeParagraph?.course_id?.toString() || '',
        paragraph_id: activeParagraph?.id?.toString() || '',
        document_id: activeParagraph?.document_id?.toString() || '',
        file: file,
        content: content,
      });
      toast.success(t('message.success.create_gallery'));
      setFile(null);
      setContent('');
      setOpen(false);
    } catch (e: unknown) {
      console.log(e);
      toast.error(e instanceof AxiosError ? e.response?.data?.message : t('message.error.unknown'));
    }
  };
  return (
    <Modal
      size={'3xl'}
      buttonSize={'md'}
      classNames={{
        header: 'py-2',
      }}
      scrollBehavior={'inside'}
      submitLabel={t('gallery.save')}
      onSubmit={handleCreateGallery}
      onOpenChange={setOpen}
      opened={open}
      cancelLabel={t('gallery.cancel')}
      header={<div className={'text-lg font-semibold'}>{t('gallery.create_gallery')}</div>}
      onClose={() => {
        setError(false);
        setOpen(false);
      }}
      disabledButton={createSentenceGroupMutation.isPending || error}
    >
      <div className={'flex flex-col gap-6 items-baseline'}>
        <div
          className={
            'relative w-full aspect-video bg-bg-box rounded-[5px] flex items-center justify-center'
          }
        >
          {!file && <UploadImage handleChange={handleChange} type={'edit'} />}
          {file && file instanceof File && (
            <div className={'relative w-full h-full rounded-[5px]'}>
              <NextImage src={URL.createObjectURL(file)} alt={''} fill objectFit={'contain'} />
              <div
                onClick={() => onDelete()}
                className={
                  'w-5 h-5 cursor-pointer absolute top-2.5 right-2.5 opacity-40 rounded-full flex items-center justify-center'
                }
              >
                <i className={'w-4 h-4 icon-close text-lg text-color-major'} />
              </div>
            </div>
          )}
        </div>
        <Textarea
          className="w-full"
          labelPlacement="outside"
          placeholder={t('gallery.form.edit_content_placeholder')}
          value={content}
          onValueChange={(e) => {
            setError(false);
            setContent(e);
          }}
          classNames={{
            input: 'text-small',
            innerWrapper: 'pb-0',
            inputWrapper: 'bg-bg-box',
            label: 'text-sm font-medium',
          }}
        />
      </div>
    </Modal>
  );
};
export default ModalCreateSentenceGroup;
