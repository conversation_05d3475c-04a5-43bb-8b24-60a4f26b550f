'use client';

import classNames from 'classnames';
import { map } from 'lodash';
import useLearnStore from 'store/learn';
import { ApproveEssayItemProps } from 'types/component';

export const EssayLessonItem = ({ sentences }: ApproveEssayItemProps) => {
  const { setSelectedSentence } = useLearnStore();
  return (
    <>
      <p className={classNames('space-y-1 text-[0.9375rem] order-1 items-start max-w-[510px]')}>
        {sentences &&
          map(sentences, (sentence) => (
            <span
              key={sentence.id}
              onClick={() => setSelectedSentence(sentence)}
              className={classNames(
                'rounded-sm pr-1 first:-ml-0 cursor-pointer col-span-6 relative transition-all ease-in-out duration-1 hover:bg-[#6e79d67d] font-svn'
              )}
            >
              <span
                className=""
                /*// @ts-ignore*/
                dangerouslySetInnerHTML={{ __html: sentence.content }}
              />
            </span>
          ))}
      </p>
    </>
  );
};
