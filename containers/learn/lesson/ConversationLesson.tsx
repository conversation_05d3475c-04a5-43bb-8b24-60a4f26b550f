import { SPECIAL_DOCUMENT_ID } from '@/configs';
import { groupConversationSentences } from '@/helpers/sentence';
import { useConversationContext } from '@/providers/ConversationProvider';
import classNames from 'classnames';

import ScrollArea from '@/components/ScrollArea';

import { ConversationLessonItem } from './ConversationLessonItem';

export const ConversationLesson = () => {
  const { paragraph, isLoading, characters, sentences } = useConversationContext();

  const groupsSentences = groupConversationSentences(sentences);

  return (
    <ScrollArea
      className={classNames('w-full px-[calc((100%_-_615px)_/_2)]', {
        'h-[calc(100vh-100px)]': paragraph?.document_id !== SPECIAL_DOCUMENT_ID,
        'h-[calc(100vh-55px)]': paragraph?.document_id === SPECIAL_DOCUMENT_ID,
      })}
      isEnabled
    >
      <div
        className={classNames(
          'flex flex-col font-[Helvetica] font-normal space-y-4 relative pt-8 pr-8',
          {}
        )}
      >
        {!isLoading &&
          groupsSentences.map((sentences, index) => {
            return (
              <ConversationLessonItem key={index} characters={characters} sentences={sentences} />
            );
          })}
      </div>
    </ScrollArea>
  );
};
