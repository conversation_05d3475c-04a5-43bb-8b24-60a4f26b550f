'use client';

import { SentenceEntity } from '@/types/model';
import classNames from 'classnames';
import { map } from 'lodash';
import useLearnStore from 'store/learn';

export const GalleryLessonItem = ({ sentences }: { sentences: SentenceEntity[] }) => {
  const { setSelectedSentence } = useLearnStore();
  return (
    <div className={'w-full h-full'}>
      <div
        className={classNames(
          'space-y-1 text-xl text-white items-center font-semibold flex flex-col w-full',
          {}
        )}
        style={{ textShadow: ' #000000 0px 0px 7px' }}
      >
        {sentences &&
          map(sentences, (sentence, index) => (
            <span
              key={index}
              onClick={() => setSelectedSentence(sentence)}
              className={classNames(
                'rounded-sm pr-1 first:-ml-0 cursor-pointer text-center col-span-6 relative transition-all ease-in-out duration-1 font-svn'
              )}
            >
              <span className="" dangerouslySetInnerHTML={{ __html: sentence?.content || '' }} />
            </span>
          ))}
      </div>
    </div>
  );
};
