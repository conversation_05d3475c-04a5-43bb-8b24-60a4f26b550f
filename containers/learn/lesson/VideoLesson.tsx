import { useCallback, useEffect, useRef, useState } from 'react';

import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { StatusEnum } from '@/configs/StatusEnum';
import { useConversationContext } from '@/providers/ConversationProvider';
import useBalanceStore from '@/store/balance';
import useLearnStore from '@/store/learn';
import useSpeakingStore from '@/store/speaking';
import ReactPlayer from 'react-player/youtube';

import {
  useEndListenMutation,
  useSaveCurrentSentenceMutation,
  useStartListenMutation,
} from '@/hooks/Ent/useSentence';
import { useSession } from '@/hooks/useSession';

import FinishScreen from '../FinishScreen';
import StartScreen from '../StartScreen';
import ConversationControl from '../listen/ConversationControl';

const VIDEO_DURATION = 120; // 120 seconds total
const SENTENCE_DURATION = 10; // 10 seconds per sentence

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const Timeline = ({
  sentences,
  currentIndex,
  currentTime,
  onSeek,
}: {
  sentences: any[];
  currentIndex: number;
  currentTime: number;
  onSeek: (index: number) => void;
}) => {
  return (
    <div className="absolute bottom-0 left-0 right-0">
      {/* Progress bar */}
      <div className="h-1 bg-gray-800 relative">
        <div
          className="absolute h-full bg-purple transition-all duration-300"
          style={{ width: `${(currentTime / VIDEO_DURATION) * 100}%` }}
        />
      </div>

      {/* Timeline segments */}
      {/* <div className="h-2 bg-gray-800/50">
        {sentences.map((sentence, index) => (
          <div
            key={sentence.id}
            className={classNames('absolute h-full transition-all duration-300 cursor-pointer', {
              'bg-purple/30': index === currentIndex,
              'bg-gray-600/30': index !== currentIndex,
            })}
            style={{
              left: `${(index * 100) / sentences.length}%`,
              width: `${100 / sentences.length}%`,
            }}
            onClick={() => onSeek(index)}
          />
        ))}
      </div> */}

      {/* Time display */}
      <div className="absolute bottom-4 right-4 bg-black/70 text-white px-2 py-1 rounded text-sm">
        {formatTime(currentTime)} / {formatTime(VIDEO_DURATION)}
      </div>
    </div>
  );
};

const SentenceDisplay = ({ sentence }: { sentence: any }) => {
  if (!sentence) return null;

  return (
    <div className="absolute left-1/2 -translate-x-1/2 bottom-1 bg-black/50 p-6 rounded-lg text-white max-w-2xl w-full">
      <div className="flex items-center gap-2 mb-2 justify-center">
        <span className="text-3xl">{sentence.emoji}</span>
        <span className="text-xl font-medium">{sentence.content}</span>
      </div>
      <div className="text-base text-center text-gray-300">
        {sentence.translation?.translate_google}
      </div>
    </div>
  );
};

export const VideoLesson = () => {
  const { paragraph, sentences, activeTab, setActiveTab, isLoadingConversations } =
    useConversationContext();
  const { exerciseToken, transactionInfo } = useLearnStore();
  const { balanceStatus } = useBalanceStore();
  const { data: session } = useSession();
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState<number>(0);
  const [isFinished, setIsFinished] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [isStartedLearn, setIsStartedLearn] = useState<boolean>(false);
  const [playedSentences, setPlayedSentences] = useState<Set<number>>(new Set());
  const playerRef = useRef<ReactPlayer>(null);
  const { autoReading, setAutoReading, replay } = useSpeakingStore();
  const timerRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const startListenMutation = useStartListenMutation();
  const endListenMutation = useEndListenMutation();
  const saveCurrentSentenceMutation = useSaveCurrentSentenceMutation();

  const currentSentence = sentences[currentSentenceIndex];

  // Handle content timing
  useEffect(() => {
    if (isPlaying) {
      timerRef.current = setInterval(() => {
        setCurrentTime((prev) => {
          const newTime = prev + 1;

          // Update sentence index based on time
          const newIndex = Math.floor(newTime / SENTENCE_DURATION);
          if (newIndex !== currentSentenceIndex && newIndex < sentences.length) {
            setCurrentSentenceIndex(newIndex);

            // Save current sentence if it hasn't been played before
            const currentSentence = sentences[newIndex];
            if (currentSentence && !playedSentences.has(currentSentence.id)) {
              saveCurrentSentenceMutation.mutate({
                document_id: paragraph?.document_id || 0,
                paragraph_id: paragraph?.id || 0,
                sentence_ids: [currentSentence.id],
                member_id: session?.user.id,
                member_exercise_token: exerciseToken,
              });
              setPlayedSentences((prev) => new Set([...prev, currentSentence.id]));
            }
          }

          // Handle end of content
          if (newTime >= VIDEO_DURATION) {
            clearInterval(timerRef.current);
            setIsPlaying(false);
            if (replay) {
              setCurrentTime(0);
              setCurrentSentenceIndex(0);
              // Remove auto-playing on loop
              // setIsPlaying(true);
            } else {
              setIsFinished(true);
            }
            return 0;
          }

          return newTime;
        });
      }, 1000);
    } else {
      clearInterval(timerRef.current);
    }

    return () => {
      clearInterval(timerRef.current);
    };
  }, [
    isPlaying,
    currentSentenceIndex,
    sentences,
    paragraph,
    session,
    exerciseToken,
    saveCurrentSentenceMutation,
    playedSentences,
    replay,
  ]);

  // Handle auto-reading state changes
  useEffect(() => {
    if (autoReading) {
      setIsPlaying(true);
    } else {
      setIsPlaying(false);
    }
  }, [autoReading]);

  const handleSeek = useCallback(
    (index: number) => {
      setCurrentSentenceIndex(index);
      const seekTime = index * SENTENCE_DURATION;
      setCurrentTime(seekTime);
      if (autoReading) {
        setIsPlaying(true);
      }
    },
    [autoReading]
  );

  const handleNextSentence = useCallback(() => {
    if (currentSentenceIndex < sentences.length - 1) {
      // Save current sentence if it hasn't been played before
      const currentSentence = sentences[currentSentenceIndex];
      if (currentSentence && !playedSentences.has(currentSentence.id)) {
        saveCurrentSentenceMutation.mutate({
          document_id: paragraph?.document_id || 0,
          paragraph_id: paragraph?.id || 0,
          sentence_ids: [currentSentence.id],
          member_id: session?.user.id,
          member_exercise_token: exerciseToken,
        });
        setPlayedSentences((prev) => new Set([...prev, currentSentence.id]));
      }

      setCurrentSentenceIndex((prev) => prev + 1);
      const nextTime = (currentSentenceIndex + 1) * SENTENCE_DURATION;
      setCurrentTime(nextTime);
      if (autoReading) {
        setIsPlaying(true);
      }
    }
  }, [
    currentSentenceIndex,
    sentences,
    paragraph,
    session,
    exerciseToken,
    saveCurrentSentenceMutation,
    playedSentences,
    autoReading,
  ]);

  const handleBackSentence = useCallback(() => {
    if (currentSentenceIndex > 0) {
      setCurrentSentenceIndex((prev) => prev - 1);
      const prevTime = (currentSentenceIndex - 1) * SENTENCE_DURATION;
      setCurrentTime(prevTime);
      if (autoReading) {
        setIsPlaying(true);
      }
    }
  }, [currentSentenceIndex, autoReading]);

  const handleFinish = useCallback(() => {
    if (replay) {
      setCurrentSentenceIndex(0);
      setCurrentTime(0);
      setIsFinished(false);
      setIsPlaying(false);
      return;
    }

    if (transactionInfo) {
      endListenMutation.mutate({
        transactionInfo: transactionInfo,
      });
    }
    setIsFinished(true);
    setIsPlaying(false);
    setCurrentTime(VIDEO_DURATION);
  }, [replay, transactionInfo, endListenMutation]);

  const handleLearnAgain = useCallback(() => {
    setCurrentSentenceIndex(0);
    setIsFinished(false);
    setCurrentTime(0);
    setIsPlaying(false);
  }, []);

  const handleStartLearn = useCallback(() => {
    if (!paragraph || balanceStatus !== StatusEnum.ON) return;
    setIsStartedLearn(true);
    startListenMutation.mutate({
      document_id: paragraph?.document_id || 0,
      paragraph_id: paragraph?.id || 0,
      course_id: paragraph?.course_id || 0,
      activeTab: LearnTypeEnum.LISTEN,
      member_exercise_token: exerciseToken || '',
    });
  }, [paragraph, balanceStatus, exerciseToken, startListenMutation]);

  const handleRestart = useCallback(() => {
    setCurrentSentenceIndex(0);
    setIsFinished(false);
    setCurrentTime(0);
    setIsStartedLearn(false);
    setIsPlaying(false);
    setAutoReading(false);
  }, []);

  useEffect(() => {
    if (currentTime === 0 && replay) {
      setAutoReading(false);
    }
    // Only run when currentTime or replay changes
  }, [currentTime, replay, setAutoReading]);

  if (!isStartedLearn) {
    return (
      <StartScreen
        activeTab={activeTab}
        onStart={handleStartLearn}
        setSelectedCharacter={() => {}}
        selectedCharacter={null}
        isLoadingConversation={isLoadingConversations}
      />
    );
  }

  if (isFinished) {
    return (
      <FinishScreen
        handleRestart={handleRestart}
        handleDoNewExercise={() => setActiveTab(LearnTypeEnum.EXERCISE)}
        isExercise={false}
      />
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh_-120px)] w-full overflow-hidden pr-8">
      <div className="flex-1 w-full flex justify-center items-center bg-[#2a2d3c] rounded-[5px] min-h-0">
        <div className="w-full h-full flex flex-col items-center justify-center relative">
          <div className="flex-1 w-full flex items-center justify-center">
            <ReactPlayer
              ref={playerRef}
              url={'https://www.youtube.com/watch?v=82OhhGnenxw'}
              controls={false}
              width="100%"
              height="70%"
              playing={isPlaying}
              style={{
                pointerEvents: 'none',
              }}
              config={{
                playerVars: {
                  showinfo: 0,
                  modestbranding: 1,
                  controls: 0,
                  rel: 0,
                  iv_load_policy: 3,
                },
              }}
            />
          </div>
          <Timeline
            sentences={sentences}
            currentIndex={currentSentenceIndex}
            currentTime={currentTime}
            onSeek={handleSeek}
          />
          <div className="flex-shrink-0 w-full">
            <SentenceDisplay sentence={currentSentence} />
          </div>
        </div>
      </div>

      {sentences && sentences.length > 0 && (
        <div className="flex-shrink-0 w-full p-2">
          <div className="w-full">
            <ConversationControl
              onNextSentence={handleNextSentence}
              endSentence={true}
              isFinishConversation={isFinished}
              onFinish={handleFinish}
              isLastSentence={currentSentenceIndex === sentences.length - 1}
              onLeanAgain={handleLearnAgain}
              onBackSentence={handleBackSentence}
            />
          </div>
        </div>
      )}
    </div>
  );
};
