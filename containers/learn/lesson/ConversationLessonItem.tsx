'use client';

import { useMemo } from 'react';

import classNames from 'classnames';
import Avatar from 'components/Avatar';
import { map } from 'lodash';
import useLearnStore from 'store/learn';
import { ApproveConversationItemProps } from 'types/component';

import PlayAudio from '../listen/PlayAudio';

export const ConversationLessonItem = ({ sentences, characters }: ApproveConversationItemProps) => {
  const { setSelectedSentence } = useLearnStore();
  const character = useMemo(() => {
    if (!sentences[0].character_id) return null;
    return characters.find((character) => character.id === sentences[0].character_id);
  }, [sentences[0].character_id, characters]);

  return (
    <div className="conversation-item">
      <div className={classNames('flex items-end mb-4')}>
        <div
          className={classNames(
            'flex flex-col space-y-1 text-[0.9375rem] mx-2 order-1 items-start'
          )}
        >
          <div className="px-4 text-[0.75rem] text-color-minor">{character?.fullname || ''}</div>
          {sentences &&
            map(sentences, (sentence, index) => (
              <div
                key={index}
                className={classNames(
                  'flex items-center group col-span-6 relative pr-8 max-w-[410px]'
                )}
              >
                <div
                  onClick={() => setSelectedSentence(sentence)}
                  className={classNames(
                    'px-4 order-0 pr-6 cursor-pointer py-1 relative rounded-[23px] inline-block shadow-small text-color-major bg-bg-box',
                    {
                      'rounded-tl-md': index > 0 && sentences.length > 1,
                      'rounded-bl-md': index < sentences.length - 1,
                    }
                  )}
                >
                  <span
                    className={classNames(
                      'transition-all ease-in-out duration-1 hover:!opacity-100 font-svn'
                    )}
                    dangerouslySetInnerHTML={{ __html: sentence?.content || '' }}
                  />
                </div>
                <PlayAudio
                  key={sentence.id}
                  onFinishAudio={() => {}}
                  audios={sentence.audios}
                  isLeftConversation={true}
                />
              </div>
            ))}
        </div>

        {/* <Avatar name={sentences[0].character_id?.toString()} size={32} className={'order-0'} /> */}
        <Avatar name={character?.fullname || ''} size={32} className={'order-0'} />
      </div>
    </div>
  );
};
