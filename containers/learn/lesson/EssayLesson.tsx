import { SPECIAL_DOCUMENT_ID } from '@/configs';
import { groupSentencesByKey } from '@/helpers/sentence';
import { useConversationContext } from '@/providers/ConversationProvider';
import classNames from 'classnames';

import ScrollArea from '@/components/ScrollArea';

import { EssayLessonItem } from './EssayLessonItem';

export const EssayLesson = () => {
  const { paragraph, sentences } = useConversationContext();
  const sortedSentences = sentences ? groupSentencesByKey(sentences, 'group') : [];
  return (
    <ScrollArea
      className={classNames('w-full px-[calc((100%_-_615px)_/_2)] pt-8', {
        'h-[calc(100vh-100px)]': paragraph?.document_id !== SPECIAL_DOCUMENT_ID,
        'h-[calc(100vh-55px)]': paragraph?.document_id === SPECIAL_DOCUMENT_ID,
      })}
      isEnabled
    >
      <div className={classNames('font-[Helvetica] font-normal space-y-4 relative block', {})}>
        {sortedSentences.map((groupSentences, index) => (
          <EssayLessonItem key={index} sentences={groupSentences} position={index} />
        ))}
      </div>
    </ScrollArea>
  );
};
