import React, { useEffect, useState } from 'react';

import TranslationsSkeleton from '@/containers/learn/Translation/skeleton/TranslationsSkeleton';
import WavePlayer from 'components/Audio/WavePlayer';
import Image from 'components/Image';
import ScrollArea from 'components/ScrollArea';
import SentencePhrases from 'containers/learn/Translation/SentencePhrases';
import SentencePoses from 'containers/learn/Translation/SentencePoses';
import useDictionary from 'hooks/Ent/useDictionary';
import { useTranslations } from 'next-intl';
import useLearnStore from 'store/learn';

const Translations = () => {
  const t = useTranslations();
  const { selectedSentence, activeParagraph, exerciseToken } = useLearnStore();
  const [position, setPosition] = useState<number>(-1);
  const { dictionary, sentenceDetails, sentencePhrases, isLoadingPos, isLoadingSentence } =
    useDictionary(selectedSentence?.id || 0, position, exerciseToken);
  const [fileUrl, setFileUrl] = useState<string>('');
  useEffect(() => {
    setFileUrl('');
    if (selectedSentence && selectedSentence.audios && selectedSentence.audios.length > 0) {
      setFileUrl(selectedSentence?.audios[0]?.url || '');
    }
  }, [selectedSentence]);

  return (
    <>
      {!selectedSentence ? (
        <div className={'py-6 pr-4'}>
          <Image
            src={activeParagraph?.image || ''}
            alt={activeParagraph?.title || ''}
            className={'mx-auto bg-bg-box border-none rounded-md w-full h-auto'}
          />

          <h5 className={'text-black text-normal mt-2 mb-3 text-left font-[Inter]'}>
            {activeParagraph?.description || ''}
          </h5>
        </div>
      ) : (
        <div className={'h-[calc(100vh_-_44px)]'}>
          <ScrollArea className="h-full pr-4 pt-4">
            {isLoadingSentence ? (
              <TranslationsSkeleton />
            ) : (
              <>
                <h5
                  className={'text-normal mb-4 text-color-major'}
                  dangerouslySetInnerHTML={{ __html: selectedSentence.content || '' }}
                />
                {fileUrl !== '' ? <WavePlayer url={fileUrl} /> : null}
                {selectedSentence?.translation?.translate_google && (
                  <div className={'mt-0'}>
                    <h5 className={'text-color-minor text-normal'}>
                      {t('sidebar.right.translate')}
                    </h5>
                    <h5
                      className={'text-normal text-color-major'}
                      dangerouslySetInnerHTML={{
                        __html: selectedSentence.translation?.translate_google || '',
                      }}
                    />
                  </div>
                )}
              </>
            )}
            {sentencePhrases && <SentencePhrases phrases={sentencePhrases} />}
            {sentenceDetails && (
              <SentencePoses
                isLoadingPos={isLoadingPos}
                sentenceDetails={sentenceDetails}
                position={position}
                setPosition={setPosition}
                dictionary={dictionary}
              />
            )}
          </ScrollArea>
        </div>
      )}
    </>
  );
};
export default Translations;
