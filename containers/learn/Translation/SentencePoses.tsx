'use client';

import React, { useEffect, useMemo, useState } from 'react';

import SentencePosSkeleton from '@/containers/history/Translation/skeleton/SentencePosSkeleton';
import { Tooltip } from '@heroui/react';
import classNames from 'classnames';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import { SentencePosesProps } from 'types/component';
import { DictionaryWord, SentenceDetail } from 'types/model';

const SentencePoses = ({
  sentenceDetails,
  setPosition,
  position,
  dictionary,
  isLoadingPos,
}: SentencePosesProps) => {
  const t = useTranslations();
  const [currentMeanIndex, setCurrentIndex] = useState(0);
  const specialChar = '.,/?:;"!@#$%^&*()-_+=[]{}|\\\'';

  const [selectedMean, setMean] = useState<DictionaryWord | null>(null);

  const handleSelectWord = (word: SentenceDetail) => {
    if (specialChar.includes(word.toString())) return;
    setPosition(word.position || 0);
  };

  useEffect(() => {
    setPosition(-1);
  }, []);

  useEffect(() => {
    if (dictionary && dictionary.id) {
      if (!dictionary.means) setMean(null);
      else setMean(dictionary.means && dictionary.means[0]);
    } else {
      setMean(null);
    }
  }, [position, dictionary]);

  const translateList = useMemo(() => {
    if (selectedMean && selectedMean.id && selectedMean.id > 0)
      return selectedMean?.translate?.split(';');
    return [];
  }, [selectedMean]);

  const handleSetMean = (type = 'next') => {
    if (!dictionary!.means) return;
    const meanIndex = dictionary?.means.findIndex((mean) => mean.id === selectedMean?.id) || 0;
    let activeIndex = meanIndex - 1;
    if (type === 'next') {
      activeIndex = meanIndex + 1;
    }
    if (activeIndex < 0) activeIndex = 0;
    if (activeIndex >= dictionary!.means.length) {
      activeIndex = dictionary!.means.length - 1;
    }
    setCurrentIndex(activeIndex);
    const activeMean = dictionary?.means[activeIndex] || null;
    setMean(activeMean);
  };
  const handleReadPronoun = () => {
    if (dictionary && dictionary.audio) {
      const audio = new Audio(dictionary.audio);
      audio.play();
    }
  };
  return (
    <div className={'mt-8 text-normal'}>
      <h5 className={'text-color-minor mb-2'}>{t('sidebar.right.lookup')}</h5>
      <div className={'flex flex-wrap mb-1 text-color-major'}>
        {sentenceDetails &&
          map(sentenceDetails, (sentencePos, index) => (
            <span
              key={index}
              onClick={() => handleSelectWord(sentencePos)}
              className={classNames(
                'bg-bg-box mr-0.5 mb-1 text-center px-1 cursor-pointer border-b border-transparent',
                {
                  '!border-color-minor': (sentencePos.position || 0) === position,
                  '!bg-transparent !border-b-transparent': specialChar.includes(sentencePos.lemma),
                }
              )}
              dangerouslySetInnerHTML={{ __html: sentencePos.word }}
            />
          ))}
      </div>
      <div className={'relative'}>
        {isLoadingPos && <SentencePosSkeleton />}
        {selectedMean && !isLoadingPos ? (
          <div className={'w-full text-normal text-color-minor bg-bg-box p-3'}>
            <div className={'grid grid-cols-12 text-color-minor'}>
              <div className={'col-span-9  select-none'}>
                <div>{dictionary?.title}</div>
                <div className={'col-span-4 text-color-major'}>{selectedMean.lemma}</div>
              </div>
              <div className={'col-span-3'}>
                <div className={'flex items-center'}>
                  <span className={'pr-2'}>
                    ({currentMeanIndex + 1}/{(dictionary?.means && dictionary?.means?.length) || 0})
                  </span>
                  <span
                    onClick={() => handleSetMean('prev')}
                    className={classNames(
                      'w-[30px] h-[24px] border cursor-pointer shadow-medium rounded-[5px] border-color-border mr-1 items-center justify-center flex',
                      {
                        'disabled opacity-50': currentMeanIndex === 0,
                      }
                    )}
                  >
                    <i className={'text-base icon-arrow-left text-color-major'} />
                  </span>
                  <span
                    onClick={() => handleSetMean('next')}
                    className={classNames(
                      'w-[30px] h-[24px] border cursor-pointer shadow-medium rounded-[5px] border-color-border  ml-1 items-center justify-center flex',
                      {
                        'disabled opacity-50':
                          currentMeanIndex ===
                          ((dictionary?.means && dictionary?.means?.length - 1) || 0),
                      }
                    )}
                  >
                    <i className={'text-base icon-arrow-right-line text-color-major'} />
                  </span>
                </div>
              </div>
            </div>

            <div className={'mt-[10px] flex'}>
              <div className={'col-span-8'}>
                <div className="">{t('sidebar.right.pronounce')}</div>
                {selectedMean.phonetic ? (
                  <span className={'text-color-major'}>/{selectedMean.phonetic}/</span>
                ) : (
                  '-'
                )}
              </div>
              {dictionary?.audio && (
                <div
                  onClick={handleReadPronoun}
                  className={
                    'h-8 w-8 cursor-pointer flex items-center justify-center bg-bg-general rounded-full border border-color-border mx-2'
                  }
                >
                  <i className={'icon-volume-up'}></i>
                </div>
              )}
            </div>
            <div className={'mt-[10px] text-color-minor'}>{t('sidebar.right.definition')}</div>
            <Tooltip
              key={'tooltip-title-definition_vi'}
              placement={'top-start'}
              content={
                <div className="max-w-[262px]">
                  <div className="text-bg-box">{selectedMean.definition_vi}</div>
                </div>
              }
              color="foreground"
              showArrow={true}
              style={{ transform: 'translateX(25px)' }}
            >
              <div className={'text-color-major'}>{selectedMean.definition}</div>
            </Tooltip>
            {selectedMean.example && (
              <>
                <div className={'mt-[10px] text-color-minor'}>{t('sidebar.right.example')}</div>
                <Tooltip
                  key={'tooltip-title-example_vi'}
                  placement={'top-start'}
                  content={
                    <div className="max-w-[262px]">
                      <div className="text-bg-box">{selectedMean.example_vi}</div>
                    </div>
                  }
                  color="foreground"
                  showArrow={true}
                  style={{ transform: 'translateX(25px)' }}
                >
                  <div className={'text-color-major'}>{selectedMean.example}</div>
                </Tooltip>
              </>
            )}

            <div className={'mt-[10px] text-color-minor'}>{t('sidebar.right.translate')}</div>
            <div className={''}>
              {map(translateList, (translate, index) => (
                <span
                  key={index}
                  className={'text-color-major rounded-[5px] py-0.5 mr-1 text-center mb-2 h-[24px]'}
                  dangerouslySetInnerHTML={{ __html: translate }}
                />
              ))}
            </div>
          </div>
        ) : position > -1 && !isLoadingPos ? (
          <div className={'w-full text-normal text-color-minor bg-bg-box p-3'}>
            Đang cập nhật ...
          </div>
        ) : (
          ''
        )}
      </div>
    </div>
  );
};
export default SentencePoses;
