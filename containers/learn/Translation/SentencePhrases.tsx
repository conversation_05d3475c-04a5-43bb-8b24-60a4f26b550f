'use client';

import React, { useState } from 'react';

import classNames from 'classnames';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import { SentencePhrase } from 'types/model';

const SentencePhrases = ({ phrases }: { phrases: SentencePhrase[] | null }) => {
  const t = useTranslations();
  const [word, setWord] = useState<SentencePhrase | null>();
  const specialChar = '.,/?:;"!@#$%^&*()-_+=[]{}|\\\'';
  const handleSelectWord = (word: SentencePhrase) => {
    if (specialChar.includes(word.toString())) return;
    setWord(word);
  };

  return (
    <div className={'mt-8 text-normal'}>
      <h5 className={'text-color-minor mb-2'}>{t('sidebar.right.phrase')}</h5>
      <div className={'flex flex-wrap text-color-major mb-1'}>
        {phrases &&
          map(phrases, (sentencePhrase, index) => (
            <span
              key={index}
              onClick={() => handleSelectWord(sentencePhrase)}
              className={classNames(
                'bg-bg-box mr-0.5 text-center px-1 mb-1 cursor-pointer border-b border-transparent',
                {
                  '!border-color-minor text-color-major': (word?.id || 0) === sentencePhrase.id,
                  '!bg-transparent !border-b-transparent': specialChar.includes(
                    sentencePhrase.words
                  ),
                }
              )}
              dangerouslySetInnerHTML={{ __html: sentencePhrase.words }}
            ></span>
          ))}
      </div>
      {word && (
        <div className={'w-full text-normal'}>
          <div
            className={
              'w-full text-color-minor bg-bg-box border px-1.5 py-3 min-h-9 border-color-line items-center'
            }
          >
            {word.label_detail?.title_vi && (
              <>
                <div className={'pl-2'}>{t('sidebar.right.function')}</div>
                <div className={'pl-2 text-color-major mb-[10px]'}>
                  {word.label_detail?.title_vi || ''}
                </div>
              </>
            )}

            {word.translation && (
              <>
                <div className={'pl-2'}>{t('sidebar.right.translate')}</div>
                <span
                  className={'pl-2 text-color-major'}
                  dangerouslySetInnerHTML={{ __html: word.translation }}
                ></span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
export default SentencePhrases;
