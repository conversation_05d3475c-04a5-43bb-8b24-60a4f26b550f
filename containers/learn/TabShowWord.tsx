'use client';

import LearnTypeEnum from '@/configs/LearnTypeEnum';
import Tabs from 'components/Tabs';
import useLearnStore from 'store/learn';

const TabShowWord = () => {
  const { setShowWord, showWord, activeTab } = useLearnStore();
  const tabShowWord = [
    {
      id: false,
      title: <i className={'icon-eye-close w-4 text-base'} />,
    },
    {
      id: true,
      title: <i className={'icon-eye w-4 text-base'} />,
    },
  ];
  if (activeTab === LearnTypeEnum.APPROVE) return <></>;
  return (
    <Tabs
      classNames={{
        tab: 'px-2',
      }}
      tabs={tabShowWord}
      onClick={(value) => setShowWord(value as boolean)}
      activeTabId={showWord}
    />
  );
};
export default TabShowWord;
