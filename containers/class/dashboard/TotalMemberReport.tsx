'use client';

import React, { useMemo } from 'react';

import { TransactionTypeEnum } from '@/configs/TransactionTypeEnum';
import ChartBarSkeleton from '@/containers/class/skeleton/ChartBarSkeleton';
import useReportStore from '@/store/report';
import { ChartItem } from '@/types/component';
import { format } from 'date-fns';
import { reduce } from 'lodash';
import { useTranslations } from 'next-intl';

import Charts from '@/components/Charts/Charts';

import useChartConfig from '@/hooks/Ent/useChartConfig';
import useReportMember from '@/hooks/Ent/useReportMember';

const TotalMemberReport = ({ title }) => {
  const t = useTranslations();
  const { params, loading } = useReportStore();
  const { makeBarDate } = useChartConfig();
  const { reportsData, isLoading } = useReportMember(
    {
      ...params,
    },
    loading || params.start_day === 0 || params.end_day === 0
  );

  const [charts, columns, totalBalance] = useMemo(() => {
    const colors = ['BLUE', 'GREEN'];
    const charts: ChartItem[] = makeBarDate(reportsData, colors);

    reportsData?.forEach((item) => {
      const index = charts.findIndex((chart) => chart.date === item.day);
      if (index >= 0) {
        charts[index] = {
          ...charts[index],
          day: format(item.day * 86400 * 1000, 'dd'),
          date: item.day,
          isActive: true,
        };
        if (item.object_id === 1) {
          charts[index] = {
            ...charts[index],
            BLUE: item.amount,
          };
        }

        if (item.object_id === 2) {
          charts[index] = {
            ...charts[index],
            GREEN: item.amount,
          };
        }
      }
    });

    const totalBalance = reduce(
      reportsData,
      (totalBalance, item) =>
        item.transaction_type === TransactionTypeEnum.TRANSACTION_BURN
          ? totalBalance + item.amount
          : totalBalance,
      0
    );

    return [charts, colors, totalBalance];
  }, [reportsData]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0 || !payload.some((p) => p.value > 0)) {
      return null;
    }
    const total = payload.reduce((current, item) => item.value + current, 0);
    return (
      <div className="bg-bg-box p-3 rounded-md shadow-md">
        <p className="font-bold">
          Ngày {label}: {total} point{' '}
        </p>
        {payload.reverse().map((entry, index) => (
          <p
            key={index}
            className="text-xs py-1 flex items-center gap-2"
            style={{ color: entry.color }}
          >
            <i className={'w-3 h-3 inline-block'} style={{ backgroundColor: entry.color }}></i>
            Lớp {index + 1}: {entry.value} point
          </p>
        ))}
      </div>
    );
  };

  return (
    <>
      <div className={'col-span-12 sm:col-span-4 md:col-span-3 2xl:col-span-2'}>
        <div className={'grid grid-cols-3'}>
          <div className={'col-span-3 flex'}>
            <div className="mr-[30px] px-[6px]">
              <i className="text-[32px] icon-diamond text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>{t('group.class.balance')}</div>
              <span className={'text-[22px] font-medium'}>{totalBalance}</span>
            </div>
          </div>
        </div>
      </div>

      <div className={'col-span-12 sm:col-span-8 md:col-span-9 2xl:col-span-10'}>
        <div className={'w-full flex justify-between items-center'}>
          <span className={'text-small font-medium text-color-major'}>{title}</span>
        </div>
        <div className={'mt-2'}>
          <div className={'w-full h-[350px]'}>
            {isLoading ? (
              <ChartBarSkeleton />
            ) : (
              // @ts-ignore
              <Charts charts={charts} columns={columns} tooltip={CustomTooltip} />
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default TotalMemberReport;
