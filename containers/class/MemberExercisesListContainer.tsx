'use client';

import React from 'react';

import Link from 'next/link';

import MemberExerciseSkeleton from '@/containers/group/skeleton/MemberExerciseSkeleton';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';
import { typeStringToIconMap } from 'utils/common';

import useMemberExercise from '@/hooks/Ent/useMemberExercise';

const MemberExercisesListContainer = ({ groupId }: { groupId: number }) => {
  const { memberExercisesList, isLoading, page, setPage, isReachingEnd } = useMemberExercise({
    groupId: groupId,
  });
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 77px)'}
        key="paragraph"
        dataLength={memberExercisesList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'pr-2 pl-[30px] py-1 text-left font-normal'}>Bài giao</th>
              <th className={'py-1 text-left font-normal'}>Lần nghe bài</th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>Tra từ</th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>Điểm nói</th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>Điểm bài tập</th>
              {/* <th className={'py-1 text-left font-normal pr-[30px]'}>Nhận xét</th> */}
              {/* <th className={'py-1 text-left font-normal pr-[30px]'}></th> */}
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {map(memberExercisesList, (item, key) => (
              <tr
                key={`tr-${key}`}
                className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group "
              >
                <td>
                  <Link
                    className="flex pl-[20px] py-[12px]"
                    href={`${EntRouters.learn}/${item.paragraph.keyx}/${item.token}`}
                    key={key}
                  >
                    {/* <i className={'icon-conversation text-[16px] mr-[10px]'}></i> */}
                    <i
                      className={`text-[16px] mr-[10px] ${
                        typeStringToIconMap[item.paragraph?.item] || ''
                      }`}
                    />
                    {item.paragraph.title}
                  </Link>
                </td>

                <td className="">{item.listen}</td>
                <td className="">{item.lookup || 0}</td>
                <td>
                  {item.paragraph?.item === 'conversation' && (
                    <div className="flex items-center">
                      <i className="icon-ok-circled text-[14px] text-primary-100 mr-[2px]"></i>

                      <div className="mr-[10px]">{item.speak_high || 0}</div>

                      <i className="icon-ok-circled text-[14px] text-yellow mr-[2px]"></i>
                      <div className="mr-[10px]">{item.speak_medium || 0}</div>

                      <i className="icon-cancel text-[14px] text-red mr-[2px]"></i>
                      <div>{item.speak_low || 0}</div>
                    </div>
                  )}
                </td>
                <td>
                  
                    <div className="flex items-center">
                      <i className="icon-ok-circled text-[14px] text-primary-100 mr-[2px]"></i>

                      <div className="mr-[10px]">{item.correct_total || 0}</div>

                      {/* <i className="icon-ok-circled text-[14px] text-yellow mr-[2px]"></i>
                      <div className="mr-[10px]">{item.speak_medium || 0}</div> */}

                      <i className="icon-cancel text-[14px] text-red mr-[2px]"></i>
                      <div>{item.wrong_total || 0}</div>
                    </div>
                  
                </td>
                {/* <td>{item.note}</td> */}
                {/* <td>
                  <i className="ml-2.5 text-medium icon-more-fill" />
                </td> */}
              </tr>
            ))}

            {isLoading ? <MemberExerciseSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default MemberExercisesListContainer;
