'use client';

import React from 'react';

import MemberSkeleton from '@/containers/class/skeleton/MemberSkeleton';
import Button from 'components/Button';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import { AccountGroupsEntity } from 'types/model';

const Account = ({ group, isLoading }) => {
  const t = useTranslations();
  const handelRemoveManager = () => {};
  return (
    <table className="table-auto w-full">
      <thead>
        <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
          <th className={'px-2 py-1 text-left font-normal pl-[20px]'}>{t('group.class.name')}</th>
          <th className={'py-1 text-left font-normal'}>{t('group.class.phone')}</th>
          <th className={'py-1 text-right font-normal pr-[30px]'}>{t('learn.btn_approve')}</th>
        </tr>
      </thead>
      <tbody className={'text-[0.8123rem]'}>
        {!isLoading && !group ? (
          <tr>
            <td colSpan={4}>
              <div className={'flex h-72 justify-center w-full items-center flex-col'}>
                <i className={'w-20 h-20 text-5xl icon-alert-line animate-bounce opacity-50'} />
                <span
                  className={'text-color-minor'}
                  dangerouslySetInnerHTML={{ __html: t('group.class.no_member') }}
                />
              </div>
            </td>
          </tr>
        ) : null}
        {group && group.account_groups
          ? // @ts-ignore
            map(group.account_groups, (accounts: AccountGroupsEntity, key) => {
              if (!accounts || !accounts.account) {
                return null; // Hoặc xử lý trường hợp accounts là số nếu cần
              }
              return (
                <tr
                  key={`tr-${key}`}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group "
                >
                  <td key={`td-1-${key}`} className={'p-2 h-[42px] pl-[30px] flex items-center'}>
                    <i className="text-[16px] icon-group" />
                    &nbsp;&nbsp;{accounts?.account?.fullname}
                  </td>

                  <td key={`td-2-${key}`} className={'w-[40%]'}>
                    <div className="flex gap-x-2">{accounts?.account?.phone}</div>
                  </td>
                  <td key={`td-4-${key}`} className={'w-[15%] pr-[30px] text-right'}>
                    <div className={'flex float-end'}>
                      <Button
                        color={'default'}
                        size={'icon'}
                        className={'hidden group-hover:block'}
                        onClick={handelRemoveManager}
                      >
                        <i className="text-medium icon-more-fill" />
                      </Button>
                    </div>
                  </td>
                </tr>
              );
            })
          : null}
        {isLoading ? <MemberSkeleton /> : null}
      </tbody>
    </table>
  );
};
export default Account;
