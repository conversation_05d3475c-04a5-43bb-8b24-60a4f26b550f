'use client';

import React, { useEffect, useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import GroupTypeEnum from '@/configs/GroupTypeEnum';
import MemberDetailSkeleton from '@/containers/group/skeleton/MemberDetailSkeleton';
import { Skeleton } from '@heroui/react';
import classNames from 'classnames';
import WavePlayer from 'components/Audio/WavePlayer';
import Button from 'components/Button';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import Header from 'containers/class/Header';
import useGroupMember from 'hooks/Ent/useGroupMember';
import { map, split } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';

import useMemberExerciseDetail from '@/hooks/Ent/useMemberExerciseDetail';

import CommentAddPopup from './member/NotePopup';

type ExerciseItem = { id: number; token: string };

const MemberDetailList = ({ groupId, memberIdToken, accountExerciseId }) => {
  const t = useTranslations();
  const router = useRouter();
  const [memberId, memberToken] = split(memberIdToken, '-');
  const [page, setPage] = useState(1);
  const [comments, setComments] = useState<{ [key: number]: string }>({});

  const { exerciseLists, pagination, isLoading, exerciseMember, exerciseParagraph, saveSpeak } =
    useMemberExerciseDetail({
      memberExerciseToken: memberToken,
      offset: page - 1,
      isFetch: parseInt(memberId) > 0,
    });

  const [openPopup, setOpenPopup] = useState(false);
  const [selectedItem, setSelectedItem] = useState<ExerciseItem | null>(null);

  const handleOpenPopup = (item) => {
    setSelectedItem(item);
    setOpenPopup(true);
  };
  const { group } = useGroupMember(parseInt(groupId));
  const [headerData, setHeaderData] = useState<{ id: number; title: string }[]>([]);
  useEffect(() => {
    if (group) {
      if (group.type !== GroupTypeEnum.CLASS) {
        router.push('/');
        return;
      }
      const newData: { id: number; title: string }[] = [];
      if (group.parent_title) {
        newData.push({ id: group.parent_id, title: group.parent_title });
      }
      newData.push({ id: group.id, title: group.title });
      setHeaderData(newData);
    }
  }, [group]);
  return (
    <>
      <Header key={`header-${groupId}`} token={group?.token} breadcrumbs={headerData} />
      <div className="flex pl-[20px] pb-[10px] justify-between items-center">
        <div className="flex items-center">
          <Link href={`${EntRouters.group}/${groupId}/member/${accountExerciseId}`}>
            <Button
              size={'xs'}
              variant={'bordered'}
              color={'default'}
              className={'float-end px-2 ml-2.5'}
            >
              <i className={classNames('icon-arrow-left text-normal')} /> Quay lại
            </Button>
          </Link>
          <div className="ml-[20px]">
            {isLoading ? (
              <Skeleton className={`rounded-sm w-64 h-4`} />
            ) : (
              <div
                dangerouslySetInnerHTML={{
                  // @ts-ignore
                  __html: t.rich('group.exercise_detail.header', {
                    content: (chuck) => `<span class='text-color-minor'>${chuck}</span>`,
                    paragraph: exerciseParagraph?.title,
                    fullname: exerciseMember?.fullname ?? '',
                  }),
                }}
              />
            )}
          </div>
        </div>
        {/* <div className="mr-[20px]">
          <Button
            size={'xs'}
            variant={'bordered'}
            color={'default'}
            className={'float-end px-2 ml-2.5'}
          >
            <i className={classNames('icon-pencil-line text-normal')} /> Viết nhận xét
          </Button>
        </div> */}
      </div>
      <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
        <InfiniteScroll
          height={'calc(100vh - 77px)'}
          key="paragraph"
          dataLength={exerciseLists.length}
          next={() => setPage(page + 1)}
          hasMore={pagination?.hasMore || false}
          loader={null}
          className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
        >
          <table className="table-auto w-full">
            <thead>
              <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px] '}>
                <th className={'pr-2 pl-[30px] py-1 text-left font-normal w-[30%]'}>Câu</th>
                <th className={'py-1 text-left font-normal w-[30%]'}>Ghi âm đọc của học sinh</th>
                <th className={'py-1 text-left font-normal pl-[30px]'}>Score</th>
                <th className={'py-1 text-right font-normal pr-[30px]'}></th>
                <th className={'py-1 text-right font-normal pr-[30px]'}></th>
              </tr>
            </thead>
            <tbody className={'text-[0.8123rem]'}>
              {map(exerciseLists, (item) => (
                <tr
                  key={`tr-${item.id}`}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group "
                >
                  <td className="">
                    <div className="flex items-center pl-[30px] min-h-[43px]">
                      <div>
                        <i className={'icon-sentence mr-[10px] text-[16px]'}></i>
                      </div>

                      <div className="">{item.template}</div>
                    </div>
                  </td>
                  <td className="py-[5px] pl-[5px]">
                    {item.url && (
                      <div>
                        <WavePlayer mb={0} height={32} url={item.url} />
                      </div>
                    )}
                  </td>
                  <td>
                    <div className="flex pl-[30px] items-center">
                      {item.score && (
                        <div className={'opacity-100  w-4 h-4'}>
                          <i
                            className={classNames('text-base icon-ok-circled', {
                              'text-red': item.score < 50,
                              'text-yellow': item.score < 80 && item.score >= 50,
                              'text-primary': item.score > 80,
                            })}
                          />
                        </div>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center pr-[20px]">
                      {/* <i className="ml-2.5 text-medium icon-more-fill" /> */}
                      {comments[item.id] ? comments[item.id] : item.comment}
                    </div>
                  </td>
                  <td>
                    <Button
                      onClick={() => handleOpenPopup(item)}
                      size={'xs'}
                      variant={'bordered'}
                      color={'default'}
                      className={'float-end px-2 ml-2.5 mr-[20px]'}
                    >
                      <i className={classNames('icon-pencil-line text-normal')} /> Thêm nhận xét
                    </Button>
                  </td>
                </tr>
              ))}
              {isLoading ? <MemberDetailSkeleton /> : null}
            </tbody>
          </table>
        </InfiniteScroll>
      </ScrollArea>
      {openPopup && (
        <CommentAddPopup
          title="Thêm nhận xét"
          open={openPopup}
          onOpen={() => setOpenPopup(true)}
          onClose={() => setOpenPopup(false)}
          id={selectedItem?.id} // Truyền ID bài tập
          saveSpeak={saveSpeak}
          onSuccess={(newComment: string) => {
            if (selectedItem?.id) {
              setComments((prev) => ({
                ...prev,
                [selectedItem.id]: newComment,
              }));
            }
          }}
        />
      )}
    </>
  );
};
export default MemberDetailList;
