'use client';

import React from 'react';

import Link from 'next/link';

import ModalAssignParagraphToMember from '@/containers/class/exercise/ModalAssignParagraphToMember';
import ExerciseAssignmentSkeleton from '@/containers/class/skeleton/ExerciseAssignmentSkeleton';
import { GroupAssignMemberList, ParagraphEntity } from '@/types/model';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import { format } from 'date-fns';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';

import NoContent from '@/components/NoContent';

import useGroupAccountExercise from '@/hooks/Ent/useGroupAccountExercise';

interface ExerciseAssignmentContainerProps {
  setOpen: (open: boolean) => void;
  open: boolean;
  groupId: number;
  onDataChange?: (data: {
    memberInGroups: GroupAssignMemberList[];
    paragraphs: ParagraphEntity[];
  }) => void;
}

const ExerciseAssignmentContainer = ({
  setOpen,
  open,
  groupId,
  onDataChange,
}: ExerciseAssignmentContainerProps) => {
  const { accountExercisesList, page, setPage, isReachingEnd, isLoading, reFetch } =
    useGroupAccountExercise(groupId ?? 0);
  const t = useTranslations();
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 77px)'}
        dataLength={accountExercisesList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead>
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
                {t('group.assignment.title')}
              </th>
              <th className={'py-1 text-left font-normal'}>{t('group.assignment.startTime')}</th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>
                {t('group.assignment.endTime')}
              </th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>
                {t('group.assignment.quantityMember')}
              </th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>
                {t('group.assignment.complete')}
              </th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {!isLoading && !accountExercisesList.length ? (
              <tr>
                <td colSpan={5}>
                  <NoContent />
                </td>
              </tr>
            ) : (
              map(accountExercisesList, (item, index) => (
                <tr
                  key={index}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group "
                >
                  <td className="py-[12px]">
                    <Link
                      href={`${EntRouters.group}/${groupId}/member/${item.id}`}
                      key={item.id}
                      className="pl-[30px] py-[12px]"
                    >
                      {item.paragraph?.title}
                    </Link>
                  </td>
                  <td>
                    {item?.start_at ? format(new Date(item?.start_at * 1000), 'dd/MM/yyyy') : 'N/A'}
                  </td>
                  <td>
                    {item?.end_at ? format(new Date(item?.end_at * 1000), 'dd/MM/yyyy') : 'N/A'}
                  </td>
                  <td>{item.members}</td>
                  <td>{item.members_finish || 0}</td>
                </tr>
              ))
            )}
            {isLoading ? <ExerciseAssignmentSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
      <ModalAssignParagraphToMember
        title={'ttt'} // Sử dụng title từ modalData
        open={open}
        onOpen={setOpen}
        onClose={() => setOpen(false)}
        groupId={groupId}
        mutate={reFetch}
        onDataChange={onDataChange}
      />
    </ScrollArea>
  );
};
export default ExerciseAssignmentContainer;
