'use client';

import React, { useEffect, useState } from 'react';

import ApproveStatusEnum from '@/configs/ApproveStatusEnum';
import { StatusEnum } from '@/configs/StatusEnum';
import { OptionItem } from '@/types/component';
import { ParagraphEntity } from '@/types/model';
import {
  Autocomplete,
  AutocompleteItem,
  ButtonGroup,
  CalendarDate,
  DatePicker,
  DateValue,
  Form,
  Textarea,
  cn,
} from '@heroui/react';
import { useInfiniteScroll } from '@heroui/use-infinite-scroll';
import {
  CalendarDateTime,
  ZonedDateTime,
  getLocalTimeZone,
  now,
  today,
} from '@internationalized/date';
import { I18nProvider } from '@react-aria/i18n';
import Button from 'components/Button';
import Modal from 'components/Modal';
import { round } from 'lodash';
import { useLocale, useTranslations } from 'next-intl';
import { GroupAddPopupProps } from 'types/popup';

import AutocompleteSearchable from '@/components/AutocompleteSearchable';

import { useAddGroupAssignAccount } from '@/hooks/Ent/useGroupAccountExercise';
import useGroupMemberList from '@/hooks/Ent/useGroupMemberList';
import useParagraphs from '@/hooks/Ent/useParagraphs';
import { useSession } from '@/hooks/useSession';

const ModalAssignParagraphToMember = ({
  groupId,
  open,
  onOpen,
  onClose,
  mutate,
  onDataChange,
}: GroupAddPopupProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const [memberList, setMemberList] = useState<OptionItem[]>([]);
  const [paragraph, setParagraph] = useState<ParagraphEntity | null>(null);
  const [selectedMemberIds, setSelectedMemberIds] = useState<(number | string)[]>(['0']);
  const [note, setNote] = useState<string | null>(null);
  const [startDate] = React.useState<DateValue | null>(now(getLocalTimeZone()));
  const [endDate, setEndDate] = React.useState<DateValue | null>(now(getLocalTimeZone()));
  const [errors, setErrors] = useState({
    start_date: '',
    end_date: '',
    paragraph_id: '',
    member_ids: '',
    note: '',
  });

  const { data } = useSession();

  const {
    paragraphList: groupDocuments,
    isLoading,
    setPage,
    page,
    isReachingEnd,
  } = useParagraphs({
    group_id: groupId,
    status: StatusEnum.ON,
  });
  const { groupMemberList } = useGroupMemberList({
    offset: 0,
    groupId: groupId,
    shouldNotFetch: groupId === 0,
  });

  // Gửi dữ liệu ra ngoài component cha khi dữ liệu thay đổi
  useEffect(() => {
    if (onDataChange) {
      onDataChange({
        memberInGroups: groupMemberList,
        paragraphs: groupDocuments,
      });
    }
  }, [groupMemberList, groupDocuments]);

  const { addGroupAssignAccount } = useAddGroupAssignAccount(mutate);

  const handleSubmit = async () => {
    const convertDateToTime = (
      date: CalendarDate | CalendarDateTime | ZonedDateTime | null,
      isEndDate = false
    ) => {
      if (!date) return null;
      let endOfDay = date;
      if (isEndDate) {
        endOfDay = new CalendarDateTime(date.year, date.month, date.day, 23, 59, 59, 0);
      }
      const newDate = endOfDay.toDate(getLocalTimeZone());
      return round(newDate.getTime() / 1000, 0);
    };
    const dataAssign = {
      account_id: data?.user.id,
      paragraph_id: paragraph?.id,
      start_at: convertDateToTime(startDate),
      end_at: convertDateToTime(endDate, true),
      group_id: groupId,
      member_ids: selectedMemberIds
        .filter((itemId) => parseInt(itemId as string) !== 0)
        .map((id) => parseInt(id as string)),
      note: note,
    };
    let hasError = false;
    if (!dataAssign.start_at) {
      hasError = true;
      setErrors((prevState) => ({ ...prevState, start_date: 'Chưa chọn ngày bắt đầu' }));
    }
    if (!dataAssign.end_at) {
      hasError = true;
      setErrors((prevState) => ({ ...prevState, end_date: 'Chưa chọn ngày kết thúc' }));
    }
    if (!selectedMemberIds.length) {
      hasError = true;
      setErrors((prevState) => ({ ...prevState, member_ids: 'Chưa chọn học viên' }));
    }
    if (!paragraph) {
      hasError = true;
      setErrors((prevState) => ({ ...prevState, paragraph_id: 'Chưa chọn bài giao' }));
    }
    if (hasError) return;
    await addGroupAssignAccount(dataAssign);
    if (onClose) {
      // @ts-ignore
      onClose();
    }
  };
  useEffect(() => {
    if (!open) {
      // Chỉ reset checkList khi modal được mở lần đầu
      const formattedOptions = groupMemberList
        ?.filter((item) => item.approve_process === ApproveStatusEnum.ACCEPTED)
        .map((member) => ({
          id: member.member.id,
          title: member.member.fullname,
        }));
      const listKeys = formattedOptions.map((item) => item.id);
      listKeys.push(0);
      setSelectedMemberIds(listKeys);
      // setEndDate(null);
      // setStartDate(null);
      setNote(null);
    }
    setTimeout(() => {
      document.querySelectorAll('[data-top-scroll]').forEach((el) => {
        el.removeAttribute('data-top-scroll');
      });

      document.querySelectorAll('[data-bottom-scroll]').forEach((el) => {
        el.removeAttribute('data-bottom-scroll');
      });
    }, 200);
  }, [open]);
  const handleGetAll = () => {
    if (groupMemberList && groupMemberList.length > 0) {
      setErrors((prevState) => ({ ...prevState, member: '' }));
      setSelectedMemberIds(groupMemberList.map((member) => member.member.id));
    }
  };

  useEffect(() => {
    // Chuyển đổi membersList thành optionList
    if (groupMemberList && groupMemberList.length > 0) {
      const formattedOptions = groupMemberList
        .filter((item) => item.approve_process === ApproveStatusEnum.ACCEPTED)
        .map((member) => ({
          id: member.member.id,
          title: member.member.fullname,
        }));
      const listKeys = formattedOptions.map((item) => item.id);
      listKeys.push(0);
      setMemberList([{ id: 0, title: t('group.assignment.assignAll') }, ...formattedOptions]);
      setSelectedMemberIds(listKeys);
    }
  }, [groupMemberList]);

  const [, scrollerRef] = useInfiniteScroll({
    hasMore: !isReachingEnd,
    isEnabled: open,
    shouldUseLoader: false, // We don't want to show the loader at the bottom of the list
    onLoadMore: () => setPage(page + 1),
    distance: 50,
  });

  const handleSelectParagraph = (key: React.Key | null) => {
    const selectedParagraph = groupDocuments.find((item) => item.id === parseInt(key as string));
    if (!selectedParagraph) {
      setErrors((prevState) => ({ ...prevState, paragraph_id: 'Chưa chọn bài giao' }));
    } else {
      setErrors((prevState) => ({ ...prevState, paragraph_id: '' }));
    }
    setParagraph(selectedParagraph ?? null);
  };

  const handleSelectMember = (id: number | string | null, isRemove = false) => {
    if (id === null) {
      setErrors((prevState) => ({ ...prevState, member: 'Vui lòng chọn học viên' }));
      setSelectedMemberIds([]);
    } else {
      if (isRemove) {
        const newSelectedKeys = selectedMemberIds.filter((i) => i.toString() !== id.toString());
        setSelectedMemberIds(newSelectedKeys);
      } else {
        setSelectedMemberIds([...selectedMemberIds, id]);
      }
    }
  };

  return (
    <Modal
      opened={open}
      size={'xs'}
      onClose={() => onClose}
      onOpenChange={onOpen}
      classNames={{
        base: 'rounded-md max-w-[40%] w-[100%] overflow-visible',
        header: 'font-bold text-base text-color-major py-5 px-4 pb-2',
        footer: 'border border-bg-box py-3 px-4 hidden',
        body: 'px-0 py-0',
      }}
      header={<h5 className={'text-sm font-medium'}>{t('group.assignment.create_title')}</h5>}
    >
      <Form
        className="w-full flex flex-col gap-x-4 gap-y-0"
        validationBehavior="native"
        validationErrors={errors}
        onSubmit={handleSubmit}
      >
        <Textarea
          rows={3}
          placeholder={t('group.assignment.placeholderNote')}
          className={
            'rounded-none w-full border-x-[0px] border-t-[0px] border-b-[1px] border-b-bg-box bg-inherit'
          }
          name={'note'}
          onValueChange={setNote}
        />

        <div className="flex gap-x-4 px-4 w-full py-1 border-b-[1px] border-b-bg-box">
          <div className="flex items-center gap-2 w-[160px]">
            <i className={'icon-logout text-base before:ml-0'} />
            <div className="whitespace-nowrap">{t('group.assignment.assignTo')}</div>
          </div>
          <div className="w-full">
            <AutocompleteSearchable
              autoFocus={false}
              items={memberList}
              selectedKeys={selectedMemberIds}
              showCheckAll={false}
              name={'member_ids'}
              checkAllLabel={t('group.assignment.assignAll')}
              placeholder={t('group.assignment.placeholderAssignmentMember')}
              onGetAll={handleGetAll}
              onItemChange={handleSelectMember}
              errorMessage={errors.member_ids}
              aria-label={'false'}
            />
          </div>
        </div>
        <div className="flex gap-x-4 px-4 w-full py-[4px] items-center border-b-[1px] border-b-bg-box">
          <div className="flex items-center gap-2 w-[160px]">
            <i className={'icon-package text-base before:ml-0'} />
            <div className="whitespace-nowrap">{t('group.assignment.title')}</div>
          </div>
          <div className="w-full">
            <Autocomplete
              label={'Assignment'}
              size={'sm'}
              scrollRef={scrollerRef}
              name={'paragraph_id'}
              className="w-full border-none shadow-none !px-0"
              classNames={{
                listboxWrapper: '!rounded-sm px-0',
                listbox: '!rounded-none',
                popoverContent: 'rounded-sm px-0',
                base: 'px-0 overflow-hidden bg-none',
                endContentWrapper: 'absolute top-[0.4px] right-3',
              }}
              listboxProps={{
                classNames: {
                  base: 'px-0',
                },
                itemClasses: {
                  base: 'rounded-none',
                },
              }}
              inputProps={{
                classNames: {
                  label:
                    'group-data-[filled-within=true]:translate-y-0 group-data-[filled-within=true]:mt-0 !hidden',
                  inputWrapper: cn('w-full h-8 px-1 block border-none shadow-none'),
                  innerWrapper: cn(
                    'flex flex-wrap gap-1 min-h-8 max-w-[calc(100%-4rem)] group-data-[has-label=true]:!items-start !items-start w-full data-[has-end-content=true]:outline-none'
                  ),
                  input: cn(
                    'text-medium pl-0 data-[has-end-content=true]:ps-0 data-[has-end-content=true]:outline-none'
                  ),
                },
              }}
              isVirtualized
              radius={'sm'}
              defaultItems={groupDocuments}
              placeholder={t('group.assignment.placeholderAssignment')}
              selectedKey={paragraph?.id.toString() || null}
              isLoading={isLoading}
              variant="bordered"
              onSelectionChange={handleSelectParagraph}
              errorMessage={errors.paragraph_id}
              scrollShadowProps={{
                isEnabled: false,
                className: 'mask-image-none',
              }}
            >
              {(item) => (
                <AutocompleteItem key={item.id} textValue={item.title}>
                  <div className={'flex'}>
                    <i className={'icon-conversation text-tiny mr-2'}></i>
                    <span>{item.title}</span>
                  </div>
                </AutocompleteItem>
              )}
            </Autocomplete>
          </div>
        </div>
        {/*<div className='flex items-center gap-2 px-4 w-full border-b-[1px] border-b-bg-box'>*/}
        {/*  <i className={'icon-timer-line text-medium'} />*/}
        {/*  <div className='mr-[3px] whitespace-nowrap'>{t('group.assignment.startTime')}</div>*/}
        {/*  <DatePicker*/}
        {/*    hideTimeZone*/}
        {/*    showMonthAndYearPickers*/}
        {/*    size={'sm'}*/}
        {/*    label=''*/}
        {/*    hourCycle={24}*/}
        {/*    defaultValue={now(getLocalTimeZone())}*/}
        {/*    name={'start_date'}*/}
        {/*    onChange={setStartDate}*/}
        {/*    errorMessage={errors.start_date}*/}
        {/*    variant='bordered'*/}
        {/*    classNames={{*/}
        {/*      base: 'shadow-none border-none ml-4 py-3',*/}
        {/*      inputWrapper: '!shadow-none !border-none',*/}
        {/*      input: 'text-medium',*/}
        {/*      errorMessage: 'text-[8px]',*/}
        {/*    }}*/}
        {/*  />*/}

        {/*</div>*/}
        <div className="flex items-center gap-2 justify-between px-4 w-full">
          <i className={'icon-timer-line text-base before:ml-0'} />
          <I18nProvider locale={locale}>
            <DatePicker
              hideTimeZone
              defaultValue={today(getLocalTimeZone())}
              minValue={today(getLocalTimeZone())}
              hourCycle={24}
              size={'sm'}
              labelPlacement={'outside-left'}
              label={t('group.assignment.endTime')}
              name={'end_date'}
              onChange={setEndDate}
              // defaultValue={now(getLocalTimeZone())}
              errorMessage={errors.end_date}
              variant="bordered"
              classNames={{
                base: '!shadow-none !border-none py-3 gap-x-4 !text-medium',
                inputWrapper: '!shadow-none !border-none ps-2',
                input: 'text-medium',
                timeInputLabel: '!text-medium whitespace-nowrap',
                label: '!text-medium whitespace-nowrap',
                calendarContent: '!shadow-none !border-none',
                calendar: '!shadow-none !border-none',
                popoverContent: '!shadow-none !border-none',
                segment: '!shadow-none !border-none',
                errorMessage: 'text-[8px]',
              }}
            />
          </I18nProvider>
        </div>

        <ButtonGroup className="flex-row gap-4 w-full justify-end border-t-[1px] border-bg-box py-3 px-4">
          <Button
            onClick={onClose}
            size={'xs'}
            variant={'bordered'}
            color={'default'}
            className={'float-end py-[13px]'}
          >
            {t('course.cancel_create')}
          </Button>
          <Button
            color={'primary'}
            size={'xs'}
            className={'float-end py-[13px]'}
            onClick={handleSubmit}
          >
            {t('learn.send')}
          </Button>
        </ButtonGroup>
      </Form>
    </Modal>
  );
};

export default ModalAssignParagraphToMember;
