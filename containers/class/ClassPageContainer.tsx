'use client';

import React, { useEffect, useMemo, useState } from 'react';

import { useParams, usePathname, useRouter } from 'next/navigation';

import GroupTypeEnum from '@/configs/GroupTypeEnum';
import ClassReportContainer from '@/containers/class/dashboard/ClassReportContainer';
import DashboardHeader from '@/containers/class/dashboard/DashboardHeader';
import ExerciseAssignmentContainer from '@/containers/class/exercise/ExerciseAssignmentContainer';
import { GroupAssignMemberList, ParagraphEntity } from '@/types/model';
import classNames from 'classnames';
import Button from 'components/Button';
import ManagerAddPopup from 'components/ServicePopup/ManagerAddPopup';
import Tabs from 'components/Tabs';
import Header from 'containers/class/Header';
import Account from 'containers/class/account/Account';
import Member from 'containers/class/member/Member';
import MemberHistorySpeak from 'containers/class/member/MemberHistorySpeak';
import LearnProgramContainer from 'containers/class/program/LearnProgramContainer';
import useGroupMember from 'hooks/Ent/useGroupMember';
import { useTranslations } from 'next-intl';

import useGroupDocument from '@/hooks/Ent/useGroupDocument';
import { useSession } from '@/hooks/useSession';

import MemberExercisesListContainer from './MemberExercisesListContainer';

const ClassPageContainer = ({ classId }: { classId: string }) => {
  const { data: sessionData } = useSession();
  const t = useTranslations();
  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();
  const tabs = useMemo(() => {
    const baseTabs = [
      {
        id: 1,
        keyx: 'dashboard',
        title: t('group.tab.dashboard'),
      },
      {
        id: 4,
        keyx: 'exercise',
        title: t('group.tab.assignment'),
      },
    ];

    // Chỉ thêm tab manager nếu sessionData.user tồn tại
    if (sessionData?.user && Object.keys(sessionData.user).length > 0) {
      baseTabs.push(
        {
          id: 2,
          keyx: 'member',
          title: t('group.tab.member'),
        },
        {
          id: 3,
          keyx: 'account',
          title: t('group.tab.manager'),
        },
        {
          id: 5,
          keyx: 'roadmap',
          title: t('group.tab.roadmap'),
        }
      );
    } else {
      baseTabs.push({
        id: 7,
        keyx: 'speak',
        title: t('group.tab.speak'),
      });
    }

    return baseTabs;
  }, [sessionData, t]);
  const { group, isLoading, mutate } = useGroupMember(parseInt(classId));
  const [open, setOpen] = useState(false);
  const [headerData, setHeaderData] = useState<Array<{ id: number; title: string }>>([]);
  const [isOpenModalProgram, setOpenModalProgram] = useState<boolean>(false);
  const [activeHistoryTab, setActiveTab] = useState('dashboard');
  const [openManager, setOpenManager] = useState(false);
  // Dữ liệu từ ExerciseAssignmentContainer
  const [asignmentData, setAssignmentData] = useState<{
    memberInGroups: GroupAssignMemberList[];
    paragraphs: ParagraphEntity[];
  }>({ memberInGroups: [], paragraphs: [] });
  useEffect(() => {
    if (group) {
      if (group.type !== GroupTypeEnum.CLASS) {
        router.push('/');
        return;
      }
      const newData: Array<{ id: number; title: string }> = [];
      if (group.parent_title) {
        newData.push({ id: group.parent_id, title: group.parent_title });
      }
      newData.push({ id: group.id, title: group.title });
      setHeaderData(newData);
    }
  }, [group]);

  useEffect(() => {
    // Đọc tab từ URL khi component mount và khi URL thay đổi
    const tabFromUrl = pathname.split('/').pop();
    if (tabFromUrl && tabs.some((tab) => tab.keyx === tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [params]);

  const { group_documents } = useGroupDocument(group?.id ?? 0);

  const handleChangeTab = (newActiveTab: string | number | boolean) => {
    const tabKey = String(newActiveTab);
    setActiveTab(tabKey);
    router.push(`/class/${classId}/${tabKey}`);

    // Refresh group data when clicking on the member tab (except first time)
    if (tabKey === 'member') {
      mutate();
    }
  };

  const handleOpen = () => {
    setOpen(true);
  };
  // const handleCloseManager = () => setOpenManager(false);
  const handleCloseManager = () => {
    setOpenManager(false);
    mutate();
  };

  const handleOpenManager = () => {
    setOpenManager(true);
  };

  return (
    <div>
      <Header key={`header-${classId}`} token={group?.token} breadcrumbs={headerData} />
      <div>
        {tabs.length > 0 && (
          <div className={'flex items-center justify-between pr-[30px]'}>
            <div className="mb-[10px]">
              <Tabs
                tabs={tabs}
                className={'ml-[30px]'}
                value={'keyx'}
                activeTabId={activeHistoryTab}
                onSelectionChange={handleChangeTab}
              />
            </div>
            <div className={'mb-[10px] flex w-full justify-end'}>
              {activeHistoryTab === 'dashboard' && <DashboardHeader />}
              {activeHistoryTab === 'exercise' &&
                // group?.member_groups?.length > 0 &&
                asignmentData.paragraphs.length > 0 &&
                asignmentData.memberInGroups.length > 0 &&
                group_documents.length > 0 &&
                sessionData?.user &&
                Object.keys(sessionData.user).length > 0 && (
                  <Button
                    onClick={() => handleOpen()}
                    size={'xs'}
                    variant={'bordered'}
                    color={'default'}
                    className={'px-2'}
                  >
                    <i className={classNames('icon-add text-normal')} />{' '}
                    {t('group.class.assignedExercise')}
                  </Button>
                )}
              {activeHistoryTab === 'account' && (
                <Button
                  onClick={() => handleOpenManager()}
                  size={'xs'}
                  variant={'bordered'}
                  color={'default'}
                  className={'float-end px-2 ml-2.5'}
                >
                  <i className={classNames('icon-add text-normal')} /> Thêm quản lý
                </Button>
              )}
              {activeHistoryTab === 'roadmap' && (
                <Button
                  color={'default'}
                  onClick={() => setOpenModalProgram(true)}
                  className={'border shadow-medium'}
                  size={'sm'}
                  startContent={<i className={'text-tiny icon-add'} />}
                >
                  {t('group.class.learnProgram')}
                </Button>
              )}
            </div>
          </div>
        )}
        {activeHistoryTab === 'dashboard' && (
          <div className={'border-bg-box border-b-[10px] h-0 w-full'}></div>
        )}
        <div className={'relative'}>
          {activeHistoryTab === 'dashboard' && group && <ClassReportContainer group={group} />}
          {activeHistoryTab === 'member' && (
            <Member isLoading={isLoading} group={group} mutate={mutate} />
          )}
          {activeHistoryTab === 'speak' && group && (
            <MemberHistorySpeak memberToken={group.token} memberId={sessionData?.member?.id} />
          )}
          {activeHistoryTab === 'account' && <Account isLoading={isLoading} group={group} />}
          {activeHistoryTab === 'exercise' ? (
            sessionData?.user && Object.keys(sessionData.user).length > 0 ? (
              <ExerciseAssignmentContainer
                open={open}
                setOpen={setOpen}
                groupId={parseInt(classId)}
                onDataChange={setAssignmentData}
              />
            ) : (
              <MemberExercisesListContainer groupId={parseInt(classId)} />
            )
          ) : null}

          {activeHistoryTab === 'roadmap' && (
            <LearnProgramContainer
              setOpenModalProgram={setOpenModalProgram}
              isOpenModalProgram={isOpenModalProgram}
              group={group}
            />
          )}
        </div>
      </div>

      <ManagerAddPopup
        title={'Thêm quản lý lớp'}
        groupId={parseInt(classId)}
        open={openManager}
        onOpen={setOpenManager}
        onClose={handleCloseManager} // Gọi callback khi popup hoàn thành
      />
    </div>
  );
};

export default ClassPageContainer;
