'use client';

import React, { useEffect, useState } from 'react';

import { ButtonGroup } from '@heroui/react';
import Button from 'components/Button';
import Input from 'components/Input';
import Modal from 'components/Modal';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

const CommentAddPopup = ({
  title,
  open,
  onOpen,
  onClose,
  id,
  saveSpeak,
  onSuccess,
}: {
  title: string;
  open: boolean;
  onOpen: () => void;
  onClose: () => void;
  id: number | undefined;
  saveSpeak: any; // Thêm prop mới
  onSuccess?: (newComment: string) => void;
}) => {
  const t = useTranslations();
  const [comment, setComment] = useState('');
  const [error, setError] = useState('');

  const handleConfirm = async () => {
    if (!comment.trim()) {
      setError(t('Vui lòng nhập nhận xét.'));
    } else {
      setError('');
      try {
        // saveSpeak({ comment, id }).then((res) => {
        //   if (res.success) {
        //     alert(t('member.success.common'));
        //     return;
        //   }
        //   alert(t('message.error.exit'));
        // });
        const res = await saveSpeak({ comment, id });
        // console.log(res);
        if (res.success) {
          toast.success(t('member.success.common'));
          if (onSuccess) onSuccess(comment);
          if (onClose) onClose();
        } else {
          toast.error(t('message.error.exit'));
        }
      } catch {
        toast.error(t('message.error.exit'));
      }
    }
  };

  useEffect(() => {
    if (!open) {
      setComment(''); // Reset comment khi modal đóng
      setError('');
    }
  }, [open]);

  return (
    <Modal
      opened={open}
      size={'xs'}
      onClose={onClose}
      onOpenChange={onOpen}
      classNames={{
        base: 'rounded-md max-w-[32%] w-[100%]',
        header: 'font-bold text-base text-color-major py-5 px-4 pb-2',
        footer: 'border border-bg-box py-3 px-4 hidden',
        body: 'px-0 py-0',
      }}
      header={<h5 className="text-sm font-medium">{title}</h5>}
    >
      <div className="items-center px-4 pb-4">
        <Input
          removeLabel={true}
          labelPlacement="outside"
          classNames={{
            inputWrapper:
              'bg-inherit px-0 py-0 text-color-major hover:!bg-transparent focus-within:!bg-transparent shadow-none',
            input: '!font-medium text-[17px] placeholder:text-[rgba(107,111,118,0.5)]',
          }}
          onChange={(e) => setComment(e.target.value)}
          value={comment}
          type="text"
          placeholder="Nhập nhận xét của bạn"
        />
        {error && <p className="text-red-500">{error}</p>}
      </div>
      <ButtonGroup className="flex-row gap-2 justify-end border-t-[1px] border-bg-box py-3 px-4">
        <Button
          onClick={onClose}
          size="xs"
          variant="bordered"
          color="default"
          className="shadow-md"
        >
          {t('course.cancel_create')}
        </Button>
        <Button color="primary" size="xs" className="" onClick={handleConfirm}>
          {t('course.confirm_create')}
        </Button>
      </ButtonGroup>
    </Modal>
  );
};

export default CommentAddPopup;
