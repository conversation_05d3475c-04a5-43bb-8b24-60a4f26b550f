'use client';

import React, { ReactElement, useRef, useState } from 'react';

import Link from 'next/link';
import { useParams } from 'next/navigation';

import ApproveStatusEnum from '@/configs/ApproveStatusEnum';
import { MemberGroupsEntity } from '@/types/model';
import { Chip, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from '@heroui/react';
import Button from 'components/Button';
import EntRouters from 'configs/EntRouters';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';
import { KeyedMutator } from 'swr';

import useApproveMemberGroups from '@/hooks/Ent/useApproveMemberGroups';
import useClickOutside from '@/hooks/common/useClickOutside';

const MemberItem = ({
  members,
  isLoading,
  mutate,
}: {
  members: MemberGroupsEntity;
  isLoading: boolean;
  mutate?: KeyedMutator<any>; // hoặc mutate?: () => void nếu bạn không dùng SWR
}) => {
  console.log(members);
  const params = useParams();
  const id = params.id ? Number(params.id) : 0;
  const [approveProcess, setApproveProcess] = useState(
    members?.approve_process || ApproveStatusEnum.CREATED
  );
  const [showActionButtons, setShowActionButtons] = useState(false);
  const buttonRef = useRef<ReactElement | null>(null);
  const { doApproveMemberGroups } = useApproveMemberGroups();
  const t = useTranslations();
  useClickOutside(buttonRef, () => setShowActionButtons(false));
  const handleApprove = async (approve: number) => {
    if (approve !== ApproveStatusEnum.ACCEPTED && approve !== ApproveStatusEnum.REJECTED) {
      toast.error(t('member.error.exit'));
      return;
    }
    try {
      const res = await doApproveMemberGroups(members.id, approve);

      if (res.success) {
        setApproveProcess(approve);
        mutate?.();
      } else {
        toast.error(t('member.error.exit'));
      }
    } catch (e) {
      toast.error(t('member.error.exit'));
    }
  };

  const toggleButton = () => {
    setShowActionButtons(!showActionButtons);
  };

  return (
    <tr className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group">
      <td className="p-2 h-[42px] pl-[30px] flex items-center">
        <i className="text-[16px] icon-group" />
        &nbsp;&nbsp;
        {approveProcess === ApproveStatusEnum.ACCEPTED ? (
          <Link href={`${EntRouters.class}/${id}/chart/${members.member_id}`}>
            {members?.member?.fullname}
          </Link>
        ) : (
          members?.member?.fullname
        )}
      </td>
      <td className="w-[35%]">
        <div className="flex gap-x-2">{members?.member?.phone}</div>
      </td>
      <td className="w-[20%] pr-[30px] text-right">
        <div className="flex justify-end w-full">
          {approveProcess === ApproveStatusEnum.REJECTED && (
            <Chip size={'sm'} radius="sm" color="danger" variant="light">
              {t('group.class.rejected')}
            </Chip>
          )}
          {approveProcess === ApproveStatusEnum.ACCEPTED ? (
            <Dropdown className={'rounded-md'} size={'sm'}>
              <DropdownTrigger>
                <Button size="icon" className="block group-hover:block" onClick={toggleButton}>
                  <i className="text-medium icon-more-fill" />
                </Button>
              </DropdownTrigger>
              <DropdownMenu aria-label="Actions">
                <DropdownItem key="view">
                  <Link href={`${EntRouters.class}/${members.group_id}/chart/${members.member_id}`}>
                    {t('group.class.view_member')}
                  </Link>
                </DropdownItem>
                <DropdownItem key="remove">
                  <div
                    className={'text-danger'}
                    onClick={() => handleApprove(ApproveStatusEnum.REJECTED)}
                  >
                    {t('group.class.remove')}
                  </div>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          ) : null}

          {approveProcess === ApproveStatusEnum.CREATED ? (
            <>
              <Button
                ref={buttonRef}
                color="primary"
                size="xs"
                className="mr-2"
                onClick={() => handleApprove(ApproveStatusEnum.ACCEPTED)}
              >
                {t('group.class.accept')}
              </Button>
              <Button
                ref={buttonRef}
                size="xs"
                color="danger"
                onClick={() => handleApprove(ApproveStatusEnum.REJECTED)}
              >
                {t('group.class.reject')}
              </Button>
            </>
          ) : null}
        </div>
      </td>
    </tr>
  );
};
export default MemberItem;
