'use client';

import React from 'react';

import ApproveStatusEnum from '@/configs/ApproveStatusEnum';
import MemberItem from '@/containers/class/member/MemberItem';
import MemberSkeleton from '@/containers/class/skeleton/MemberSkeleton';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import { KeyedMutator } from 'swr';
import { MemberGroupsEntity } from 'types/model';

const Member = ({
  group,
  isLoading,
  mutate,
}: {
  group: any;
  isLoading: boolean;
  mutate: KeyedMutator<any>;
}) => {
  const t = useTranslations();
  return (
    <table className="table-auto w-full">
      <thead>
        <tr className="bg-bg-box text-color-minor text-[13px] h-[32px]">
          <th className="px-2 pl-[30px] py-1 text-left font-normal">{t('group.class.name')}</th>
          <th className="py-1 text-left font-normal">{t('group.class.phone')}</th>
          <th className="py-1 text-right font-normal pr-[30px]">{t('learn.btn_approve')}</th>
        </tr>
      </thead>
      <tbody className="text-[0.8123rem]">
        {!isLoading && !group ? (
          <tr>
            <td colSpan={4}>
              <div className="flex h-72 justify-center w-full items-center flex-col">
                <i className="w-20 h-20 text-5xl icon-alert-line animate-bounce opacity-50" />
                <span
                  className="text-color-minor"
                  dangerouslySetInnerHTML={{ __html: t('group.class.no_member') }}
                />
              </div>
            </td>
          </tr>
        ) : null}

        {group && group.member_groups
          ? map(group.member_groups, (members: MemberGroupsEntity, key) => {
              if (
                !members ||
                !members.member ||
                members.approve_process === ApproveStatusEnum.REJECTED
              )
                return null;
              return <MemberItem isLoading={false} members={members} key={key} mutate={mutate} />;
            })
          : null}
        {isLoading ? <MemberSkeleton /> : null}
      </tbody>
    </table>
  );
};

export default Member;
