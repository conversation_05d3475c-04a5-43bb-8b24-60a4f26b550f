'use client';

import React, { useEffect } from 'react';

import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import PointsReport from '@/containers/group/report-item/PointsReport';
import TokenReport from '@/containers/group/report-item/TokenReport';
import useReportStore from '@/store/report';
import ScrollArea from 'components/ScrollArea';
import { useTranslations } from 'next-intl';

const MemberDetailReportContainer = ({ memberId }) => {
  const t = useTranslations();
  const { params, setParams } = useReportStore();
  useEffect(() => {
    setParams({
      ...params,
      item: ReportTypeEnum.MEMBER,
      object_id: memberId,
    });

    return () => {
      const tooltipEl = document.getElementById('chartjs-tooltip');
      if (tooltipEl) {
        document.body.removeChild(tooltipEl);
      }
    };
  }, []);

  return (
    <ScrollArea
      className={'!h-[calc(100vh_-_43px)] relative w-full flex-1 bg-bg-general overflow-x-hidden'}
    >
      <div className={'grid grid-cols-12 mt-3 gap-4 px-[30px]'}>
        <TokenReport
          title={t('group.dashboard.label_chart_member_token')}
          label={t('group.dashboard.label_chart_member_point')}
        />
      </div>

      <div className={'grid grid-cols-12 gap-4 mt-10 px-[30px]'}>
        <PointsReport />
      </div>
    </ScrollArea>
  );
};
export default MemberDetailReportContainer;
