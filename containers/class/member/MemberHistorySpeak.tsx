'use client';

// import Button from 'components/Button';
import React, { useState } from 'react';

// import Link from 'next/link';
// import EntRouters from 'configs/EntRouters';
import MemberDetailSkeleton from '@/containers/group/skeleton/MemberDetailSkeleton';
import classNames from 'classnames';
import WavePlayer from 'components/Audio/WavePlayer';
import ScrollArea from 'components/ScrollArea';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';

// import useGroupMember from 'hooks/Ent/useGroupMember';
// import Header from 'containers/class/Header';
import useMemberExerciseDetail from '@/hooks/Ent/useMemberExerciseDetail';

// import { useTranslations } from 'next-intl';
// import { Skeleton } from '@heroui/react';
// import GroupTypeEnum from '@/configs/GroupTypeEnum';
// import { useRouter } from 'next/navigation';
// import CommentAddPopup from './member/NotePopup';

// type ExerciseItem = { id: number; token: string };

const MemberHistorySpeak = ({ memberToken, memberId }) => {
  //   const t = useTranslations();
  //   const router = useRouter();
  //   const [memberId, memberToken] = split(memberIdToken, '-');
  const [page, setPage] = useState(1);

  const { exerciseLists, pagination, isLoading } = useMemberExerciseDetail({
    // memberExerciseToken: memberToken,
    offset: page - 1,
    isFetch: parseInt(memberId) > 0,
  });

  return (
    <>
      <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
        <InfiniteScroll
          height={'calc(100vh - 77px)'}
          key="paragraph"
          dataLength={exerciseLists.length}
          next={() => setPage(page + 1)}
          hasMore={pagination?.hasMore || false}
          loader={null}
          className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
        >
          <table className="table-auto w-full">
            <thead>
              <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px] '}>
                <th className={'pr-2 pl-[30px] py-1 text-left font-normal w-[30%]'}>Câu</th>
                <th className={'py-1 text-left font-normal w-[30%]'}>Ghi âm đọc của học sinh</th>
                <th className={'py-1 text-left font-normal pl-[30px]'}>Score</th>
                <th className={'py-1 text-right font-normal pr-[30px]'}></th>
              </tr>
            </thead>
            <tbody className={'text-[0.8123rem]'}>
              {map(exerciseLists, (item) => (
                <tr
                  key={`tr-${item.id}`}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group "
                >
                  <td className="">
                    <div className="flex items-center pl-[30px] min-h-[43px]">
                      <div>
                        <i className={'icon-sentence mr-[10px] text-[16px]'}></i>
                      </div>

                      <div className="">{item.template}</div>
                    </div>
                  </td>
                  <td className="py-[5px] pl-[5px]">
                    {item.url && (
                      <div>
                        <WavePlayer mb={0} height={32} url={item.url} />
                      </div>
                    )}
                  </td>
                  <td>
                    <div className="flex pl-[30px] items-center">
                      {item.score && (
                        <div className={'opacity-100  w-4 h-4'}>
                          <i
                            className={classNames('text-base icon-ok-circled', {
                              'text-red': item.score < 50,
                              'text-yellow': item.score < 80 && item.score >= 50,
                              'text-primary': item.score > 80,
                            })}
                          />
                        </div>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center pr-[20px]">
                      {/* <i className="ml-2.5 text-medium icon-more-fill" /> */}
                      {item.comment}
                    </div>
                  </td>
                </tr>
              ))}
              {isLoading ? <MemberDetailSkeleton /> : null}
            </tbody>
          </table>
        </InfiniteScroll>
      </ScrollArea>
    </>
  );
};
export default MemberHistorySpeak;
