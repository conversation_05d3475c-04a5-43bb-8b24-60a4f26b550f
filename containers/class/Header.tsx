'use client';

import React from 'react';

import { Tooltip } from '@heroui/react';
import classNames from 'classnames';
import Button from 'components/Button';
import EntRouters from 'configs/EntRouters';
import AppHeader from 'containers/layout/AppHeader';
import { useTranslations } from 'next-intl';
import useHeaderStore from 'store/header';

type Breadcrumb = {
  id: number;
  title: string;
};

type HeaderProps = {
  breadcrumbs: Breadcrumb[];
  token: string;
  tooltipContent?: string;
  link?: string | null;
};
const Header: React.FC<HeaderProps> = ({ breadcrumbs, token, tooltipContent = '', link = '' }) => {
  const { title } = useHeaderStore();
  const t = useTranslations();
  const copyLink = () => {
    const genereLink =
      link && link !== '' ? link : window.location.origin + EntRouters.group_invite + '/' + token;
    navigator.clipboard
      .writeText(genereLink)
      .then(() => {
        console.log('Token đã được sao chép vào clipboard');
      })
      .catch((err) => {
        console.error('Lỗi khi sao chép: ', err);
      });
  };
  return (
    <AppHeader bottom={'0px'}>
      <div className={'h-[43px] px-[30px] w-full grid grid-cols-12 items-center'}>
        <div className={'col-span-6 flex items-center'}>
          <span className={classNames('flex gap-x-2')}>
            <i className={'icon-group text-[16px]'} />
            {breadcrumbs.length === 0
              ? title != ''
                ? title
                : t('group.title')
              : breadcrumbs.map((item, index) => (
                  <div className={'flex items-center'} key={index}>
                    {' '}
                    {index === 0 && breadcrumbs.length > 1 ? (
                      <a href={`${EntRouters.group}/${item.id}`}>{item.title}</a>
                    ) : (
                      item.title
                    )}{' '}
                    {breadcrumbs.length > index + 1 ? (
                      <i className={'icon-arrow-right-fill text-medium ml-2'} />
                    ) : (
                      ''
                    )}
                  </div>
                ))}

            {link !== null ? (
              <Tooltip
                showArrow={true}
                content={
                  tooltipContent && tooltipContent !== ''
                    ? tooltipContent
                    : t('group.class.copy_link')
                }
              >
                <Button
                  color={'default'}
                  size={'icon'}
                  className={'bg-[transparent] hover:bg-bg-box'}
                  onClick={copyLink}
                >
                  <i className="text-medium icon-links-line" />
                </Button>
              </Tooltip>
            ) : null}
          </span>
        </div>
      </div>
    </AppHeader>
  );
};

export default Header;
