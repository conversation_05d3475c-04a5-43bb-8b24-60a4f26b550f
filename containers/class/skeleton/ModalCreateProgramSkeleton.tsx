import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const ModalCreateProgramSkeleton = () => {
  return (
    <>
      {map(range(1, 10), (index) => (
        <div className="border-b border-bg-box h-[43px] flex gap-3 items-center" key={index}>
          <Skeleton className="rounded-sm h-4 w-4" />
          <Skeleton className={`rounded-sm ${index % 2 ? 'w-2/5' : 'w-3/5'} h-3`} />
        </div>
      ))}
    </>
  );
};
export default ModalCreateProgramSkeleton;
