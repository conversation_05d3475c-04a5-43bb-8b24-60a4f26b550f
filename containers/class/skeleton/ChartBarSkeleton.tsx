import React from 'react';

import { Skeleton } from '@heroui/react';
import { map, random, times } from 'lodash';

const ChartBarSkeleton = () => {
  const randomArray = times(30, () => random(0, 350));
  return (
    <div className="border-b border-bg-box h-[350px] flex gap-3 items-end">
      <div className={'h-[350px] w-0 border-l border-l-color-border'}></div>
      {map(randomArray, (item, index) => (
        <Skeleton style={{ height: `${item}px` }} className={`rounded-sm w-8`} key={index} />
      ))}
    </div>
  );
};
export default ChartBarSkeleton;
