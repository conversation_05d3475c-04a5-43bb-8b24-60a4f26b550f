import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const MemberSkeleton = () => {
  return (
    <>
      {map(range(1, 30), (index) => (
        <tr className="border-b border-bg-box h-[43px] items-center" key={index}>
          <td className={'p-2 pl-[30px]'}>
            <div className={'flex items-center gap-3'}>
              <Skeleton className="rounded-sm h-3 w-3" />
              <Skeleton className={`rounded-sm ${index % 2 ? 'w-2/5' : 'w-3/5'} h-3`} />
            </div>
          </td>
          <td className={'p-2'}>
            <Skeleton className={`rounded-sm w-20 h-3`} />
          </td>
          <td className={'p-2'}>
            <div className="flex w-full justify-end pr-[30px]">
              <Skeleton className={`rounded-sm w-3 h-3`} />
            </div>
          </td>
        </tr>
      ))}
    </>
  );
};
export default MemberSkeleton;
