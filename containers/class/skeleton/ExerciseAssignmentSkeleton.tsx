import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const GroupSkeleton = () => {
  return (
    <>
      {map(range(1, 30), (index) => (
        <tr className="border-b border-bg-box h-[43px] items-center" key={index}>
          <td className={'p-2 pl-[30px]'}>
            <Skeleton className={`rounded-sm ${index % 2 ? 'w-2/5' : 'w-3/5'} h-3`} />
          </td>
          <td className={'p-2'}>
            <Skeleton className={`rounded-sm w-20 h-3`} />
          </td>
          <td className={'p-2'}>
            <Skeleton className={`rounded-sm w-20 h-3`} />
          </td>
          <td className={'p-2'}>
            <Skeleton className={`rounded-sm w-3 h-3`} />
          </td>
          <td className={'p-2'}>
            <Skeleton className={`rounded-sm w-3 h-3`} />
          </td>
        </tr>
      ))}
    </>
  );
};
export default GroupSkeleton;
