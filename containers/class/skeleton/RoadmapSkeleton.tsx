import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const RoadmapSkeleton = () => {
  return (
    <>
      {map(range(1, 30), (index) => (
        <div
          className="border-b border-bg-box h-[43px] flex gap-3 items-center px-[30px]"
          key={index}
        >
          <Skeleton className="rounded-sm h-3 w-3" />
          <Skeleton className={`rounded-sm ${index % 2 ? 'w-2/5' : 'w-3/5'} h-3`} />
        </div>
      ))}
    </>
  );
};
export default RoadmapSkeleton;
