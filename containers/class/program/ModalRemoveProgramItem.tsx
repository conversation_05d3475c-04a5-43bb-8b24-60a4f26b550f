'use client';

import Modal from 'components/Modal';

const ModalRemoveProgramItem = ({
  openModalRemoveProgram,
  isOpenModalRemoveProgram,
  onConfirmRemove,
}) => {
  const handleRemove = () => {
    onConfirmRemove();
  };

  return (
    <Modal
      size={'sm'}
      submitLabel={'Xóa'}
      onSubmit={handleRemove}
      onOpenChange={openModalRemoveProgram}
      opened={isOpenModalRemoveProgram}
      header={'Thông báo'}
    >
      <div className={'text-left my-3'}>Chủ đề này sẽ được xóa khỏi chương trình học ?</div>
    </Modal>
  );
};

export default ModalRemoveProgramItem;
