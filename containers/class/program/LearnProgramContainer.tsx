'use client';

import React, { useEffect, useState } from 'react';

import { StatusEnum } from '@/configs/StatusEnum';
import ModalRemoveProgramItem from '@/containers/class/program/ModalRemoveProgramItem';
import { ProgramItem } from '@/containers/class/program/ProgramItem';
import RoadmapSkeleton from '@/containers/class/skeleton/RoadmapSkeleton';
import usePopupStore from '@/store/popup';
import { EditSentenceItem } from '@/types/component';
import { LearnProgramPositionEntity } from '@/types/hooks';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import { GroupDocumentEntity } from 'types/model';

import DragContext from '@/components/Drags/DragContext';
import DragItem from '@/components/Drags/DragItem';
import ErrorModal from '@/components/ServicePopup/ErrorModal';

import useGroupDocument from '@/hooks/Ent/useGroupDocument';

import ModalCreateProgram from './ModalCreateProgram';

const LearnProgramContainer = ({ group, isOpenModalProgram, setOpenModalProgram }) => {
  const { group_documents, isLoading, updateGroupDocument, doChangeLearnProgramPosition } =
    useGroupDocument(group?.id ?? 0);
  const [isOpenModalRemoveProgram, setOpenModalRemoveProgram] = useState(false);
  const { setErrorModal } = usePopupStore();
  const [removeDocument, setRemoveDocument] = useState<GroupDocumentEntity | null>(null);
  const [dragItems, setDragItems] = useState<EditSentenceItem[]>([]);
  const t = useTranslations();
  useEffect(() => {
    if (group_documents && group_documents.length) {
      const documents: GroupDocumentEntity[] = [];
      map(group_documents, (item: GroupDocumentEntity) => {
        if (item.status === StatusEnum.ON) {
          documents.push({ ...item, position: item.position || 0 });
        }
      });
      setDragItems([...documents]);
    }
  }, [group_documents]);
  const openModalRemoveParagraph = (groupDocument: GroupDocumentEntity) => {
    setRemoveDocument(groupDocument);
    setOpenModalRemoveProgram(true);
  };
  const handleAddItem = (items: EditSentenceItem[]) => {
    setDragItems(items);
  };
  const handleRemoveItem = async () => {
    setOpenModalRemoveProgram(false);
    if (!removeDocument || removeDocument?.id === 0) return;
    const paramDeleteLearnProgram = {
      id: removeDocument.id,
      status: 1,
      // title: removeParagraph.title,
      // group_id: removeParagraph.group_id,
      // paragraph_id: removeParagraph.paragraph_id,
    };
    updateGroupDocument(paramDeleteLearnProgram).then((res) => {
      if (res?.id) {
        setErrorModal({ opened: false, message: '' });
        setDragItems((prevState) => prevState.filter((item) => item.id !== removeDocument?.id));
      } else {
        setErrorModal({ opened: true, message: 'Có lỗi khi xóa chủ đề' });
      }
      setRemoveDocument(null);
    });
  };

  const handleDragEnd = (lists: GroupDocumentEntity[]) => {
    const paragraphPositions: LearnProgramPositionEntity[] = [];
    lists.map((item, index: number) => {
      if (index !== item.position) paragraphPositions.push({ id: item.id, position: index });
    });
    doChangeLearnProgramPosition(paragraphPositions);
  };

  return (
    <div>
      <div className={'bg-bg-box w-full text-color-minor text-[13px] h-[32px]'}>
        <div className={'text-left leading-[32px] font-normal px-[30px]'}>
          {t('group.class.assignedExercise')}
        </div>
      </div>
      {isLoading || !group ? <RoadmapSkeleton /> : null}
      <DragContext items={dragItems} setItems={setDragItems} onDragEnd={handleDragEnd}>
        {dragItems.map((item: EditSentenceItem, index: number) => (
          <DragItem
            key={item.id}
            id={item.id}
            position={index}
            dragClassName={'top-3 left-2 !right-[unset]'}
          >
            <ProgramItem key={index} onRemove={openModalRemoveParagraph} program={item} />
          </DragItem>
        ))}
      </DragContext>
      <ErrorModal />
      <ModalRemoveProgramItem
        openModalRemoveProgram={setOpenModalRemoveProgram}
        isOpenModalRemoveProgram={isOpenModalRemoveProgram}
        onConfirmRemove={handleRemoveItem}
      />
      <ModalCreateProgram
        groupId={group?.id || 0}
        onAddDocument={handleAddItem}
        documents={dragItems}
        openModalCreateProgram={setOpenModalProgram}
        isOpenModalCreateProgram={isOpenModalProgram}
      />
    </div>
  );
};

export default LearnProgramContainer;
