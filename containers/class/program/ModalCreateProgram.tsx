'use client';

import React, { useEffect, useMemo, useState } from 'react';

import ModalCreateProgramSkeleton from '@/containers/class/skeleton/ModalCreateProgramSkeleton';
import { Checkbox } from '@heroui/react';
import { Input } from 'components';
import Modal from 'components/Modal';
import { map } from 'lodash';
import debounce from 'lodash/debounce';
import { DocumentEntity } from 'types/model';

import ScrollArea from '@/components/ScrollArea';

import useGroupDocument from '@/hooks/Ent/useGroupDocument';
import useSearchDocuments from '@/hooks/Ent/useSearchDocuments';

const ModalCreateProgram = ({
  openModalCreateProgram,
  isOpenModalCreateProgram,
  onAddDocument,
  documents,
  groupId,
}) => {
  const [keyword, setKeyword] = useState<string>('');
  const [keywordSearch, setKeywordSearch] = useState<string>('');
  const { documentsList, isLoading } = useSearchDocuments(keywordSearch);
  const { saveGroupDocument } = useGroupDocument();
  const [selectedDocument, setSelectedDocument] = useState<DocumentEntity[]>([]);
  const [documentFilterList, setDocuments] = useState<DocumentEntity[]>([]);
  const debouncedSetKeywordSearch = useMemo(
    () => debounce((value) => setKeywordSearch(value), 500),
    []
  );
  useEffect(() => {
    debouncedSetKeywordSearch(keyword);
  }, [keyword, debouncedSetKeywordSearch]);
  useEffect(() => {
    const lists: DocumentEntity[] = [];
    documentsList &&
      map(documentsList, (item) => {
        if (documents.some((doc) => doc.document_id === item.id)) {
          lists.push({ ...item, inList: true });
        } else {
          lists.push({ ...item, inList: false });
        }
      });
    setDocuments(lists);
  }, [documentsList]);
  const onToggleItem = (documentItem: DocumentEntity) => {
    const id = documentItem.id;
    const hasParagraph = selectedDocument.find((item) => item.id === id);
    if (hasParagraph) {
      setSelectedDocument((prevState) => prevState.filter((item) => item.id !== id));
    } else {
      setSelectedDocument((prevState) => [...prevState, documentItem]);
    }
  };

  const handleAddDocumentToProgram = () => {
    saveGroupDocument(selectedDocument, groupId).then((res) => {
      const newItem: DocumentEntity[] = [];
      map(res, (item) => {
        if (item.success && item.data) {
          newItem.push(item.data);
        }
      });
      onAddDocument([...documents, ...newItem]);
    });
  };

  const modalHeader = useMemo(() => {
    return (
      <div>
        <Input
          size={'sm'}
          className={'text-small pt-2'}
          classNames={{
            input: 'text-small',
            innerWrapper: 'pb-0',
            inputWrapper: '',
            label: 'text-small font-medium',
          }}
          value={keyword}
          onValueChange={setKeyword}
          removeLabel={false}
          labelPlacement={'outside'}
          placeholder={' '}
          label={'Chọn chủ đề'}
        />
      </div>
    );
  }, [keyword]);
  return (
    <Modal
      size={'lg'}
      buttonSize={'md'}
      classNames={{
        header: 'py-2',
      }}
      scrollBehavior={'inside'}
      submitLabel={'Thêm vào chương trình học'}
      onSubmit={handleAddDocumentToProgram}
      onOpenChange={openModalCreateProgram}
      opened={isOpenModalCreateProgram}
      header={modalHeader}
    >
      <ScrollArea className={'min-h-[300px] -mr-5 pr-5'}>
        {!isLoading && (
          <div className={'py-3 h-[300px]'}>
            {map(documentFilterList, (item) => {
              return (
                <div
                  key={item.id}
                  className={'h-10 border-b border-b-color-border flex items-center w-full'}
                >
                  <Checkbox
                    onValueChange={() => onToggleItem(item)}
                    // @ts-ignore
                    value={item.id}
                    size={'sm'}
                    // @ts-ignore
                    isDisabled={item.inList}
                    className={
                      'w-full !max-w-full pr-0 outline-none before:focus:!outline-none before:focus-visible:!outline-none'
                    }
                    classNames={{
                      base: 'border-color-border w-full outline-none before:focus:!outline-none before:focus-visible:!outline-none',
                      label: 'w-full !pr-0',
                    }}
                  >
                    <div className={'flex items-center justify-between w-full'}>
                      {item.title}
                      {/*@ts-ignore*/}
                      {item.inList ? (
                        <i className="text-base icon-ok-circled text-primary" />
                      ) : null}
                    </div>
                  </Checkbox>
                </div>
              );
            })}
          </div>
        )}
        {isLoading && (
          <div className={'relative w-full h-[300px]'}>
            <ModalCreateProgramSkeleton />
          </div>
        )}
      </ScrollArea>
    </Modal>
  );
};

export default ModalCreateProgram;
