'use client';

import React, { FC } from 'react';

import Link from 'next/link';

import Button from 'components/Button';
import { ProgramItemProps } from 'types/component';

export const ProgramItem: FC<ProgramItemProps> = ({ program, onRemove }) => {
  return (
    <div className="w-full bg-bg-general hover:bg-bg-box/60 group border-b border-bg-box group flex items-center justify-between pl-[22px] pr-[22px]">
      <div className={'p-2 h-[43px] flex items-center'}>
        <Link href={`/document/${program.id}`} className={'flex items-center'}>
          <i className="text-[16px] icon-document mr-1" /> {program.title}
        </Link>
      </div>
      <Button
        onClick={() => onRemove(program)}
        color="default"
        size="icon"
        className="hidden group-hover:block"
      >
        <i className="text-small text-danger icon-delete-bin" />
      </Button>
    </div>
  );
};
