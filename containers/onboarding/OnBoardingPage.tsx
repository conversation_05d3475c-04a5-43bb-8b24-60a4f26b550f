'use client';

import React from 'react';

import { useParams, useRouter } from 'next/navigation';

// import useOnBoarding from 'hooks/Ent/useOnBoarding';
import EntRouters from 'configs/EntRouters';

import QuestionScreen from './QuestionScreen';

interface OnBoardingPageProps {
  setStep: (step: number) => void;
  activeStep: number;
  quizzQuestions: { id: number; content: string; translate: string; type: number }[];
}

const OnBoardingPage = ({ setStep, activeStep, quizzQuestions }: OnBoardingPageProps) => {
  const router = useRouter();
  const params = useParams();
  const { slug } = params;
  if (!slug || typeof slug === 'undefined') {
    router.push(EntRouters.welcome);
    return null;
  }
  // const { question } = useOnBoarding(slug.toString());

  return (
    <div className={'text-color-major'}>
      <QuestionScreen setStep={setStep} step={activeStep} quizzQuestions={quizzQuestions} />
    </div>
  );
};
export default OnBoardingPage;
