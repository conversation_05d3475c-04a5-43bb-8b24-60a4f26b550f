'use client';

import React, { useState } from 'react';

import Input from 'components/Form/Input';
import { OTP_LENGTH } from 'configs';
import { useTranslations } from 'next-intl';
import OtpInput from 'react-otp-input';
import { MemberRegisterProps } from 'types/component';

const MemberRegister = ({ memberId, onUpdate }: MemberRegisterProps) => {
  const t = useTranslations();
  const [PIN, setPin] = useState('');
  const [name, setName] = useState('');
  const handleChangeName = (name: string) => {
    setName(name);
    onUpdate({
      memberName: name,
      pin: PIN,
      id: memberId,
    });
  };
  const handleChangePin = (pin: string) => {
    setPin(pin);
    onUpdate({
      memberName: name,
      pin: pin,
      id: memberId,
    });
  };
  return (
    <div className={'flex items-center mb-6'}>
      <div className={'mr-6 max-w-[320px]'}>
        <div className={'pb-1'}>{t('onboard.name')}</div>

        <Input
          type={'text'}
          className={'bg-bg-box h-12 w-72'}
          onChange={(e) => handleChangeName(e.target.value)}
          inputClassName={'bg-bg-box'}
        />
      </div>
      <div className={''}>
        <div className={'pl-2 pb-1'}>
          {t('onboard.password')} <span className={'text-red'}>({t('onboard.password_note')})</span>
        </div>
        <OtpInput
          value={PIN}
          onChange={handleChangePin}
          numInputs={OTP_LENGTH}
          renderSeparator={''}
          inputType={'tel'}
          renderInput={(props) => <input {...props} />}
          placeholder="0000"
          containerStyle={{
            fontSize: '40px',
          }}
          inputStyle={{
            color: '#00A758',
            width: '3rem',
            margin: '0 5px',
            border: '1px solid #E5E5E5',
            background: '#F4F5F8',
            outline: 0,
            borderRadius: '8px',
            fontSize: 32,
          }}
        />
      </div>
    </div>
  );
};
export default MemberRegister;
