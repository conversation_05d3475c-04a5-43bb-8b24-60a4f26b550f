'use client';

import React from 'react';

import { useRouter } from 'next/navigation';

import Button from 'components/Button';
import EntLogoIcon from 'components/Icons/EntLogoIcon';
import EntRouters from 'configs/EntRouters';
import { useTranslations } from 'next-intl';

const FinishScreen = () => {
  const router = useRouter();
  const t = useTranslations();
  const handleStart = () => {
    router.push(EntRouters.home);
  };
  return (
    <div className="flex">
      <nav
        // className='md:left-0 md:block md:fixed md:top-0 md:bottom-0 md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden bg-bg-general flex flex-wrap items-center justify-between relative md:w-[13.75rem] z-10 py-4 pl-[15px] pr-[17px]'
        className=" md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden bg-bg-general flex flex-wrap items-center justify-between relative md:w-[13.75rem] z-10 py-4 pl-[15px] pr-[17px]"
      ></nav>
      <div className={'pl-[30px] py-6'}>
        <div className={'mt-16 mb-8'}>
          <EntLogoIcon className={'w-80'} />
        </div>
        <Button
          onClick={handleStart}
          className={'mr-6 w-[120px] h-[40px] text-[16px]'}
          color={'primary'}
        >
          {t('onboard.btn_start')}
        </Button>
      </div>
    </div>
  );
};
export default FinishScreen;
