'use client';

import React, { useEffect } from 'react';

import classNames from 'classnames';
// import useOnBoarding from 'hooks/Ent/useOnBoarding';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import OnboardingStoreProps from 'store/onboarding';

interface OnBoardingHeaderProps {
  setStep: (step: number) => void;
  activeStep: number;
  quizzQuestions: { id: number; translate: string; type: number }[];
}
const OnBoardingHeader = ({ setStep, activeStep = 1, quizzQuestions }: OnBoardingHeaderProps) => {
  const {
    setQuestionId,
    questionId,
    setQuestionContent,
    setQuestionType,
    setQuestionStep,
    questionStep,
  } = OnboardingStoreProps();
  // const { quizzQuestions } = useOnBoarding({});
  const handleClickStep = (step: number, content: string, type: number, index: number) => {
    setQuestionId(step);
    setQuestionContent(content);
    setQuestionType(type);
    setQuestionStep(index);
  };

  useEffect(() => {
    if (quizzQuestions && quizzQuestions.length > 0) {
      const currentQuestion = quizzQuestions.find((question) => question.id === questionId);
      console.log('currentQuestion', currentQuestion);
      if (currentQuestion) {
        setQuestionContent(currentQuestion.translate);
        setQuestionType(currentQuestion.type);
      }
      setStep(quizzQuestions[questionStep]?.id);
      setQuestionId(quizzQuestions[questionStep]?.id);
    }
  }, [questionStep, quizzQuestions]);
  const t = useTranslations();
  return (
    <div className="flex">
      <nav
        // className='md:left-0 md:block md:fixed md:top-0 md:bottom-0 md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden bg-bg-general flex flex-wrap items-center justify-between relative md:w-[13.75rem] z-10 py-4 pl-[15px] pr-[17px]'
        className=" md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden bg-bg-general flex flex-wrap items-center justify-between relative md:w-[13.75rem] z-10 py-4 pl-[15px] pr-[17px]"
      ></nav>
      <div className={' py-3 pl-[30px] relative'}>
        <div>{t('onboard.step')}</div>
        <div className="h-6 rounded-[5px] steps bg-bg-box w-auto inline-flex items-center">
          {quizzQuestions &&
            map(quizzQuestions, (question, index) => (
              <div
                key={question.id}
                onClick={() =>
                  handleClickStep(question.id, question.translate, question.type, index)
                }
                className={classNames(
                  'step rounded-[5px] border border-transparent hover:border-color-border hover:bg-bg-general w-[30px] text-center text-color-minor hover:text-[#272A2F] cursor-pointer',
                  {
                    '!border-color-border bg-bg-general': index === questionStep,
                    'border-r-color-border': index !== questionStep,
                  }
                )}
              >
                {index + 1}
              </div>
            ))}
          {/* <div onClick={() => handleClickStep(1)} className={classNames('step rounded-[5px] border border-transparent hover:border-color-border hover:bg-bg-general w-[30px] text-center text-color-minor hover:text-[#272A2F] cursor-pointer',{
              '!border-color-border bg-bg-general': activeStep === 1,
              'border-r-color-border': activeStep!== 1
            })}>1</div>
            <div onClick={() => handleClickStep(2)} className={classNames('step rounded-[5px] border border-transparent hover:border-color-border hover:bg-bg-general w-[30px] text-center text-color-minor hover:text-[#272A2F] cursor-pointer',{
              '!border-color-border bg-bg-general': activeStep === 2,
              'border-r-color-border': activeStep!== 2
            })}>2</div>
            <div onClick={() => handleClickStep(3)} className={classNames('step rounded-[5px] border border-transparent hover:border-color-border hover:bg-bg-general w-[30px] text-center text-color-minor hover:text-[#272A2F] cursor-pointer',{
              '!border-color-border bg-bg-general': activeStep === 3,
              'border-r-color-border': activeStep!== 3
            })}>3</div>
            <div onClick={() => handleClickStep(4)} className={classNames('step rounded-[5px] border border-transparent hover:border-color-border hover:bg-bg-general w-[30px] text-center text-color-minor hover:text-[#272A2F] cursor-pointer',{
              '!border-color-border bg-bg-general': activeStep === 4
            })}>4</div> */}
        </div>
      </div>
    </div>
  );
};

export default OnBoardingHeader;
