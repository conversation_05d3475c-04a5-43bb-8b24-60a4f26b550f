'use client';

import React, { useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import Button from 'components/Button';
import EntRouters from 'configs/EntRouters';
import { useTranslations } from 'next-intl';
import { NewMemberProps } from 'types/component';

import MemberRegister from './MemberRegister';

const WelcomeScreen = () => {
  const router = useRouter();
  const initMember: NewMemberProps = { memberName: '', pin: '', id: 0 };
  const [memberLists, setMemberLists] = useState<Array<NewMemberProps>>([{ ...initMember }]);
  const handleAddMember = () => {
    setMemberLists([
      ...memberLists,
      {
        ...initMember,
        id: Date.now(),
      },
    ]);
  };
  const updateMemberInfo = (newMember: NewMemberProps) => {
    const listMember = [...memberLists];
    listMember.every((item) => {
      if (item.id === newMember.id) {
        item.memberName = newMember.memberName;
        item.pin = newMember.pin;
      }
      return item;
    });
  };
  // useEffect(() => {
  //   router.push(EntRouters.onboard_question + '1');
  // }, []);
  const handleSaveMember = () => {
    //doSomeThing
    router.push(EntRouters.onboard_question + '1');
  };
  const t = useTranslations();
  return (
    <div className={'pl-[30px] py-6'}>
      <div className={'mt-16 mb-8'}>
        <h4 className={'text-[1.125rem] text-[#3F3934]'}>{t('onboard.student')}</h4>
        <h5 className={'text-color-minor'}>{t('onboard.welcome_note')}</h5>
      </div>
      {memberLists.map((item: NewMemberProps, index) => (
        <MemberRegister memberId={item.id} onUpdate={updateMemberInfo} key={index} />
      ))}

      <div className={'mb-6'}>
        <span className={'text-purple cursor-pointer'} onClick={handleAddMember}>
          {t('onboard.add_member')}
        </span>
      </div>

      <div className={'flex items-center mt-10'}>
        <Button onClick={handleSaveMember} className={'mr-6'}>
          {t('onboard.btn_save')}
        </Button>
        <Link href={'/onboarding/1'} className={'flex items-center'}>
          <span>{t('onboard.skip')}</span>
          <i className={'text-base icon-arrow-right-line text-color-minor'} />
        </Link>
      </div>
    </div>
  );
};
export default WelcomeScreen;
