'use client';

import React, { useEffect, useState } from 'react';

import usePopupStore from '@/store/popup';
import Button from 'components/Button';
// import { useRouter } from 'next/navigation';
import ListCheckBox from 'components/Form/ListCheckBox';
// import OnBoardingHeader from 'containers/onboarding/OnBoardingHeader';
// import { useRouter } from 'next/navigation';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import useEditMember from 'hooks/Ent/useEditMember';
import useOnboardingAnswers from 'hooks/Ent/useOnboardingAnswers';
import { useTranslations } from 'next-intl';
// import Link from 'next/link';
// import useOnBoarding from 'hooks/Ent/useOnBoarding';
import OnboardingStoreProps from 'store/onboarding';

import { useSession } from '@/hooks/useSession';

// import { map } from 'lodash';
interface Answer {
  id: number;
  title: string;
  isChecked: boolean;
}
const QuestionScreen = ({ setStep, step, quizzQuestions }) => {
  const { data: sessionData, update } = useSession();

  const { questionId, setQuestionId, questionContent, questionStep, setQuestionStep } =
    OnboardingStoreProps();
  const t = useTranslations();
  const [answers, setAnswers] = useState<Answer[]>([]);

  const [questionStateId, setQuestionStateId] = useState<number>(
    quizzQuestions.length > 0
      ? (quizzQuestions[questionStep]?.id ?? quizzQuestions[0]?.id)
      : quizzQuestions[0]?.id
  );
  // console.log(quizzQuestions[questionStep].content);
  const { setErrorModal } = usePopupStore();
  const { doChangeMemberById, doTransactionRecommend } = useEditMember();
  // const [step, setStep] = useState<number>(1);
  const { quizzAnswers } = useOnboardingAnswers(questionStateId);
  useEffect(() => {
    if (quizzQuestions.length > 0 && questionStep < quizzQuestions.length) {
      setQuestionStateId(quizzQuestions[questionStep]?.id ?? quizzQuestions[0]?.id);
    }
  }, [questionStep, quizzQuestions]);
  useEffect(() => {
    // const filteredAnswers = quizzAnswers
    //   .filter(answer => answer.question_id === questionId)
    //   .map(answer => ({
    //     id: answer.id,
    //     title: answer.content,
    //     isChecked: false,
    //   }));

    const filteredAnswers = quizzAnswers.map((answer) => ({
      id: answer.id,
      title: answer.content,
      isChecked: false,
    }));

    setQuestionStateId(quizzQuestions[questionStep]?.id);
    // So sánh dữ liệu mới và cũ trước khi gọi setAnswers
    setAnswers((prevAnswers) => {
      const isDifferent = JSON.stringify(prevAnswers) !== JSON.stringify(filteredAnswers);
      return isDifferent ? filteredAnswers : prevAnswers;
    });
    // setQuestionTitle(questionContent);
  }, [questionId, quizzAnswers, questionContent]);

  const handleSaveAnswer = async () => {
    // console.log(9876)
    // console.log()
    try {
      // const response = await saveAnswers(questionId,answerReqs[questionId].id,answerReqs[questionId].content); // Gọi saveFavourite với các tham số cần thiết
      // const { success, message } = response;
      // if (success) {
      //     // Xử lý logic khi lưu thành công
      //     console.log('Successfully saved favourite.');
      //     setQuestionId(questionId + 1)
      // } else {
      //     // Xử lý logic khi gặp lỗi
      //     console.error('Error saving favourite:', message);
      // }
      //       const isLastQuestion = questionId === quizzAnswers.length;
      //       console.log(6544);
      // console.log(isLastQuestion);

      // if (questionStep + 1 === quizzQuestions.length) {
      //   // Nếu là câu hỏi cuối cùng, chuyển hướng đến trang kết thúc
      //   router.push(EntRouters.onboard_finish);
      // } else {
      // Nếu không, chuyển đến câu hỏi tiếp theo
      setQuestionStep(questionStep + 1);
      setStep(step + 1);
      setQuestionId(quizzQuestions[questionStep + 1]?.id);
      // }

      // setQuestionId(questionId + 1)
    } catch (error) {
      // Xử lý logic khi gặp lỗi
      console.error('Error saving favourite:', error);
    }
    // router.push(EntRouters.onboard_finish);
  };

  const handleStart = async () => {
    const res = await doChangeMemberById({
      is_onboard: 2,
      id: sessionData?.member?.id,
    });
    await doTransactionRecommend();

    if (res.success) {
      console.log('Successfully saved update.');
      await update({
        ...sessionData,
        member: {
          ...sessionData?.member,
          is_onboard: 2,
        },
      });
      window.location.assign(EntRouters.home);
      // router.push(EntRouters.home);
      setErrorModal({
        opened: true,
        message: t('member.success.common'),
      });
    } else {
      setErrorModal({
        opened: true,
        message: t('message.error.exit'),
      });
    }
  };
  // console.log(777);
  // console.log(answers);
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      {/* <OnBoardingHeader setStep={setStep} activeStep={step} /> */}

      <div className="flex">
        <nav
          // className='md:left-0 md:block md:fixed md:top-0 md:bottom-0 md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden bg-bg-general flex flex-wrap items-center justify-between relative md:w-[13.75rem] z-10 py-4 pl-[15px] pr-[17px]'
          className=" md:overflow-y-auto md:flex-row md:flex-nowrap md:overflow-hidden bg-bg-general flex flex-wrap items-center justify-between relative md:w-[13.75rem] z-10 py-4 pl-[15px] pr-[17px]"
        ></nav>

        <div className={'pl-[30px] py-6'}>
          <div className={'mt-12 mb-8'}>
            <h5 className={'text-color-minor mb-[20px]'}>
              Câu hỏi này nhằm mục đích tạo ra bài giảng phù hợp nhất dựa trên tình trạng hiện nay
              của bạn.
            </h5>
            <h4 className={'text-color-minor text-[1.125rem] mb-[20px]'}>
              {quizzQuestions[questionStep]?.content}
            </h4>
            <div className="max-w-[550px]">{answers && <ListCheckBox answers={answers} />}</div>
            <div className={'flex items-center mt-10 mb-[90px]'}>
              {questionStep + 1 === quizzQuestions?.length && (
                <Button
                  onClick={handleStart}
                  className={'mr-6 w-[120px] h-[40px] text-[16px]'}
                  color={'primary'}
                >
                  {t('onboard.btn_start')}
                </Button>
              )}
              {questionStep + 1 < quizzQuestions?.length && (
                <Button
                  onClick={handleSaveAnswer}
                  className={'mr-6 w-[120px] h-[40px] text-[16px]'}
                  color={'primary'}
                >
                  {t('onboard.btn_save')}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
  );
};
export default QuestionScreen;
