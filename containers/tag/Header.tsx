'use client';

import React from 'react';

import { Skeleton } from '@heroui/react';
import CreateCoursePopup from 'components/ServicePopup/CreateCoursePopup';
import AppHeader from 'containers/layout/AppHeader';
import { typeStringToIconMap } from 'utils/common';

interface HeaderProps {
  tagTitle: string; // Định nghĩa kiểu dữ liệu của tag_title là string
  tagItem: string;
  isLoading: boolean;
}

const Header: React.FunctionComponent<HeaderProps> = ({ tagTitle, tagItem, isLoading }) => {
  const [open, setOpen] = React.useState(false);

  return (
    <AppHeader>
      <div
        className={
          'py-3 px-[30px] w-full grid grid-cols-12 items-center content-between align-baseline'
        }
      >
        {isLoading ? <Skeleton className={'w-64 h-4'} /> : null}
        <div className={'col-span-6 flex items-center'}>
          <span className="flex items-center gap-x-2">
            <i className={`${typeStringToIconMap[tagItem] || ''} text-[16px]`} />
            {tagTitle}
          </span>
        </div>
        <CreateCoursePopup open={open} onOpen={setOpen} />
      </div>
      <div className={'border-bg-box border-b-[1px] h-0 w-full'}></div>
    </AppHeader>
  );
};

export default Header;
