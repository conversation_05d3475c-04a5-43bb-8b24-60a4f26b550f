'use client';

import React, { useEffect } from 'react';

import Link from 'next/link';
import { useParams } from 'next/navigation';

import TagListSkeleton from '@/containers/tag/skeleton/TagListSkeleton';
import ScrollArea from 'components/ScrollArea';
import Header from 'containers/tag/Header';
import useTags from 'hooks/Ent/useTags';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';
import useSearchStore from 'store/search';
import { typeStringToIconMap } from 'utils/common';

import NoContent from '@/components/NoContent';

const TagList = ({ tag_id = 0, items = '' }) => {
  const params = useParams();
  const { setType, setKeyword } = useSearchStore();
  const { keyword, item } = params;
  useEffect(() => {
    setKeyword(keyword?.toString() || '');
    setType(item?.toString() || '');
  }, []);

  const { tagsDetailList, page, setPage, isLoading, tags, isReachingEnd } = useTags({
    parent_id: tag_id,
    item: items?.toString(),
  });
  if (!isLoading && !tagsDetailList.length)
    return (
      <>
        <Header tagTitle={tags?.title || ''} tagItem={tags?.item || []} isLoading={isLoading} />
        <table className="table-auto w-full">
          <thead>
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[10px]'}>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <NoContent />
              </td>
            </tr>
          </tbody>
        </table>
      </>
    );
  return (
    <>
      <Header tagTitle={tags?.title} tagItem={tags?.item} isLoading={isLoading} />
      <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
        <InfiniteScroll
          height={'calc(116vh - 100px)'}
          dataLength={tagsDetailList.length}
          next={() => setPage(page + 1)}
          hasMore={!isReachingEnd}
          loader={null}
          className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
        >
          <table className="table-auto w-full">
            <thead>
              <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[10px]'}>
                <th></th>
              </tr>
            </thead>
            <tbody className={'text-[0.8123rem]'}>
              {tagsDetailList &&
                map(
                  tagsDetailList,
                  (tagDetail, key) =>
                    tagDetail?.status === 2 && (
                      <tr
                        key={`tr-${key}`}
                        className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box [&>td>div]:hidden [&>td>div]:hover:flex [&>td>button]:hidden [&>td>button]:hover:flex"
                      >
                        <td
                          key={`td-1-${key}`}
                          className={'p-2 pl-[30px] h-[42px] flex items-center'}
                        >
                          {tagDetail?.keyx ? (
                            <Link
                              className="flex items-center gap-x-2"
                              href={`/${tagDetail?.keyx}`}
                            >
                              <i
                                className={`text-medium ${
                                  typeStringToIconMap[tagDetail?.item] || ''
                                }`}
                              />
                              {tagDetail?.title}
                            </Link>
                          ) : (
                            <Link
                              className="flex items-center gap-x-2"
                              href={`/tags/${tagDetail?.id}`}
                            >
                              <i
                                className={`text-medium ${
                                  typeStringToIconMap[tagDetail?.item] || ''
                                }`}
                              />
                              {tagDetail?.title}
                            </Link>
                          )}
                        </td>
                      </tr>
                    )
                )}

              {isLoading ? <TagListSkeleton /> : null}
            </tbody>
          </table>
        </InfiniteScroll>
      </ScrollArea>
    </>
  );
};

// export default HomePage;

export default TagList;
