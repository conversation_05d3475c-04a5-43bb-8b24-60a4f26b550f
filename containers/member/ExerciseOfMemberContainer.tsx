'use client';

import React from 'react';

import Link from 'next/link';

import ExerciseAssignmentSkeleton from '@/containers/class/skeleton/ExerciseAssignmentSkeleton';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import { format } from 'date-fns';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';

import NoContent from '@/components/NoContent';

import useMemberExercise from '@/hooks/Ent/useMemberExercise';

interface ExerciseOfMemberContainerProps {
  memberId: number;
}

const ExerciseOfMemberContainer = ({ memberId }: ExerciseOfMemberContainerProps) => {
  const { memberExercisesList, isLoading, isReachingEnd, page, setPage } = useMemberExercise({
    groupId: -1,
    memberId: memberId,
  });
  console.log(memberExercisesList);

  const t = useTranslations();
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 77px)'}
        dataLength={memberExercisesList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead>
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
                {t('group.assignment.title')}
              </th>
              <th className={'py-1 text-left font-normal'}>{t('group.assignment.startTime')}</th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>
                {t('group.assignment.endTime')}
              </th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>
                {t('group.assignment.quantityMember')}
              </th>
              <th className={'py-1 text-left font-normal pr-[30px]'}>
                {t('group.assignment.complete')}
              </th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {!isLoading && !memberExercisesList.length ? (
              <tr>
                <td colSpan={5}>
                  <NoContent />
                </td>
              </tr>
            ) : (
              map(memberExercisesList, (item, index) => (
                <tr
                  key={index}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group "
                >
                  <td className="py-[12px]">
                    <Link
                      href={`${EntRouters.group}/member/${item.id}`}
                      key={item.id}
                      className="pl-[30px] py-[12px]"
                    >
                      {item.paragraph?.title}
                    </Link>
                  </td>
                  <td>
                    {item?.created_at
                      ? format(new Date(item?.created_at * 1000), 'dd/MM/yyyy')
                      : 'N/A'}
                  </td>
                  <td>
                    {item?.created_at
                      ? format(new Date(item?.created_at * 1000), 'dd/MM/yyyy')
                      : 'N/A'}
                  </td>
                  <td>{item?.member_id || ''}</td>
                  <td>{item?.member_id || 0}</td>
                </tr>
              ))
            )}
            {isLoading ? <ExerciseAssignmentSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default ExerciseOfMemberContainer;
