import { Skeleton } from '@heroui/react';

const LoginMemberSkeleton = () => {
  return (
    <>
      <div className="container mx-auto px-4 h-full">
        <div className="flex content-center items-center justify-center h-full">
          <div className="w-full lg:w-4/12 px-4">
            <div className="relative border-0 flex flex-col min-w-0 break-words w-full mb-2 ">
              <div className="flex-auto px-4 lg:px-10  pt-10">
                <div className="flex items-center justify-center">
                  <Skeleton className="rounded-full w-[128px] h-[128px] object-center" />
                </div>
                <Skeleton className="w-full h-[19.5px] my-[10px]" />
                <Skeleton className="w-full h-[19.5px] my-[10px]" />
                <div className={'text-center text-xl mb-[40px]'}></div>
                <div className="relative w-full my-3 text-center">
                  <Skeleton className="w-full h-[19.5px] " />
                  {/* <div className={'text-color-major'}>{t('auth.password')}</div> */}
                </div>
                <div className="flex justify-between w-full max-w-[232px] mx-auto mb-[40px]">
                  <Skeleton className="w-[50px] h-[50px]" />
                  <Skeleton className="w-[50px] h-[50px]" />
                  <Skeleton className="w-[50px] h-[50px]" />
                  <Skeleton className="w-[50px] h-[50px]" />
                </div>

                <div className={'flex justify-center'}>
                  <Skeleton className="w-[150px] h-[35px]" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default LoginMemberSkeleton;
