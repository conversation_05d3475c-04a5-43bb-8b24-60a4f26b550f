'use client';

import React, { useMemo, useState } from 'react';

import EntRouters from '@/configs/EntRouters';
import DashboardHeader from '@/containers/class/dashboard/DashboardHeader';
import MemberDetailReportContainer from '@/containers/class/member/MemberDetailReportContainer';
import AppHeader from '@/containers/layout/AppHeader';
import ExerciseOfMemberContainer from '@/containers/member/ExerciseOfMemberContainer';
import { Tooltip } from '@heroui/react';
import { useTranslations } from 'next-intl';

import Avatar from '@/components/Avatar';
import Button from '@/components/Button';
import Tabs from '@/components/Tabs';

const MemberDetailContainer = ({ member }) => {
  const t = useTranslations();
  const [activeTab, setActiveTab] = useState('dashboard');

  const tabs = useMemo(() => {
    return [
      {
        id: 1,
        keyx: 'dashboard',
        title: t('group.tab.dashboard'),
      },
      {
        id: 4,
        keyx: 'exercise',
        title: t('group.tab.assignment'),
      },
    ];
  }, [t]);

  const handleChangeTab = (newActiveTab: string | number | boolean) => {
    const tabKey = String(newActiveTab);
    setActiveTab(tabKey);
  };

  const copyLink = () => {
    const generateLink = window.location.origin + EntRouters.member_login + '/' + member.token;
    navigator.clipboard.writeText(generateLink).catch((err) => {
      console.error('Lỗi khi sao chép: ', err);
    });
  };
console.log('member', member);
  return (
    <>
      <AppHeader bottom={activeTab === 'dashboard' ? '10px' : '0px'}>
        <div className={'px-[30px] w-full grid grid-cols-12 items-center gap-2 py-2'}>
          <div className={'col-span-6 flex gap-2 items-center'}>
            {/* <Avatar name={member.id.toString()} image={member?.avatar} /> */}
            <Avatar name={member.id.toString()} className={'mr-2'} size={18}/>
            <div className={'flex items-center'}>
              <a href={`${EntRouters.member_login}/${member?.token}`}>{member?.nickname}</a>
            </div>

            <Tooltip showArrow={true} content={t('group.class.linkMemberLogin')}>
              <Button
                color={'default'}
                size={'icon'}
                className={'bg-[transparent] hover:bg-bg-box'}
                onClick={copyLink}
              >
                <i className="text-medium icon-links-line" />
              </Button>
            </Tooltip>
          </div>
          <div className="col-span-12">
            <div className={'flex items-center justify-between'}>
              <Tabs
                tabs={tabs}
                value={'keyx'}
                activeTabId={activeTab}
                onSelectionChange={handleChangeTab}
              />

              {activeTab === 'dashboard' && <DashboardHeader />}
            </div>
          </div>
        </div>
      </AppHeader>

      {activeTab === 'dashboard' && <MemberDetailReportContainer memberId={member?.id} />}
      {activeTab === 'exercise' && <ExerciseOfMemberContainer memberId={member?.id} />}
    </>
  );
};

export default MemberDetailContainer;
