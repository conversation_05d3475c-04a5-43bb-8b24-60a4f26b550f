'use client';
import React, { useEffect, useRef, useState } from 'react';
import { Character } from '@/types/model';
import useCharacter from '@/hooks/Ent/useCharacter';
import { EditSentenceItem } from '@/types/component';
import classNames from 'classnames';
import ScrollArea from '@/components/ScrollArea';
import { SentenceUpdateEntity } from '@/types/hooks';
import useSentence from '@/hooks/Ent/useSentence';
import { Textarea } from '@nextui-org/input';


interface AutocompleteProps {
  sentence: EditSentenceItem,
  setCharacterName: (char: string) => void
  setOpenModalCharacter: (open: boolean) => void
  setItems: (dragItems: (prevDragItems: EditSentenceItem[]) => EditSentenceItem[]) => void;
}

export default function InputCharacterInstant({
                                         setCharacterName,
                                         sentence,
                                         setOpenModalCharacter,
                                         setItems,
                                       }: AutocompleteProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocus, setIsFocus] = useState(false);
  const [fullName, setFullName] = useState(sentence.fullname ?? '');
  const [keyword, setKeyword] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [hasUserTyped, setHasUserTyped] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const isItemClickedRef = useRef(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const { characters } = useCharacter(keyword, sentence);
  const { upsertSentence } = useSentence();
  useEffect(() => {
    const keysPressed: any = {};
    const keyDownHandler = (event: KeyboardEvent) => {
      keysPressed[event.key] = true;
      if (Object.keys(keysPressed).length === 1 && event.key === 'Enter') {
        setHasUserTyped(false);
        if (keyword !== '' && keyword !== sentence.fullname && isFocus) {
          if (isOpen && characters.length > 0) {
            setKeyword('');
            setFullName(characters[0].fullname);
            handleSelectItem(characters[0]);
          } else {
            isItemClickedRef.current = true;
            setOpenModalCharacter(true);
          }
        }
      }
    };
    const keyUpHandler = (event: KeyboardEvent) => {
      delete keysPressed[event.key];
    };

    window.addEventListener('keydown', keyDownHandler);
    window.addEventListener('keyup', keyUpHandler);
    return () => {
      window.removeEventListener('keydown', keyDownHandler);
      window.removeEventListener('keyup', keyUpHandler);
    };
  });
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  useEffect(() => {
    if (!hasUserTyped) return;
    const debounceTimer = setTimeout(() => setKeyword(fullName), 300);
    return () => clearTimeout(debounceTimer);
  }, [fullName]);
  useEffect(() => {
    if (keyword.length > 0) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [keyword]);
  useEffect(() => {
    if (dropdownRef.current && selectedIndex >= 0) {
      const listItems = dropdownRef.current.querySelectorAll('.character-item');
      const selectedItem = listItems[selectedIndex];
      if (selectedItem) {
        selectedItem.scrollIntoView({ block: 'nearest' });
      }
    }
  }, [selectedIndex]);
  const handleInputChange = (value: string) => {
    setHasUserTyped(true);
    setCharacterName(value);
    setFullName(value);
  };
  const handleSelectItem = async (character: Character) => {
    isItemClickedRef.current = true;
    setFullName(character.fullname);
    setIsOpen(false);
    setIsFocus(false);
    setFocusedIndex(character.id);
    setHasUserTyped(false);
    const paramUpdateSentence: SentenceUpdateEntity = {
      character_id: character.id,
      id: sentence.id,
      paragraph_id: sentence.paragraph_id,
      document_id: sentence.document_id,
      course_id: sentence.course_id,
      position: sentence.position,
    };
    if(!sentence.is_new) {
      const res = await upsertSentence([paramUpdateSentence]);
      if (res.success) {
        setItems((prevDragItems) =>
          prevDragItems.map(item =>
            item.id === sentence.id ? {
              ...item,
              character_id: character.id,
              fullname: character.fullname,
              raw_fullname: character.fullname,
            } : item,
          ),
        );
      } else {
        //bao loi
      }
    }else{
      setItems((prevDragItems) =>
        prevDragItems.map(item =>
          item.id === sentence.id ? {
            ...item,
            character_id: character.id,
            fullname: character.fullname,
            raw_fullname: character.fullname,
          } : item,
        ),
      );
    }
  };
  const handleFocus = () => {
    setIsFocus(true);
  };

  const handleBlur = () => {
    setTimeout(() => {
      setHasUserTyped(false);
      setIsOpen(false);
      console.log(isItemClickedRef.current);
      if (!isItemClickedRef.current) {
        setIsFocus(false);
        setKeyword('');
        //nếu có list nhân vật mà bấm ra ngoài thì hủy chọn, còn nếu không có list nhân vật dc filter thì đê
        //nguyên để add mới nhân vật đó
        if (characters.length) setFullName(sentence.fullname ?? '');
      }
      isItemClickedRef.current = false;

    }, 100);

  };
  // @ts-ignore
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (!isOpen) return;
    console.log(e.key);
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prevIndex =>
          prevIndex < characters.length - 1 ? prevIndex + 1 : prevIndex
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prevIndex => (prevIndex > 0 ? prevIndex - 1 : 0));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < characters.length) {
          handleSelectItem(characters[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        break;
    }
  };
  return (
    <div className={'relative'}>
      <Textarea
        minRows={1}
        maxRows={1}
        ref={inputRef}
        defaultValue={fullName}
        value={fullName}
        onValueChange={handleInputChange}
        placeholder='Type to search...'
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        data-focus-visible={false}
        label={''}
        width='100%'
        tabIndex={0}
        variant={'bordered'}
        classNames={{
          base: 'active:!bg-transparent active:!border-none active:!outline-none bg-transparent focus:!bg-transparent focus:!outline-none focus:!border-none focus-visible:!outline-none focus-visible:!border-none',
          mainWrapper:
            'active:!border-none focus:!bg-transparent focus:!outline-none focus:!border-none focus-within:!bg-transparent active:!bg-transparent focus-visible:!border-none focus-visible:!outline-none focus-visible:!shadow-none',
          inputWrapper:
            'p-0 !border-none !shadow-none active:!border-none !bg-transparent hover:!bg-transparent focus:bg-bg-box w-full min-h-6 focus:!bg-transparent focus-visible:!outline-none focus-visible:!border-none',
          innerWrapper:
            '!bg-transparent active:!border-none hover:!bg-transparent focus:bg-bg-box w-full focus:!bg-transparent focus:!outline-none focus-visible:!outline-none focus-visible:!border-none',
          input:
            'text-medium active:!bg-transparent active:!border-none active:!outline-none bg-transparent focus:!bg-transparent focus:!outline-none focus:!border-none !h-auto focus-visible:!outline-none focus-visible:!border-none leading-4',
        }}
      />

      {isOpen && characters.length > 0 && (
        <div ref={dropdownRef}>
          <ScrollArea
            className='absolute z-10 mt-1 bg-bg-general min-w-10 max-w-24 w-auto shadow-medium border border-color-border rounded-md max-h-60 overflow-auto'
          >
            {characters.map((item, index) => (
              <div
                key={item.id}
                onClick={() => handleSelectItem(item)}
                className={classNames('character-item px-4 min-w-10 w-full text-left py-2 cursor-pointer hover:bg-bg-box whitespace-nowrap', {
                  'bg-bg-box': item.id === focusedIndex || index === selectedIndex,
                })}
                onMouseDown={() => handleSelectItem(item)}
              >
                {item.fullname}
              </div>
            ))}
          </ScrollArea>
        </div>
      )}
    </div>
  );
}