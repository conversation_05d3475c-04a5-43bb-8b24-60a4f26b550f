import React, { FC, useRef, useState } from 'react';
import { SentenceItemProps } from 'types/component';
import Avatar from 'components/Avatar';
import { Textarea } from '@nextui-org/input';
import InputCharacter from '@/containers/paragraph/edit/InputCharacter';
import useSentence from '@/hooks/Ent/useSentence';
import { SentenceUpdateEntity, UpsertCharacterEntity } from '@/types/hooks';
import usePopupStore from '@/store/popup';
import useCharacter from '@/hooks/Ent/useCharacter';

export const SentenceItemInstant: FC<SentenceItemProps> = ({
                                                      sentence,
                                                      onFocusSentence,
                                                      setCharacterName,
                                                      setOpenModalCharacter,
                                                      setItems,
                                                    }) => {
  const [, setRandom] = useState(0);
  const [content, setContent] = useState(sentence.content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { upsertSentence } = useSentence();
  const { upsertCharacter } = useCharacter('', sentence);
  const { setErrorModal } = usePopupStore();
  const [isChange, setIsChange] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const handleChangeContent = (cont: string) => {
    setIsChange(true);
    if (content === sentence.content) {
      setIsChange(false);
    }
    setContent(cont);
  };

  const handleSaveSentence = async (sentence) => {
    if (!isChange || isSaving) return;
    const paramUpdateSentence: SentenceUpdateEntity = {
      ...sentence,
      content: content,
    };
    if (sentence.is_new) {
      delete paramUpdateSentence.id;
    }
    setIsSaving(true);
    const res = await upsertSentence([paramUpdateSentence]);
    if (res.success) {
      //tiếp tục cập nhật character
      if (sentence.is_new && res.data?.id) {
        const paramsUpsertCharacter: UpsertCharacterEntity = {
          fullname: sentence.fullname,
          gender: sentence.gender,
          age: sentence.age,
          accent: sentence.accent,
          sentence_current_id: sentence.id,
          paragraph_id: sentence.paragraph_id,
          course_id: sentence.course_id,
        };
        const res = await upsertCharacter(paramsUpsertCharacter);
        if (res.success) {
          setItems((prevDragItems) =>
            prevDragItems.map(item =>
              item.id === sentence.id ? {
                ...item,
                content: content,
                id: sentence.is_new ? res.data.id : sentence.id,
                is_new: false,
              } : item,
            ),
          );
          setRandom(Date.now);
        } else {
          setErrorModal({ opened: true, message: 'Có lỗi khi cập nhật character' });
        }
      }
    } else {
      setErrorModal({ opened: true, message: 'Có lỗi khi cập nhật câu' });
    }
    setIsSaving(false);
    setIsChange(false);
  };
  //useClickOutside(textareaRef, () => handleSaveSentence(focusItem));
  const handleFocus = () => {
    onFocusSentence(sentence);
  };
  return (
    <div
      onClick={handleFocus}
      className={
        'border-b-1 min-h-10 border-bg-box relative pb-2 mb-2 flex items-center w-full'
      }
    >
      <div className={'w-36 text-center flex-shrink-0'}>
        <div className={'flex gap-2 mr-2'}>
          <Avatar name={sentence.fullname ?? ''} size={24} className={'mt-1 flex-shrink-0'} />
          <InputCharacter setItems={setItems} setCharacterName={setCharacterName} sentence={sentence}
                          setOpenModalCharacter={setOpenModalCharacter} />
        </div>
      </div>
      <div className={'w-full pr-6'}>
        <Textarea
          ref={textareaRef}
          value={content}
          defaultValue={content}
          onValueChange={handleChangeContent}
          tabIndex={0}
          onFocus={handleFocus}
          onBlur={() => handleSaveSentence(sentence)}
          onMouseEnter={() => handleSaveSentence(sentence)}
          variant={'bordered'}
          minRows={1}
          classNames={{
            base: 'active:!bg-transparent active:!border-none active:!outline-none bg-transparent focus:!bg-transparent focus:!outline-none focus:!border-none focus-visible:!outline-none focus-visible:!border-none',
            mainWrapper:
              'active:!border-none focus:!bg-transparent focus:!outline-none focus:!border-none focus-within:!bg-transparent active:!bg-transparent focus-visible:!border-none focus-visible:!outline-none focus-visible:!shadow-none',
            inputWrapper:
              'p-0 !border-none !shadow-none active:!border-none !bg-transparent hover:!bg-transparent focus:bg-bg-box w-full min-h-6 focus:!bg-transparent focus-visible:!outline-none focus-visible:!border-none',
            innerWrapper:
              '!bg-transparent active:!border-none hover:!bg-transparent focus:bg-bg-box w-full focus:!bg-transparent focus:!outline-none focus-visible:!outline-none focus-visible:!border-none',
            input:
              'text-medium active:!bg-transparent active:!border-none active:!outline-none bg-transparent focus:!bg-transparent focus:!outline-none focus:!border-none !h-auto focus-visible:!outline-none focus-visible:!border-none leading-4',
          }}
          label=''
          placeholder='Điền nội dung'
        />
      </div>
    </div>
  );
};
