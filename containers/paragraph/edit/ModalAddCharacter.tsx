'use client';

import React, { ChangeEvent, useEffect, useMemo, useState } from 'react';

import { ACCENTS, AGES, GENDERS } from '@/configs';
import usePopupStore from '@/store/popup';
import { EditSentenceItem, ModalAddCharacterProps, OptionItem } from '@/types/component';
import { UpsertCharacterEntity } from '@/types/hooks';
import { forEach, map } from 'lodash';
import { useTranslations } from 'next-intl';

import Modal from '@/components/Modal';
import Select from '@/components/Select';
import ErrorModal from '@/components/ServicePopup/ErrorModal';
import Tabs from '@/components/Tabs';

import useCharacter from '@/hooks/Ent/useCharacter';

export const CharacterItem = ({ character }: { character: EditSentenceItem }) => {
  const genders = useMemo(() => GENDERS, []);
  const ages = useMemo(() => AGES, []);
  const accents: OptionItem[] = useMemo(() => ACCENTS, []);
  const [gender, setGender] = useState(GENDERS[0].id);
  const [age, setAge] = useState(AGES[0].id);
  const [accent, setAccent] = useState(ACCENTS[0].id);
  const handleAgeChange = (e: ChangeEvent<HTMLSelectElement>) => {
    setAge(e.target.value);
  };

  const handleVoiceChange = (e: ChangeEvent<HTMLSelectElement>) => {
    setAccent(e.target.value);
  };
  const handleGenderChange = (selectedGender) => {
    setGender(selectedGender);
  };

  return (
    <div className={'grid grid-cols-3 gap-x-1 gap-y-2'}>
      <div className={'col-span-1'}>{character.fullname}</div>
      <div className={'col-span-2'}>
        <div className="flex justify-between items-center gap-1 w-full">
          <Tabs
            classNames={{
              tab: 'px-2',
            }}
            tabs={genders}
            onClick={handleGenderChange}
            activeTabId={gender}
          />

          <Select
            label={'Chọn'}
            size={'sm'}
            selectedKeys={[age]}
            items={ages}
            onChange={handleAgeChange}
            classNames={{
              base: 'h-6 w-auto',
              trigger: '!h-6 !min-h-6 w-32 text-tiny',
              label: '!hidden gap-0',
              innerWrapper: 'gap-0 !pt-0',
            }}
          />

          <Select
            label={'Chọn'}
            size={'sm'}
            selectedKeys={[accent]}
            items={accents}
            onChange={handleVoiceChange}
            classNames={{
              base: 'h-6 w-auto',
              trigger: '!h-6 !min-h-6 w-32 text-tiny',
              label: '!hidden gap-0',
              innerWrapper: 'gap-0 !pt-0',
            }}
          />
        </div>
      </div>
    </div>
  );
};
const ModalAddCharacter = ({
  dragItems,
  sentence,
  opened,
  onOpenChange,
  setDragItems,
  onSuccess,
}: ModalAddCharacterProps) => {
  const t = useTranslations();
  const { upsertCharacter } = useCharacter('', sentence);
  const { setErrorModal } = usePopupStore();
  const [characters, setCharacters] = useState<EditSentenceItem[]>([]);
  useEffect(() => {
    const chars: EditSentenceItem[] = [];
    forEach(dragItems, (item) => {
      if (item.character_id === 0) {
        chars.push(item);
      }
    });
    setCharacters(chars);
  }, [dragItems]);

  const handleSaveCharacter = async () => {
    if (characters.length === 0) return;
    const request = map(characters, (item) => {
      const paramsUpsertCharacter: UpsertCharacterEntity = {
        fullname: item.fullname ?? '',
        raw_fullname: item.raw_fullname ?? '',
        gender: item.gender ?? '',
        age: item.age ?? '',
        accent: item.accent ?? '',
        paragraph_id: item.paragraph_id,
        course_id: item.course_id,
      };
      return upsertCharacter(paramsUpsertCharacter).then((res) => res.data);
    });
    const response = await Promise.all(request);
    reRenderList(response);
    if (response.length === characters.length) {
      onOpenChange(false);
    } else {
      setErrorModal({ opened: true, message: 'Có lỗi khi cập nhật character' });
    }
  };

  const reRenderList = (response) => {
    const changeItems: EditSentenceItem[] = [];
    const newList: EditSentenceItem[] = forEach(dragItems, (item) => {
      const character = response.find((res) => res.fullname === item.fullname);
      if (!character) return item;
      const mappedItem = {
        ...item,
        character_id: character.id ?? item.character_id,
        paragraph_id: character.paragraph_id,
        course_id: character.course_id,
        fullname: item.fullname,
        raw_fullname: item.fullname,
        gender: item.gender,
        age: item.age,
        accent: item.accent,
      };
      changeItems.push({
        ...mappedItem,
        id: 0,
      });
      return mappedItem;
    });
    setDragItems([...newList]);

    onSuccess(changeItems);
  };

  return (
    <>
      <Modal
        size={'xl'}
        opened={opened}
        onOpenChange={onOpenChange}
        header={t('group.class.addCharacter')}
        submitLabel={'Lưu'}
        onSubmit={handleSaveCharacter}
      >
        {characters &&
          map(characters, (character, index) => (
            <CharacterItem character={character} key={index} />
          ))}
      </Modal>
      <ErrorModal />;
    </>
  );
};
export default ModalAddCharacter;
