'use client';

import { But<PERSON> } from '@/components';
import { StatusEnum } from '@/configs/StatusEnum';
import Header from '@/containers/paragraph/edit/Header';
import ModalAddCharacter from '@/containers/paragraph/edit/ModalAddCharacter';
import SentenceItem from '@/containers/paragraph/edit/SentenceItem';
import EditParagraphSkeleton from '@/containers/paragraph/skeleton/EditParagraphSkeleton';
import { EditSentenceItem } from '@/types/component';
import { SentencePositionEntity, SentenceUpdateEntity } from '@/types/hooks';
import { Character, SentenceEntity } from '@/types/model';
import { forEach } from 'lodash';

import DragContext from '@/components/Drags/DragContext';
import DragItem from '@/components/Drags/DragItem';
import Modal from '@/components/Modal';
import ScrollArea from '@/components/ScrollArea';
import ErrorModal from '@/components/ServicePopup/ErrorModal';
import { Tooltip } from '@/components/Tooltip';

import useSentence from '@/hooks/Ent/useSentence';

const EditParagraphContainer = () => {
  // const t = useTranslations();
  // const router = useRouter();
  // const params = useParams();

  // const paragraph_id_str = params.paragraph_id?.toString() || '0';
  // useEffect(() => {
  //   if (!paragraph_id_str || parseInt(paragraph_id_str) === 0) router.back();
  //   return () => {};
  // }, [params]);

  // const { paragraph } = useParagraph(paragraph_id_str, false);

  // const { documentList } = useDocuments({
  //   course_id: activeParagraph?.course_id ? activeParagraph.course_id : SPECIAL_COURSE_ID,
  //   shouldNotFetch: !activeParagraph || activeParagraph.course_id === SPECIAL_COURSE_ID,
  // });
  // useEffect(() => {
  //   if (documentList?.length) {
  //     setDocuments(documentList ?? []);
  //     const activeDocument = documentList.find((doc) => doc.id === activeParagraph?.document_id);
  //     if (activeDocument) {
  //       setActiveDocument(activeDocument);
  //       setCourse(activeDocument.course || null);
  //       setParagraphs(activeDocument?.paragraphs || []);
  //     }
  //   }
  // }, [documentList]);

  // const { sentences, characters, doChangeSentencesPosition, mutate, isLoading } = useConversations(
  //   LearnTypeEnum.APPROVE,
  // );

  // const [openModalResult, setOpenModalResult] = useState<boolean>(false);
  // const [openModalCharacter, setOpenModalCharacter] = useState<boolean>(false);

  // const [dragItems, setDragItems] = useState<EditSentenceItem[]>([]);
  // const [focusItem, setFocusItem] = useState<EditSentenceItem | null>(null);

  // useEffect(() => {
  //   const keysPressed: any = {};
  //   const keyDownHandler = (event: KeyboardEvent) => {
  //     if (event.key === 'Tab' && handleAddItem) {
  //       const itemsLength = dragItems.length;
  //       if (focusItem && focusItem.id === dragItems[itemsLength - 1].id) {
  //         event.preventDefault();
  //         if (focusItem.content !== '') handleAddItem(itemsLength);
  //       }
  //     }
  //   };
  //   const keyUpHandler = (event: KeyboardEvent) => {
  //     delete keysPressed[event.key];
  //   };

  //   window.addEventListener('keydown', keyDownHandler);
  //   window.addEventListener('keyup', keyUpHandler);
  //   return () => {
  //     window.removeEventListener('keydown', keyDownHandler);
  //     window.removeEventListener('keyup', keyUpHandler);
  //   };
  // });

  // useEffect(() => {
  //   if (sentences.length) {
  //     let currentMapped: SentenceEntity[] = [];
  //     const mappedSentences: SentenceEntity[][] = [];
  //     sentences.map((sentence: SentenceEntity, index: number) => {
  //       if (sentence.status !== 2) return;
  //       if (
  //         currentMapped.length === 0 ||
  //         (sentence.sentence_group_id !== undefined &&
  //           sentence.sentence_group_id === sentences[index].sentence_group_id)
  //       ) {
  //         currentMapped.push(sentence);
  //       } else {
  //         mappedSentences.push(currentMapped);
  //         currentMapped = [sentence];
  //       }
  //     });
  //     // Thêm group cuối cùng
  //     if (currentMapped.length > 0) {
  //       mappedSentences.push(currentMapped);
  //     }
  //     const listCards: EditSentenceItem[] = [];
  //     mappedSentences.map((sentences) => {
  //       const contents: Array<string> = [];
  //       const item: SentenceEntity = sentences[0];
  //       const character = characters.find((char: Character) => char.id === item.character_id);
  //       sentences.map((sentence) => contents.push(sentence?.content));

  //       listCards.push({
  //         id: item.id,
  //         content: contents.join(' '),
  //         character_id: item.character_id,
  //         is_new: false,
  //         hasChange: false,
  //         position: item.position ?? 0,
  //         paragraph_id: item.paragraph_id,
  //         document_id: item.document_id,
  //         course_id: item.course_id,
  //         raw_content: item.content,

  //         fullname: character?.fullname ?? '',
  //         raw_fullname: character?.fullname ?? '',
  //         voice_id: character?.voice_id ?? 0,
  //         age: character?.age ?? '',
  //         gender: character?.gender ?? '',
  //         accent: character?.accent ?? '',
  //       });
  //     });
  //     const sorted = sortBy(listCards, ['position']);
  //     setDragItems(sorted);
  //   }
  // }, [sentences]);

  // const handleSaveParagraph = async () => {
  //   const entities: SentenceUpdateEntity[] = [];
  //   const characters: Character[] = [];
  //   // kiểm tra các item, nếu thêm mới character thì phải thêm mới trươc, sau đó mới thêm/update câu
  //   forEach(dragItems, (items) => {
  //     if (items.hasChange) {
  //       if (items.character_id === 0) {
  //         characters.push({
  //           ...items,
  //           description: '',
  //           sentence_current_id: items.id,
  //         } as Character);
  //       } else {
  //         entities.push(items);
  //       }
  //     }
  //   });

  //   if (isSaving) return;
  //   setSaving(true);
  //   if (characters.length) {
  //     setOpenModalCharacter(true);
  //   } else {
  //     await handleSaveParagraphAfterSaveCharacter(entities);
  //   }
  // };
  // const handleSaveParagraphAfterSaveCharacter = async (data) => {
  //   if (!data.length) {
  //     setSaving(false);
  //     return;
  //   }
  //   const res = await upsertSentence(data);
  //   setSaving(false);
  //   if (res.success) {
  //     setHasChangeSentence(false);
  //     setOpenModalResult(true);
  //   }
  // };
  // const handleClose = async () => {
  //   await mutate();
  // };
  // const handleAddItem = (index: number) => {
  //   const cloneCard = dragItems;
  //   const newSentence = {
  //     ...dragItems[index],
  //     id: Date.now(),
  //     content: '',
  //     character_id: 0,
  //     is_new: true,
  //     fullname: '',
  //     raw_fullname: '',
  //     raw_content: '',
  //     gender: '',
  //     age: '',
  //     accent: '',
  //     voice_id: 0,
  //     position: index + 1,
  //   };
  //   cloneCard.splice(index + 1, 0, newSentence);
  //   setDragItems([...cloneCard]);
  // };

  // const { upsertSentence } = useSentence();
  // const handleRemove = (id: number) => {
  //   const removeItemIndex = dragItems.findIndex((item: SentenceEntity) => item.id === id);
  //   if (removeItemIndex > -1) {
  //     const paramUpdateSentence: SentenceUpdateEntity[] = [];
  //     paramUpdateSentence.push({
  //       id: dragItems[removeItemIndex].id,
  //       status: StatusEnum.OFF,
  //     });
  //     const newLists: EditSentenceItem[] = dragItems.filter(
  //       (item: SentenceEntity) => item.id !== id,
  //     );
  //     if (removeItemIndex < dragItems.length) {
  //       for (let i = removeItemIndex + 1; i < dragItems.length; i = i + 1) {
  //         const item = dragItems[i];
  //         const newPosition = item.position && item.position > 0 ? item.position - 1 : 0;
  //         paramUpdateSentence.push({
  //           id: item.id,
  //           position: newPosition,
  //         });
  //         newLists.map((listItem) =>
  //           listItem.id === item.id ? { ...listItem, position: newPosition } : listItem,
  //         );
  //       }
  //     }
  //     upsertSentence(paramUpdateSentence).then(() => {
  //       setDragItems(newLists);
  //     });
  //   }
  // };

  // const handleDragEnd = (lists: EditSentenceItem[]) => {
  //   const sentencePositions: SentencePositionEntity[] = [];
  //   lists.map(
  //     (item, index: number) =>
  //       item.position !== index &&
  //       sentencePositions.push({
  //         id: item.id,
  //         position: index,
  //       }),
  //   );
  //   doChangeSentencesPosition(sentencePositions);
  // };

  // return (
  //   <div>
  //     <Header />

  //     <div className="flex items-center pl-[30px] pr-5 pb-3 justify-between w-full">
  //       <Tooltip key={'tooltip-title-paragraph'} content={activeParagraph?.title || ''}>
  //         <div
  //           className={
  //             'text-[14px] text-color-major font-bold font-[Inter] overflow-hidden text-ellipsis hover:text-clip'
  //           }
  //         >
  //           {activeParagraph?.title || ''}
  //           {activeParagraph?.process_approve === StatusEnum.ON && (
  //             <span className="rounded-full ml-1 inline-block w-3 h-3 bg-white border-1 border-purple-100 leading-none">
  //               <i className={'text-base w-2 h-2 icon-ok-circled text-purple-100'} />
  //             </span>
  //           )}
  //         </div>
  //       </Tooltip>
  //       {hasChangeSentence && (
  //         <div className={'col-span-4 flex items-center justify-end'}>
  //           <Button onClick={handleSaveParagraph} size={'sm'} color={'primary'}>
  //             {t('paragraph.edit.btnSave')}
  //           </Button>
  //         </div>
  //       )}
  //     </div>

  //     <div className={'px-2 bg-color-line h-8 w-full'}>
  //       <div className={'w-full h-8 flex px-[calc((100%_-_615px)_/_2)] py-2'}>
  //         <div className={'w-10 flex-shrink-0 text-left'}></div>
  //         <div className={'w-36 text-left flex-shrink-0'}>
  //           {t('paragraph.edit.label_title_character')}
  //         </div>
  //         <div className={''}>{t('paragraph.edit.label_title_content')}</div>
  //       </div>
  //     </div>
  //     <ScrollArea className={'w-full h-[calc(100vh_-_133px)] px-[calc((100%_-_615px)_/_2)] py-2'}>
  //       {isLoading ? <EditParagraphSkeleton /> : null}
  //       <DragContext setItems={setDragItems} items={dragItems} onDragEnd={handleDragEnd}>
  //         {dragItems.map((item: EditSentenceItem, index: number) => (
  //           <DragItem
  //             key={item.id}
  //             id={item.id}
  //             position={index}
  //             handleAddItem={handleAddItem}
  //             handleRemoveItem={handleRemove}
  //             className={'pl-10'}
  //             removeItemClassName={'right-0 absolute'}
  //             dragClassName={'right-[unset] w-10 left-0'}
  //           >
  //             <SentenceItem
  //               sentence={item}
  //               setItems={setDragItems}
  //               items={dragItems}
  //               onFocusSentence={setFocusItem}
  //               setHasChangeSentence={setHasChangeSentence}
  //               key={index}
  //             />
  //           </DragItem>
  //         ))}
  //       </DragContext>
  //     </ScrollArea>
  //     <Modal
  //       header={'Thông báo'}
  //       size={'xs'}
  //       opened={openModalResult}
  //       onOpenChange={setOpenModalResult}
  //       onClose={handleClose}
  //       classNames={{
  //         footer: 'px-4',
  //       }}
  //     >
  //       <div className={'text-center'}>
  //         <span dangerouslySetInnerHTML={{ __html: t('paragraph.edit.content_save_success') }} />
  //       </div>
  //     </Modal>
  //     <ModalAddCharacter
  //       setDragItems={setDragItems}
  //       sentence={focusItem!}
  //       dragItems={dragItems}
  //       onSuccess={handleSaveParagraphAfterSaveCharacter}
  //       opened={openModalCharacter}
  //       onOpenChange={setOpenModalCharacter}
  //     />
  //     <ErrorModal />
  //   </div>
  // );
  return null;
};
export default EditParagraphContainer;
