'use client';

import React, { FC, useRef, useState } from 'react';

import InputCharacter from '@/containers/paragraph/edit/InputCharacter';
import { Textarea } from '@heroui/react';
import Avatar from 'components/Avatar';
import { useTranslations } from 'next-intl';
import { SentenceItemProps } from 'types/component';

const SentenceItem: FC<SentenceItemProps> = ({
  sentence,
  onFocusSentence,
  setItems,
  setHasChangeSentence,
}) => {
  const t = useTranslations();
  const [content, setContent] = useState(sentence.content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const handleChangeContent = (cont: string) => {
    if (cont !== sentence.content) {
      setHasChangeSentence(true);
    } else {
      setHasChangeSentence(false);
    }
    setContent(cont);
    setItems((prevDragItems) =>
      prevDragItems.map((item) =>
        item.id === sentence.id
          ? {
              ...item,
              content: cont,
              id: sentence.is_new ? 0 : sentence.id,
              hasChange: true,
            }
          : item
      )
    );
  };
  const handleFocus = () => {
    onFocusSentence(sentence);
  };
  return (
    <div
      onClick={handleFocus}
      className={'border-b-1 min-h-10 border-bg-box relative pb-2 mb-2 flex items-center w-full'}
    >
      <div className={'w-36 text-center flex-shrink-0'}>
        <div className={'flex gap-2 mr-2'}>
          <Avatar name={sentence.fullname ?? ''} size={24} className={'mt-1 flex-shrink-0'} />
          <InputCharacter
            setItems={setItems}
            sentence={sentence}
            setHasChangeSentence={setHasChangeSentence}
          />
        </div>
      </div>
      <div className={'w-full pr-6'}>
        <Textarea
          ref={textareaRef}
          value={content}
          defaultValue={content}
          onValueChange={handleChangeContent}
          tabIndex={0}
          onFocus={handleFocus}
          variant={'bordered'}
          minRows={1}
          classNames={{
            base: 'active:!bg-transparent active:!border-none active:!outline-none bg-transparent focus:!bg-transparent focus:!outline-none focus:!border-none focus-visible:!outline-none focus-visible:!border-none',
            mainWrapper:
              'active:!border-none focus:!bg-transparent focus:!outline-none focus:!border-none focus-within:!bg-transparent active:!bg-transparent focus-visible:!border-none focus-visible:!outline-none focus-visible:!shadow-none',
            inputWrapper:
              'p-0 !border-none !shadow-none active:!border-none !bg-transparent hover:!bg-transparent focus:bg-bg-box w-full min-h-6 focus:!bg-transparent focus-visible:!outline-none focus-visible:!border-none',
            innerWrapper:
              '!bg-transparent active:!border-none hover:!bg-transparent focus:bg-bg-box w-full focus:!bg-transparent focus:!outline-none focus-visible:!outline-none focus-visible:!border-none',
            input:
              'text-medium active:!bg-transparent active:!border-none active:!outline-none bg-transparent focus:!bg-transparent focus:!outline-none focus:!border-none !h-auto focus-visible:!outline-none focus-visible:!border-none leading-4',
          }}
          label=""
          placeholder={t('paragraph.edit.placeholder_input')}
        />
      </div>
    </div>
  );
};
export default SentenceItem;
