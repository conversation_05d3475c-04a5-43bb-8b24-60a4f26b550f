'use client';

import React, { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import Select from 'components/Select';
import Tabs from 'components/Tabs';
import EntRouters from 'configs/EntRouters';
import AppHeader from 'containers/layout/AppHeader';
import useLearnStore from 'store/learn';
import { TabProps } from 'types/tabs';

const Header: React.FunctionComponent = () => {
  const router = useRouter();
  const { documents, course, activeDocument, paragraphs, activeParagraph } = useLearnStore();
  const [selectParagraph, setSelectParagraph] = useState<Array<TabProps>>([]);
  const handleChangeDocument = (e) => {
    const _documentId = e.target.value ?? 0;
    router.push(`${EntRouters.learn_redirect}/${_documentId}`);
  };

  const handleChangeParagraph = (paragraphId) => {
    router.replace(`${EntRouters.paragraph_edit}/${paragraphId}`);
  };
  useEffect(() => {
    const _paragraphs: Array<TabProps> = [];
    if (paragraphs) {
      paragraphs.map((item, index) => {
        _paragraphs.push({
          id: item.keyx,
          title: index + 1,
          label: `${item.id} - ${item.title}`,
        });
      });
    }
    setSelectParagraph(_paragraphs);
  }, [paragraphs]);

  return (
    <AppHeader bottom={'0px'}>
      <div className={'h-[43px] pl-[30px] pr-[20px] w-full grid grid-cols-12 items-center gap-3'}>
        <div className={'col-span-8 flex items-center'}>
          {documents.length > 0 && (
            <Select
              label={course?.title || ''}
              sectionLabel={course?.title || ''}
              selectedKeys={[activeDocument?.id.toString() ?? '']}
              items={documents}
              onChange={handleChangeDocument}
              itemIcon={<i className={'w-4 h-4 mr-1 icon-document'} />}
              startContent={<i className={'w-4 h-4 mr-1 icon-document'} />}
              classNames={{
                base: 'h-[29px] w-72 mr-4',
                trigger: 'h-[29px] min-h-7 w-72',
              }}
            />
          )}
          <div className="h-[25px] rounded-[5px] steps bg-bg-box w-auto inline-flex items-center flex-row-reverse">
            {selectParagraph.length > 0 ? (
              <Tabs
                tabs={selectParagraph}
                classNames={{
                  tab: 'px-[9px] justify-center',
                }}
                activeTabId={activeParagraph?.keyx}
                onClick={handleChangeParagraph}
                showTooltip={true}
              />
            ) : null}
          </div>
        </div>
      </div>
    </AppHeader>
  );
};

export default Header;
