'use client';

import React from 'react';

import AutocompleteSearch from '@/containers/search/AutocompleteSearch';
import classNames from 'classnames';
import Button from 'components/Button';
import CreateCoursePopup from 'components/ServicePopup/CreateCoursePopup';
import AppHeader from 'containers/layout/AppHeader';
import { useTranslations } from 'next-intl';
import useHeaderStore from 'store/header';

const Header: React.FunctionComponent = () => {
  const [open, setOpen] = React.useState(false);
  const [showSearch, setShowSearch] = React.useState(false);
  const t = useTranslations();
  const { title } = useHeaderStore();
  return (
    <AppHeader>
      <div
        className={
          'px-[30px] w-full grid grid-cols-12 items-center flex content-between align-baseline'
        }
      >
        <div className={'col-span-6 flex items-center'}>
          <span className={classNames('block', { '!hidden': showSearch })}>{title}</span>
          <div
            className={classNames(' w-0 opacity-0', {
              'transition-all duration-200 ease-in-out !w-full !opacity-100': showSearch,
            })}
          >
            <AutocompleteSearch key={'autocomplete-course-root'} />
          </div>
        </div>

        <div className={'col-span-6 flex flex-grow-0 items-center justify-end'}>
          <div
            className={
              'border cursor-pointer border-color-border mr-3 align-baseline rounded-md inline-block p-1'
            }
            onClick={() => setShowSearch(!showSearch)}
          >
            <i className={'text-base icon-search'} />
          </div>
          <Button
            color={'default'}
            size={'sm'}
            onClick={() => setOpen(true)}
            startContent={<i className={'icon-add text-base text-color-minor'} />}
            className={'bg-bg-button border border-color-border'}
          >
            {t('course.add_course')}
          </Button>
        </div>
        <CreateCoursePopup open={open} onOpen={setOpen} />
      </div>
    </AppHeader>
  );
};

export default Header;
