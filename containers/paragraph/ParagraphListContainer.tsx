'use client';

import React, { useState } from 'react';

import Link from 'next/link';

import ParagraphListSkeleton from '@/containers/paragraph/skeleton/ParagraphListSkeleton';
import classNames from 'classnames';
import Button from 'components/Button';
import FavouriteButton from 'components/FavouriteButton';
import GemIcon from 'components/Icons/GemIcon';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import useFavouriteAll from 'hooks/Ent/useFavouriteAll';
import useParagraphs from 'hooks/Ent/useParagraphs';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';

import NoContent from '@/components/NoContent';

const TableHeader: React.FC = () => {
  return (
    <tr className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
      <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>Title</th>
      <th className={'px-2 py-1 text-left font-normal hidden'}>Phí</th>
      <th>&nbsp;</th>
    </tr>
  );
};

interface ParagraphRowProps {
  paragraph: any;
  favouriteAll: any[];
  isEdit: boolean;
  onEditClick: () => void;
}

const ParagraphRow: React.FC<ParagraphRowProps> = ({
  paragraph,
  favouriteAll,
  isEdit,
  onEditClick,
}) => {
  const t = useTranslations();

  return (
    <tr className="bg-bg-general hover:bg-bg-box border-b border-bg-box [&>td>div]:hidden [&>td>button]:hover:flex">
      <td className={'p-2 pl-[30px] h-[42px] flex items-center'}>
        <Link href={`${EntRouters.learn}/${paragraph.keyx}`}>{paragraph.title}</Link>
        <FavouriteButton
          favouriteList={favouriteAll}
          item={EntRouters.learn}
          object_id={paragraph.id}
        />
        <div
          onClick={onEditClick}
          className={
            'flex items-center justify-center w-[28px] h-[27px] rounded-md ml-2 hover:bg-bg-box cursor-pointer'
          }
        >
          <i
            className={classNames('w-[12px] h-[13px] icon-pencil-line fill-color-minor', {
              '!fill-yellow': isEdit,
            })}
          />
        </div>
      </td>
      <td className={'p-2 text-purple w-[100px] hidden'}>
        {paragraph.status === 2 ? (
          <span className={'bg-bg-box px-2 py-1 rounded-sm'}></span>
        ) : (
          <span className={'flex items-center'}>
            <GemIcon className={'fill-purple w-4 h-4 mr-1'} />
            {paragraph.id}
          </span>
        )}
      </td>
      <td className={'w-[120px]'}>
        {paragraph.status === 2 && (
          <Button color={'primary'} size={'xs'}>
            {t('course.learnNow')}
          </Button>
        )}
      </td>
    </tr>
  );
};

const EmptyState: React.FC = () => (
  <table className="table-auto w-full">
    <thead>
      <TableHeader />
    </thead>
    <tbody>
      <tr>
        <td colSpan={3}>
          <NoContent />
        </td>
      </tr>
    </tbody>
  </table>
);

const ParagraphListContainer: React.FC = () => {
  const { favouriteAll } = useFavouriteAll();
  const { paragraphList, isLoading, setPage, page, isReachingEnd } = useParagraphs({});
  const [isEdit, setEdit] = useState(false);

  const handleEditClick = () => setEdit(!isEdit);

  if (!isLoading && !paragraphList.length) {
    return <EmptyState />;
  }

  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        dataLength={paragraphList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <TableHeader />
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {paragraphList &&
              map(paragraphList, (paragraph, key) => (
                <ParagraphRow
                  key={`paragraph-${key}`}
                  paragraph={paragraph}
                  favouriteAll={favouriteAll}
                  isEdit={isEdit}
                  onEditClick={handleEditClick}
                />
              ))}
            {isLoading && <ParagraphListSkeleton />}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};

export default ParagraphListContainer;
