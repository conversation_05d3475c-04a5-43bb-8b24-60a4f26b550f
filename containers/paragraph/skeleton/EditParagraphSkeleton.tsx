import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const RoadmapSkeleton = () => {
  return (
    <>
      {map(range(1, 30), (index) => (
        <div
          className="border-b border-bg-box h-[43px] flex gap-3 items-center px-[30px]"
          key={index}
        >
          <div className="w-36 flex gap-2 items-center pl-2.5">
            <Skeleton className="rounded-full h-6 w-6" />
            <Skeleton className={`rounded-sm ${index % 2 ? 'w-14' : 'w-16'} h-3`} />
          </div>
          <Skeleton className={`rounded-sm ${index % 2 ? 'w-2/5' : 'w-3/5'} h-3`} />
        </div>
      ))}
    </>
  );
};
export default RoadmapSkeleton;
