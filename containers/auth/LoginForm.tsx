'use client';

import React, { useActionState, useTransition } from 'react';

import { useRouter } from 'next/navigation';

import { loginAction } from '@/actions/auth';
import CategoryEnum from '@/configs/CategoryEnum';
import { locales } from '@/configs/config.i18n';
import { setUserLocale } from '@/services/locale';
import { LoginSchema } from '@/utils/schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import classNames from 'classnames';
import { Session } from 'next-auth';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import { FormProvider as Form, useForm } from 'react-hook-form';
import * as z from 'zod';

import Button from '@/components/Button';
import Input from '@/components/Input';

import { useSession } from '@/hooks/useSession';
import { setCookie } from 'cookies-next';

const LoginForm = () => {
  const t = useTranslations();
  const router = useRouter();
  const { update } = useSession();
  const { setTheme } = useTheme();
  const [isPending, startTransition] = useTransition();
  const [state, formAction] = useActionState(loginAction, { error: null, success: false });
  const form = useForm<z.infer<typeof LoginSchema>>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      phone: '',
      password: '',
      remember: false,
    },
  });

  const onSubmit = form.handleSubmit((values) => {
    startTransition(() => {
      formAction(values);
    });
  });

  const setUserTheme = (sessionData: Session) => {
    if (sessionData?.member_categories && sessionData.member_categories.length > 0) {
      const themeCategory = sessionData.member_categories.find(
        (category) => category.category_id === CategoryEnum.THEME_LIST
      );
      if (themeCategory && typeof themeCategory.valuex === 'string') {
        setTheme(themeCategory.valuex);
      }
    }
  };

  const setupUserLocale = async (sessionData: Session) => {
    if (sessionData?.member_categories && sessionData.member_categories.length > 0) {
      const langCategory = sessionData.member_categories.find(
        (category) => category.category_id === CategoryEnum.LANG_LIST
      );
      if (
        langCategory &&
        typeof langCategory.valuex === 'string' &&
        locales.includes(langCategory.valuex as any)
      ) {
        await setUserLocale(langCategory.valuex as any);
      }
    }
  };
  React.useEffect(() => {
    if (state.success) {
      update().then((res) => {
        const sessionData = res as Session;
        //sessionData.member?.id
        setCookie('ent.member-part', 'ak', {
          maxAge: 24 * 60 * 60, // 1 ngày
          httpOnly: false,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
        });
        setUserTheme(sessionData);
        setupUserLocale(sessionData);
        localStorage.setItem('ent.app-name', 'langenter');
      });
      router.push('/');
    }
  }, [state, router]);

  return (
    <>
      {state.error && <div className={'text-red-500 mb-4'}>{state.error}</div>}
      <Form {...form}>
        <form onSubmit={onSubmit}>
          <div className="relative w-full mt-12 mb-10">
            <Input
              {...form.register('phone')}
              removeLabel={false}
              disabled={isPending}
              labelPlacement={'outside'}
              className={classNames(
                'mt-5 text-color-major autofill:shadow-none !rounded-none border-b border-b-bg-box focus:outline-none focus:ring w-full ease-linear transition-all duration-150 text-xl',
                { '!border-b-red-500': form.formState.errors.phone }
              )}
              type={'text'}
              label={t('auth.phone')}
              placeholder={'0987 654 321'}
            />
            {form.formState.errors.phone && (
              <p className="text-red-500 text-xs mt-1">{form.formState.errors.phone.message}</p>
            )}
          </div>
          <div className="relative w-full mt-[45px] mb-1">
            <Input
              removeLabel={false}
              {...form.register('password')}
              labelPlacement={'outside'}
              className={classNames(
                'mt-5 text-color-major autofill:shadow-none !rounded-none border-b border-b-bg-box focus:outline-none focus:ring w-full ease-linear transition-all duration-150 text-xl',
                { '!border-b-red-500': form.formState.errors.password }
              )}
              disabled={isPending}
              type={'password'}
              label={t('auth.password')}
              placeholder={'********'}
            />
            {form.formState.errors.password && (
              <p className="text-red-500 text-xs mt-1">{form.formState.errors.password.message}</p>
            )}
          </div>
          <div className={'flex justify-between items-center'}>
            <label className="items-center cursor-pointer hidden">
              <input
                {...form.register('remember')}
                type="checkbox"
                disabled={isPending}
                className="form-checkbox border-0 rounded ml-1 w-4 h-4 ease-linear transition-all duration-150"
              />
              <span className="ml-2 text-xs">Remember me</span>
            </label>
            <div className="text-right text-purple">
              <a href="#" onClick={(e) => e.preventDefault()} className="text-blueGray-200">
                <small className="!text-[12px]">{t('auth.forgotPass')}</small>
              </a>
            </div>
          </div>

          <div className="text-center mt-8">
            <Button type={'submit'} color={'primary'} size={'lg'} disabled={isPending}>
              {isPending ? (
                <i className={'animate-spin text-white text-sm w-3 h-3 icon-spin2'} />
              ) : null}{' '}
              {t('auth.btnLogin')}
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
};

export default LoginForm;
