'use client';

import React from 'react';

import useFavouriteAll from 'hooks/Ent/useFavouriteAll';
import useParagraphConversations from 'hooks/Ent/useParagraphConversations';

import ConversationEmptyState from './components/ConversationEmptyState';
import ConversationTable from './components/ConversationTable';

interface ConversationListProps {
  course_id: number;
  type: string;
}

const ConversationList = ({ course_id, type }: ConversationListProps) => {
  const { favouriteAll } = useFavouriteAll();
  const { paragraphList, isLoading, page, setPage, isReachingEnd } = useParagraphConversations({
    course_id: course_id,
    item: type,
    includes: 'favourite',
    limit: 20,
  });

  if (!isLoading && !paragraphList.length) {
    return <ConversationEmptyState />;
  }

  return (
    <ConversationTable
      paragraphList={paragraphList}
      isLoading={isLoading}
      page={page}
      setPage={setPage}
      isReachingEnd={isReachingEnd}
      type={type}
      favouriteAll={favouriteAll}
    />
  );
};

export default ConversationList;
