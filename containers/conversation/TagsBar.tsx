'use client';

import React, { memo, useEffect, useState } from 'react';

import { useParams, useRouter } from 'next/navigation';

import useCategoryStore from '@/store/category';
import Tabs from 'components/Tabs';
import { ITEM_CONVERSATION, ITEM_ESSAY } from 'configs';
import { CategoryEntity } from 'types/model';

const TagsBar = ({ categoryParentId }: { categoryParentId: string }) => {
  const router = useRouter();
  const [activeHistoryTag, setActiveTag] = useState('all');

  const { categories: initialCategories } = useCategoryStore();

  const categories = initialCategories
    .filter((item) => item.parent_id === Number(categoryParentId))
    .map((category) => ({
      ...category,
      label: category.title_vi, // Gán title_vi làm label
    }));
  if (categories.length > 0) {
    categories.unshift({
      id: -1,
      title: 'All',
      title_vi: 'Tất cả',
      label: 'T<PERSON>t cả', // Bổ sung label
      icon: '',
      keyx: 'all',
      valuex: 'all',
      parent_id: 0,
      status: 2,
      is_favourite: 1,
      items: Array<CategoryEntity>(),
    });
  }
  const params = useParams();
  const tag_id = params.id?.toString() || '';
  const curCategorySelect = categories.findIndex((category) => category.valuex === tag_id) ?? -1;
  useEffect(() => {
    if (parseInt(tag_id) > 0 && curCategorySelect !== -1) {
      setActiveTag(categories[curCategorySelect].keyx);
    }
  }, [curCategorySelect]); // Chỉ gọi useEffect khi curCategorySelect thay đổi
  const handleClick = (activeHistoryTag: string) => {
    setActiveTag(activeHistoryTag);
    const curCategory = categories.find((category) => category.keyx === activeHistoryTag) ?? null;
    let path = ITEM_CONVERSATION;
    if (categoryParentId === '211') {
      path = ITEM_ESSAY;
    }
    if (activeHistoryTag === 'all') {
      router.push('/' + path);
    } else {
      router.push('/' + path + '/' + curCategory?.valuex);
    }
  };
  return (
    <>
      {categories.length > 0 && (
        <Tabs
          tabs={categories}
          className={'ml-[30px] mb-[10px]'}
          value={'keyx'}
          activeTabId={activeHistoryTag}
          onClick={(activeHistoryTag) => handleClick(activeHistoryTag)}
          showTooltip={true} // Bật tooltip
        />
      )}
    </>
  );
};
export default memo(TagsBar);
