import React from 'react';

import ScrollArea from 'components/ScrollArea';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';
import { FavouriteEntity, ParagraphEntity } from 'types/model';

import ConversationSkeleton from '../skeleton/ConversationSkeleton';
import ConversationTableHeader from './ConversationTableHeader';
import ConversationTableRow from './ConversationTableRow';

interface ConversationTableProps {
  paragraphList: ParagraphEntity[];
  isLoading: boolean;
  page: number;
  setPage: (page: number) => void;
  isReachingEnd: boolean;
  type: string;
  favouriteAll: Pick<FavouriteEntity, 'item' | 'object_id'>[];
}

const ConversationTable = ({
  paragraphList,
  isLoading,
  page,
  setPage,
  isReachingEnd,
  type,
  favouriteAll,
}: ConversationTableProps) => {
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 53px)'}
        dataLength={paragraphList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <ConversationTableHeader />
          <tbody className={'text-[0.8123rem]'}>
            {paragraphList
              ? map(paragraphList, (paragraph, key) => (
                  <ConversationTableRow
                    key={`conversation-row-${key}`}
                    paragraph={paragraph}
                    type={type}
                    favouriteAll={favouriteAll}
                  />
                ))
              : null}
            {isLoading ? <ConversationSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};

export default ConversationTable;
