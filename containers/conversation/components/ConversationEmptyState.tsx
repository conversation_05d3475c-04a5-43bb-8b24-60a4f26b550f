import React from 'react';

import { useTranslations } from 'next-intl';

import NoContent from '@/components/NoContent';

const ConversationEmptyState = () => {
  const t = useTranslations();

  return (
    <table className="table-auto w-full">
      <thead>
        <tr className={'bg-bg-box text-color-minor text-[13px] leading-[8px] hidden'}>
          <th className={'pl-[30px] text-left font-normal'}>&nbsp;</th>
          <th>&nbsp;</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colSpan={2}>
            <NoContent title={t('course.no_document')} />
          </td>
        </tr>
      </tbody>
    </table>
  );
};

export default ConversationEmptyState;
