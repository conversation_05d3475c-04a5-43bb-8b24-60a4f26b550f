'use client';

import React from 'react';

import Link from 'next/link';
import { useParams } from 'next/navigation';

import ConversationSkeleton from '@/containers/conversation/skeleton/ConversationSkeleton';
import { Button } from 'components';
import FavouriteButton from 'components/FavouriteButton';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import useFavouriteAll from 'hooks/Ent/useFavouriteAll';
import useTagDetails from 'hooks/Ent/useTagDetails';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';

import NoContent from '@/components/NoContent';

const ConversationTagsList = () => {
  const { favouriteAll } = useFavouriteAll();
  const t = useTranslations();
  const params = useParams();
  const tag_id = params.id?.toString() || '';
  const { tagDetailList, isLoading, page, setPage, isReachingEnd } = useTagDetails({
    item: 'paragraph',
    tag_id: parseInt(tag_id) || 0,
  });
  // const [isFavorite, setFavorite] = useState(false);
  // const handleClickFavorite = () => {
  //   setFavorite(!isFavorite);
  // };
  if (!isLoading && !tagDetailList.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={'bg-bg-box text-color-minor text-[13px] leading-[8px] hidden'}>
            <th className={'pl-[30px] text-left font-normal'}>&nbsp;</th>
            <th>&nbsp;</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={2}>
              <NoContent title={t('course.no_document')} />
            </td>
          </tr>
        </tbody>
      </table>
    );
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        dataLength={tagDetailList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead>
            <tr className={'bg-bg-box text-color-minor text-[13px] leading-[8px] hidden'}>
              <th className={'pl-[30px] text-left font-normal'}>&nbsp;</th>
              <th>&nbsp;</th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {tagDetailList
              ? map(tagDetailList, (tagDetail, key) => (
                  <tr
                    key={`tr-${key}`}
                    className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group [&>td>button]:hidden [&>td>button]:hover:flex"
                  >
                    <td key={`td-1-${key}`} className={'p-2 pl-[30px] h-[42px] flex items-center'}>
                      <i className={'icon-conversation text-[16px] mr-2'} />
                      <Link
                        key={`a-${key}`}
                        href={`${EntRouters.learn}/${tagDetail.paragraph?.keyx}`}
                      >
                        {tagDetail.paragraph?.title}
                      </Link>
                      {/* <div onClick={handleClickFavorite}
                      key={`div-${key}`}
                      className={'flex items-center justify-center w-[28px] h-[27px] rounded-md ml-2 hover:bg-bg-box cursor-pointer'}>
                    <StarIcon
                        key={`icon-star-${key}`}
                        className={classNames('w-[15px] h-[16px] fill-color-minor', { '!fill-yellow': isFavorite })} />
                  </div> */}
                      <FavouriteButton
                        favouriteList={favouriteAll}
                        item={EntRouters.learn}
                        object_id={tagDetail.paragraph?.id || 0}
                      />
                    </td>
                    <td key={`td-2-${key}`} className={'w-[120px] pr-3'}>
                      {tagDetail.status === 2 ? (
                        <Button
                          as={'a'}
                          href={`${EntRouters.learn}/${tagDetail.paragraph?.keyx}`}
                          color={'primary'}
                          size={'xs'}
                          className={'group-hover:!flex hidden'}
                        >
                          {t('course.learnNow')}
                        </Button>
                      ) : null}
                    </td>
                  </tr>
                ))
              : null}
            {isLoading ? <ConversationSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default ConversationTagsList;
