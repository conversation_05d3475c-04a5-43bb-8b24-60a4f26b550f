'use client';

import React from 'react';

import AppHeader from 'containers/layout/AppHeader';
import { useTranslations } from 'next-intl';

const Header = ({ page }) => {
  const t = useTranslations();
  return (
    <AppHeader>
      <div className={'h-[43px] px-[30px] w-full items-center flex content-between align-baseline'}>
        <div className={'col-span-6 flex items-center'}>
          <span className={'flex items-center gap-x-2'}>
            <i className={`icon-${page} text-[16px]`} />
            {t(`${page}.title`)}
          </span>
        </div>
      </div>
    </AppHeader>
  );
};

export default Header;
