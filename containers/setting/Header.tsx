'use client';

import React from 'react';

import Button from 'components/Button';
import AppHeader from 'containers/layout/AppHeader';

const Header: React.FunctionComponent = () => {
  return (
    <AppHeader>
      <div className={'w-full grid grid-cols-12'}>
        <div className={'col-span-4'}>Khóa học</div>
        <div className={'col-span-8 flex flex-grow-0 items-center justify-end'}>
          <div
            className={'border border-color-border mr-3 align-baseline rounded-md inline-block p-1'}
          >
            <i className={'text-large text-color-minor icon-medal'} />
          </div>
          <Button
            startContent={<i className={'w-4 h-4 icon-package text-color-major'} />}
            className={'bg-bg-button border border-color-border'}
          >
            {'Thêm khóa học'}
          </Button>
        </div>
      </div>
    </AppHeader>
  );
};

export default Header;
