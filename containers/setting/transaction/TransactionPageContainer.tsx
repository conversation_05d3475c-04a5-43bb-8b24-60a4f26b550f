'use client';

import React, { useEffect, useMemo, useState } from 'react';

import { Button } from '@/components';
import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import TokenReport from '@/containers/setting/transaction/TokenReport';
import useReportStore from '@/store/report';
import { ButtonGroup, Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from '@heroui/react';
import {
  endOfMonth,
  fromUnixTime,
  getUnixTime,
  setHours,
  setMinutes,
  setSeconds,
  startOfMonth,
} from 'date-fns';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';

import MonthYearSelector from '@/components/MonthYearSelector';

import useReportMemberToken from '@/hooks/Ent/useReportMemberToken';
import { useSession } from '@/hooks/useSession';

type Member = {
  id: number;
  fullname: string;
};
const TransactionPageContainer = () => {
  const { date, setDate, params, setParams, loading } = useReportStore();
  // const [isInitialized, setIsInitialized] = useState<boolean>(false); // Theo dõi lần load đầu tiên

  const getMonthRangeFromTimestamp = (timestamp: number) => {
    const newDate = fromUnixTime(timestamp);
    const startTimestamp = getUnixTime(startOfMonth(newDate));
    const lastMomentOfEndDay = setSeconds(setMinutes(setHours(endOfMonth(newDate), 23), 59), 59);
    const endTimestamp = getUnixTime(lastMomentOfEndDay);
    return { startTimestamp, endTimestamp };
  };

  // Tự động tính toán timestamp mỗi khi date thay đổi
  const { startTimestamp, endTimestamp } = useMemo(() => {
    const timestamp = getUnixTime(date);
    return getMonthRangeFromTimestamp(timestamp);
  }, [date]);

  // Gọi API mỗi khi `date` hoặc `params.object_id` thay đổi
  const { reportsData, isLoading } = useReportMemberToken(
    {
      item: params.item,
      start_at: startTimestamp,
      end_at: endTimestamp,
      member_id: params.object_id,
    },
    loading // Không dùng `isInitialized`, luôn gọi API khi `date` hoặc `params.object_id` thay đổi
  );
  const t = useTranslations();
  const { data: session } = useSession();
  const [member, setMember] = useState<Member | null>(null);
  const [members, setMembers] = useState<Member[]>();

  useEffect(() => {
    const _member = [{ id: 0, fullname: t('settings.transactions.all_member') }];
    map(session?.members, (member) => _member.push(member));
    setMembers(_member);
  }, [session]);
  const handleSelectMember = (member: Member) => {
    setMember(member);
    if (member) {
      setParams({ ...params, item: ReportTypeEnum.MEMBER, object_id: member.id });
    } else {
      setParams({ ...params, item: ReportTypeEnum.MEMBER, object_id: 0 });
    }
  };
  return (
    <div className={'py-3 pl-[30px]'}>
      <div className={'mt-2.5'}>
        <MonthYearSelector setDate={setDate} date={date} />
        {session.members && (
          <ButtonGroup className={'h-6 border border-color-border shadow-medium rounded-sm ml-3'}>
            <Dropdown placement="bottom-end">
              <DropdownTrigger className={'rounded-none shadow-none min-w-min px-2 h-[22px]'}>
                <Button color={'default'} className={'shadow-none rounded-r-none h-[22px]'}>
                  {member?.fullname || t('settings.transactions.all_member')}
                  <i className={'text-medium icon-arrow-down'} />
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                disallowEmptySelection
                aria-label="Class options"
                selectedKeys={[member?.id || 0]}
                selectionMode="single"
                className="max-w-[300px] rounded-none bg-bg-general"
              >
                {map(members, (member) => (
                  <DropdownItem key={member.id} onPress={() => handleSelectMember(member)}>
                    {member.fullname}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </Dropdown>
          </ButtonGroup>
        )}
      </div>
      <div className="mt-5 mb-10">
        <div>
          <TokenReport
            title={t('settings.transactions.use_by_month')}
            // @ts-ignore
            reportsData={reportsData?.total_report}
            isLoading={isLoading}
            height="h-[350px]"
          />
        </div>
        <div className={'grid grid-cols-2 gap-6 mt-[50px]'}>
          <div>
            <TokenReport
              title={t('settings.transactions.listen_by_month')}
              // @ts-ignore
              reportsData={reportsData?.listen_report}
              isLoading={isLoading}
              height="h-[250px]"
            />
          </div>
          <div>
            <TokenReport
              title={t('settings.transactions.read_by_month')}
              // @ts-ignore
              reportsData={reportsData?.speak_report}
              isLoading={isLoading}
              height="h-[250px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransactionPageContainer;
