'use client';

import React, { useMemo } from 'react';

import ChartBarSkeleton from '@/containers/class/skeleton/ChartBarSkeleton';
import { ChartItem } from '@/types/component';
import { MemberReportsEntity, ReportsEntity } from '@/types/model';
import classNames from 'classnames';
import { map, reduce } from 'lodash';

import Charts from '@/components/Charts/Charts';

import useChartConfig from '@/hooks/Ent/useChartConfig';

type TokenReportProps = {
  title: string;
  reportsData: MemberReportsEntity[];
  isLoading: boolean;
  height?: string;
};
const TokenReport = ({ title, reportsData, isLoading, height }: TokenReportProps) => {
  const totalAmount = reportsData?.reduce((sum, report: any) => sum + report.amount, 0);
  const { makeBarDate } = useChartConfig();
  // console.log('reportsDatat', reportsData);
  const [charts, columns] = useMemo(() => {
    const colors = ['BLUE', 'GREEN'];
    // @ts-ignore

    const charts: ChartItem[] = makeBarDate(reportsData, colors);
    // console.log('charts', charts);
    //tổng hợp theo ngày
    const groupedByDay = reduce(
      reportsData,
      (acc, item) => {
        // @ts-ignore
        if (!acc[item.day]) {
          // @ts-ignore
          acc[item.day] = [];
        }
        // @ts-ignore
        acc[item.day].push(item);
        return acc;
      },
      {}
    );
    // console.log(groupedByDay);
    map(groupedByDay, (items: ReportsEntity[], key) => {
      //tổng hợp điểm theo ngày
      const groupedPoint = reduce(
        items,
        (acc, item) => {
          const transactionType = item.transaction_type ?? 1; // Thay undefined bằng 1
          if (!acc[transactionType]) {
            acc[transactionType] = 0;
          }
          acc[transactionType] += item.amount;
          return acc;
        },
        {}
      );

      const index = charts.findIndex((item) => item.date === parseInt(key));
      if (index >= 0) {
        charts[index - 1] = {
          day: key,
          date: parseInt(key),
          BLUE: groupedPoint[1],
          GREEN: groupedPoint[2],
          isActive: true,
        };
      }
    });
    return [charts, colors];
  }, [reportsData]);

  type CustomTooltipProps = {
    active?: boolean;
    payload: [];
    label?: string;
  };
  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    // @ts-ignore
    if (!active || !payload || payload.length === 0 || !payload.some((p) => p.value > 0)) {
      return null;
    }
    // @ts-ignore
    const total = payload.reduce((current, item) => item.value + current, 0);

    const genTransactionName = (id) => {
      const ids = {
        BLUE: 'IN',
        GREEN: 'OUT',
      };
      return ids[id] ?? '#';
    };

    return (
      <div className="bg-bg-box p-3 rounded-md shadow-md">
        <p className="font-bold">
          Ngày {label}: {total} Kim cương{' '}
        </p>
        {/*@ts-ignore*/}
        {payload.reverse().map((entry, index) => (
          <p
            key={index}
            className="text-xs py-1 flex items-center gap-2"
            // @ts-ignore
            style={{ color: entry.color }}
          >
            {/*@ts-ignore*/}
            <i className={'w-3 h-3 inline-block'} style={{ backgroundColor: entry.color }}></i>
            {/*@ts-ignore*/}
            {genTransactionName(entry.name)}: {entry.value} Kim cương
          </p>
        ))}
      </div>
    );
  };

  return (
    <>
      {/* <div className={'col-span-12 sm:col-span-4 md:col-span-3 2xl:col-span-2'}>
        <div className={'grid grid-cols-3'}>
          <div className={'col-span-3 flex'}>
            <div className="mr-[30px] px-[6px]">
              <i className="text-[32px] icon-diamond text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>{t('group.dashboard.balance')}</div>
              <span className={'text-[22px] font-medium'}>10000</span>
              <Link href={'#'} className={'text-small block text-purple'}>
                {t('group.dashboard.topup_history')}
              </Link>
              <Button className={'h-6 mt-2 px-3 border border-color-border'}>
                {t('group.dashboard.topup')}
              </Button>
            </div>
          </div>
        </div>
      </div> */}

      <div className={'col-span-12 sm:col-span-8 md:col-span-9 2xl:col-span-10'}>
        <div className={'w-full flex justify-between items-center'}>
          <span className={'text-small font-bold text-color-major text-[15px]'}>{title}</span>
          <div className={'flex'}>
            <span>{totalAmount ?? 0}</span>
            <i className="text-base ml-2 icon-diamond text-yellow-100" />
          </div>
        </div>
        <div className={'mt-2'}>
          <div className={classNames('w-full relative', height)}>
            {isLoading ? (
              <ChartBarSkeleton />
            ) : (
              /*@ts-ignore*/
              <Charts charts={charts} columns={columns} tooltip={CustomTooltip} label={''} />
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default TokenReport;
