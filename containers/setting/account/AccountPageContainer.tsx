'use client';

import React, { useEffect, useState } from 'react';

import { Input } from '@/components';
import { verifyPassword } from '@/helpers';
import { UseMemberAddProps } from '@/types/hooks';
import { Avatar } from '@heroui/react';
import { useTranslations } from 'next-intl';
import { SubmitHandler, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

import Button from '@/components/Button';
import InputPassword from '@/components/InputPassword';
import ScrollArea from '@/components/ScrollArea';

import useAccount from '@/hooks/Ent/useAccount';
import useAuth from '@/hooks/Ent/useAuth';
import { useSession } from '@/hooks/useSession';

const AccountPageContainer = () => {
  const t = useTranslations();
  const { data: sessionData, update } = useSession();
  const [fullname, setFullname] = useState('');
  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
  } = useForm<UseMemberAddProps>();
  const [avatarImage, setAvatarImage] = useState<string | ArrayBuffer | null>(null); // State để lưu ảnh đại diện
  const [submitType, setSubmitType] = useState('');
  const { setMessage, errorMessage } = useAuth();
  const { updateAccount } = useAccount();

  useEffect(() => {
    const account = sessionData?.member;
    setValue('id', account?.id);
    if (sessionData?.member?.fullname) {
      setFullname(sessionData.member.fullname);
      setValue('fullname', sessionData.member.fullname);
    }
  }, [sessionData]);

  const onSubmit: SubmitHandler<UseMemberAddProps> = async (data) => {
    try {
      if (submitType === 'changePass') {
        setMessage('');
        setValue('avatar', new File([''], 'avatar'));
        if (data.password === '') {
          setMessage(t('message.error.password.empty'));
          return;
        }
        if (data.password.length < 8) {
          setMessage(t('message.error.password.atleast'));
          return;
        }
        const isValid = verifyPassword(data.password);
        if (!isValid) {
          setMessage(t('message.error.password.invalid'));
          return;
        }
        if (data.password !== data.rePassword) {
          setMessage(t('message.error.password.notMatch'));
          return;
        }
        setValue('password', data.password);
      } else if (submitType === 'updateInfo') {
        setValue('password', '');
        if (data.fullname === '') {
          setMessage(t('message.error.fullname'));
          return;
        }
      }
      const res = await updateAccount(data);
      if (res.success) {
        await update({
          ...sessionData,
          member: {
            ...sessionData?.member,
            fullname: data.fullname,
            avatar: res.data?.avatar,
          },
        });
        toast.success(t('message.success.common'));
      } else {
        if (res.message === 'not_found_password') {
          toast.error(t('message.error.password.invalid'));
        } else if (res.message === 'can_not_update') {
          toast.error(t('message.error.unknown'));
        } else {
          setMessage(res.message);
        }
      }
    } catch {
      toast.error(t('message.error.exit'));
    }
  };

  const handleAvatarClick = () => {
    const inputElement = document.querySelector('input[name="avatar"]');
    if (inputElement) {
      // @ts-ignore
      inputElement.click();
    }
  };
  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]; // Lấy tệp hình ảnh từ sự kiện onChange
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarImage(reader.result); // Cập nhật ảnh đại diện với dữ liệu của tệp hình ảnh
      };
      reader.readAsDataURL(file); // Đọc tệp hình ảnh
    }
  };

  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <div className={'py-3 p-[30px]'}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mt-[20px] mb-[30px]">
            <div className={'pb-3 text-[14px]'}>{t('settings.avatar')}</div>
            <Avatar
              onClick={handleAvatarClick}
              src={(avatarImage && avatarImage.toString()) || ''}
              fallback={
                <span className="whitespace-nowrap">
                  <i className={'icon-upload text-[24px]'}></i>
                  {t('settings.upload')}
                </span>
              }
              showFallback
              className={
                'w-[128px] h-[128px] bg-bg-box border-[1px] border-color-border text-color-major  cursor-pointer'
              }
            />
            <Input
              {...register('avatar')}
              name={'avatar'}
              type={'file'}
              className={'hidden'}
              onChange={handleAvatarChange}
            />
          </div>
          <div className="mb-[30px]">
            <div className={'pb-2 text-[14px]'}>{t('settings.fullname')}</div>
            <Input
              {...register('fullname', { required: submitType === 'updateInfo' })}
              inputMode={'text'}
              value={fullname}
              onChange={(e) => {
                setFullname(e.target.value);
                setValue('fullname', e.target.value);
              }}
              type={'text'}
              className={'w-3/6 border-0 bg-transparent'}
              classNames={{
                inputWrapper: ' bg-bg-box',
                input: 'text-normal',
              }}
              aria-invalid={errors.fullname ? 'true' : 'false'}
            />
            {errors.fullname?.type === 'required' && (
              <div className={'mt-3 mb-4 text-red'}>{t('message.error.fullname')}</div>
            )}
          </div>
          <div className={'border-b border-color-border  mb-[40px]'}>
            <Button
              onClick={() => setSubmitType('updateInfo')}
              type={'submit'}
              className={'!w-auto  mb-[40px]'}
              color={'primary'}
              size={'lg'}
            >
              {t('settings.member.edit.button')}
            </Button>
          </div>
          <div className=" mb-[40px]">
            <div className="mb-[20px]">
              <h6 className={'text-xl'}>{t('settings.member.change_pin')}</h6>
            </div>
            <div className="flex w-full flex-col ">
              <div className={'pb-2 text-[14px]'}>{t('settings.member.password')}</div>
              <InputPassword
                {...register('password', { required: submitType === 'changePass' })}
                inputMode={'text'}
                className={'w-3/6 border-0 bg-transparent'}
                classNames={{
                  inputWrapper: ' bg-bg-box',
                  input: 'text-normal',
                }}
                aria-invalid={errors.password ? 'true' : 'false'}
              />
              {errors.password?.type === 'required' ? (
                <div className="mt-3 mb-4 text-red">{t('message.error.password.empty')}</div>
              ) : errorMessage !== '' ? (
                <div className="mt-3 text-red">{errorMessage}</div>
              ) : null}
            </div>

            <div className="flex w-full flex-col mt-4">
              <div className={'pb-2 text-[14px]'}>{t('settings.member.rePassword')}</div>
              <InputPassword
                {...register('rePassword', { required: submitType === 'changePass' })}
                inputMode={'text'}
                className={'w-3/6 border-0 bg-transparent'}
                classNames={{
                  inputWrapper: ' bg-bg-box',
                  input: 'text-normal',
                }}
                aria-invalid={errors.password ? 'true' : 'false'}
              />
              {errors.rePassword?.type === 'required' ? (
                <div className="mt-3 mb-4 text-red">{t('message.error.password.empty')}</div>
              ) : errorMessage !== '' ? (
                <div className="mt-3 text-red">{errorMessage}</div>
              ) : null}
            </div>
          </div>
          <div className={' mb-[90px]'}>
            <Button
              onClick={() => setSubmitType('changePass')}
              type={'submit'}
              className={'!w-auto  mb-[40px]'}
              color={'primary'}
              size={'lg'}
            >
              {t('settings.member.edit.button')}
            </Button>
          </div>
        </form>
      </div>
    </ScrollArea>
  );
};
export default AccountPageContainer;
