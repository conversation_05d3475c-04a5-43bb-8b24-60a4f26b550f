'use client';

import React, { useEffect, useState } from 'react';

import { useAppContext } from '@/store/contexts/AppContext';
import Select from 'components/Select';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import { OptionItem } from 'types/component';
import { AppContextProps, ThemeItem } from 'types/theme';

const SettingPage = () => {
  const t = useTranslations();
  const selectLists: Array<OptionItem> = [
    { id: 1, title: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 2, title: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 3, title: '<PERSON><PERSON><PERSON><PERSON>' },
  ];

  const [selectHabit, setSelectHabit] = useState(selectLists[0].id);
  const [selectNextMethod, setNextMethod] = useState(selectLists[0].id);
  const [selectedTheme, setSelectTheme] = useState<ThemeItem>();
  const { listTheme }: AppContextProps = useAppContext();
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    const currentTheme = listTheme?.find((themeItem) => themeItem.keyx === theme);
    if (currentTheme) {
      setSelectTheme(currentTheme);
    }
  }, [listTheme]);
  useEffect(() => {
    if (selectedTheme) {
      setTheme(selectedTheme.keyx);
    }
  }, [selectedTheme]);

  return (
    <div className={' py-3 pl-[30px]'}>
      <div className={'mt-4'}>
        <h6 className={'text-sm'}>{t('settings.usePointer')}</h6>
        <h6 className={'text-sm text-color-minor mt-1'}>{t('settings.descriptionPointer')} </h6>
      </div>
      <hr className="my-4 md:min-w-full" />
      <div className={'flex justify-between items-center'}>
        <div>
          <h6 className={'text-sm'}>{t('settings.theme')}</h6>
          <h6 className={'text-sm text-color-minor mt-1'}>{t('settings.descriptionTheme')} </h6>
        </div>
        {/*// @ts-ignore*/}
        {/*<Select selected={selectedTheme} options={listThemes} onChange={setSelectTheme} className={'min-w-[12rem]'} />*/}
      </div>
      <hr className="my-4 md:min-w-full" />
      <div className={''}>
        <h6 className={'text-xl mb-1'}>{t('settings.habit')}</h6>
        <div className={'flex justify-between items-center'}>
          <div>
            <h6 className={'text-sm'}>{t('settings.showHomeDefault')}</h6>
            <h6 className={'text-sm text-color-minor mt-1'}>
              {t('settings.descriptionShowHomeDefault')}{' '}
            </h6>
          </div>
          <Select
            label={'Habit'}
            size={'sm'}
            selectedKeys={[selectHabit?.toString() ?? '']}
            items={selectLists}
            onChange={(event) => setSelectHabit(event.target.value)}
            classNames={{
              base: 'w-32 !py-[5px]',
              trigger: 'h-[29px] bg-bg-general hover:bg-bg-general',
            }}
          />
        </div>
      </div>
      <hr className="my-4 md:min-w-full" />
      <div className={'flex justify-between items-center'}>
        <div>
          <h6 className={'text-sm'}>{t('settings.showHomeDefault')}</h6>
          <h6 className={'text-sm text-color-minor mt-1'}>
            {t('settings.descriptionShowHomeDefault')}{' '}
          </h6>
        </div>
        <Select
          label={'Language'}
          size={'sm'}
          selectedKeys={[selectNextMethod?.toString() ?? '']}
          items={selectLists}
          onChange={(event) => setNextMethod(event.target.value)}
          classNames={{
            base: 'w-32 !py-[5px]',
            trigger: 'h-[29px] bg-bg-general hover:bg-bg-general',
          }}
        />
      </div>
      <hr className="my-4 md:min-w-full" />
      <div className="mb-5"></div>
    </div>
  );
};

export default SettingPage;
