import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const MemberDetailSkeleton = () => {
  return (
    <>
      <tr className="border-b border-bg-box items-center">
        <td colSpan={2} className={'pl-[30px] h-28 flex flex-col justify-center pr-[5px]'}>
          <Skeleton className="rounded-sm h-5 w-3/5" />
          <div className={'w-full flex items-center justify-between mt-4'}>
            <Skeleton className="rounded-sm w-2/5 h-3" />
            <Skeleton className="rounded-sm w-10 h-3" />
          </div>
        </td>
      </tr>
      {map(range(1, 10), (index) => (
        <tr className="border-b border-bg-box h-[43px] items-center" key={index}>
          <td className={'pl-[30px] min-h-[43px] flex items-center gap-x-3 py-[10px] pr-[5px]'}>
            <Skeleton className="rounded-sm h-3 w-3" />
            <Skeleton className="rounded-sm w-2/5 h-3" />
          </td>
          <td>
            <Skeleton className="rounded-sm w-3/5 h-3" />
          </td>
        </tr>
      ))}
    </>
  );
};
export default MemberDetailSkeleton;
