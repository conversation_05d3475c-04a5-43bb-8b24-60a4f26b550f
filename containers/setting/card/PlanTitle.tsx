'use client';

import React from 'react';

import usePlans from 'hooks/Ent/usePlans';
import { map } from 'lodash';

import PlanHeader from './PlanHeader';

const PlanTitle = () => {
  const { plansTitle } = usePlans();
  if (!plansTitle) return <></>;
  return (
    <div className="flex flex-col p-6 mx-auto max-w-lg text-center ">
      <PlanHeader />
      {map(plansTitle, (item: any) => (
        <>
          <h6 className={'text-xl text-left mb-3'}>{item.title}</h6>
          <ul role="list" className="mb-8 space-y-2 text-left">
            {map(item.features, (feature) => (
              <li className="flex items-center space-x-3 border-b border-color-line mb-1 pb-2">
                <span className={'text-sm text-color-minor'}>{feature.value}</span>
              </li>
            ))}
          </ul>
        </>
      ))}
    </div>
  );
};
export default PlanTitle;
