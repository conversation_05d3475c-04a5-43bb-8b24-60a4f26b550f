'use client';

import React from 'react';

import PlanItem from 'containers/setting/card/PlanItem';
import usePlans from 'hooks/Ent/usePlans';
import { map } from 'lodash';

import PlanTitle from './PlanTitle';

const PricingPlans = () => {
  const { plans, selectedPlan, setPlan } = usePlans();
  if (!plans) return <></>;
  return (
    <section className="bg-bg-general">
      <div className="py-4 px-4 mx-auto max-w-screen-xl ">
        <div className="mx-auto max-w-screen-md text-center mb-8 lg:mb-12">
          <h2 className="mb-4 text-4xl tracking-tight text-lg">Chonj plan</h2>
        </div>
        <div className="space-y-8 lg:grid lg:grid-cols-4 sm:gap-6 xl:gap-10 lg:space-y-0 text-sm">
          <PlanTitle />
          {map(plans, (plan: any) => (
            <PlanItem selectedPlan={selectedPlan} features={plan} onClick={setPlan} />
          ))}
        </div>
      </div>
    </section>
  );
};
export default PricingPlans;
