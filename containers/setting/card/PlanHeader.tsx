'use client';

import React from 'react';

import Button from 'components/Button';

interface PlanHeaderProps {
  id?: number;
  title?: string | null;
  price?: string | null;
  description?: string | null;
}
const PlanHeader = ({ id, title, price, description }: PlanHeaderProps) => {
  return (
    <div className={'text-left'}>
      <h4 className={'text-md pb-2'}>{title ?? <>&nbsp;</>}</h4>
      <h6 className={'text-xl'}>{price ?? <>&nbsp;</>}</h6>
      <h6 className={'text-sm text-color-minor'}>
        {description && description != '' ? description : <span>&nbsp;</span>}
      </h6>

      {id && id > 0 ? (
        <Button className={'mt-3'}>{'Nâng cấp'}</Button>
      ) : (
        <span className={'h-8 p-3 block mt-3'}>&nbsp;</span>
      )}
      <hr className="my-4 md:min-w-full" />
    </div>
  );
};
export default PlanHeader;
