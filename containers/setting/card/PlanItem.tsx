'use client';

import className from 'classnames';
import usePlans from 'hooks/Ent/usePlans';
import { map } from 'lodash';

import PlanHeader from './PlanHeader';

interface PlanItemProps {
  features: any;
  selectedPlan: any;
  onClick: (plan: any) => void;
}

const PlanItem = ({ features, selectedPlan, onClick }: PlanItemProps) => {
  if (!features) return <></>;
  const { plansTitle } = usePlans();
  const handleSelectPlan = () => {
    onClick(features);
  };
  return (
    <div
      onClick={handleSelectPlan}
      className={className(
        'flex flex-col p-6 mx-auto w-full text-center cursor-pointer border border-bg-general',
        { 'border-color-border bg-bg-box': selectedPlan?.id === features.id }
      )}
    >
      <PlanHeader
        id={features.id}
        title={features.title}
        price={features.price}
        description={features.description}
      />
      {map(plansTitle, (item: any) => (
        <>
          <h6 className={'text-xl text-left mb-3'}>&nbsp;</h6>
          <ul role="list" className="mb-8 space-y-2 text-left w-full">
            {map(item.features, (feature) => (
              <li className="flex items-center space-x-3 border-b border-color-border mb-1 pb-2 w-full">
                {features[feature.key] && features[feature.key] !== '' ? (
                  <>
                    <i className={'text-base text-color-minor icon-information'} />
                    <span className={'text-sm text-color-minor'}> {features[feature.key]}</span>
                  </>
                ) : (
                  <>&nbsp;</>
                )}
              </li>
            ))}
          </ul>
        </>
      ))}
    </div>
  );
};
export default PlanItem;
