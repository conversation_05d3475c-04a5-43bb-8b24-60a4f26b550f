'use client';

import React from 'react';

import Input from 'components/Form/Input';
import { useTranslations } from 'next-intl';

const NotificationPage = () => {
  const t = useTranslations();
  const listNotification = [
    {
      id: 1,
      name: 'Không muốn nhận thông báo',
    },
    {
      id: 2,
      name: '<PERSON><PERSON>t thành viên hoàn thành 1 bài học',
    },
    {
      id: 3,
      name: '<PERSON>ột thành viên hoàn thành 1 bài luyện nói từ 8 điểm trở lên',
    },
    {
      id: 4,
      name: '<PERSON><PERSON><PERSON> thành viên hoàn thành 1 bài tập từ 8 điểm trở lên',
    },
    {
      id: 5,
      name: 'Nhắc gia hạn dịch vụ',
    },
  ];

  return (
    <div className={' py-3 pl-[30px]'}>
      <div className={'mt-4'}>
        <h6 className={'text-sm'}>{t('settings.emailGetNotice')}</h6>
        <Input className={'h-8 mt-2 w-80'} />
      </div>
      <div className={'text-sm text-color-major mt-4'}>
        {listNotification.map((item, index) => {
          return (
            <div key={index} className={' border-b border-color-line py-1 mb-1'}>
              {item.name}
            </div>
          );
        })}
      </div>
      <div className="mb-5"></div>
    </div>
  );
};

export default NotificationPage;
