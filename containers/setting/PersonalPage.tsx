'use client';

import React from 'react';

import Input from 'components/Form/Input';
import Image from 'components/Image';
import { useTranslations } from 'next-intl';

const PersonalPage = () => {
  const t = useTranslations();

  return (
    <div className={' py-3 pl-[30px]'}>
      <div className={'mt-4'}>
        <h6 className={'text-sm'}>{t('settings.avatar')}</h6>
        <div className={'mt-4'}>
          <Image
            src={'/ent/images/avatar1.png'}
            width={128}
            height={128}
            className={'bg-bg-box rounded-full border border-color-border'}
            alt={''}
          />
        </div>
        <div className={'mt-4 text-sm'}>
          <label className={'my-2 block'}>{t('settings.phone')}</label>
          <Input className={'w-80 h-8'} />
        </div>
        <div className={'mt-4 text-sm'}>
          <label className={'my-2 block'}>{t('settings.fullName')}</label>
          <Input className={'w-80 h-8'} />
        </div>
      </div>
      <div className="mb-5"></div>
    </div>
  );
};

export default PersonalPage;
