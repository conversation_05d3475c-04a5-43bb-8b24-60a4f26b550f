'use client';

import React from 'react';

import CategoryEnum from '@/configs/CategoryEnum';
import useCategoryStore from '@/store/category';
import { CategoryEntity } from '@/types/model';
import { SharedSelection } from '@heroui/system';
import Select from 'components/Select';
import saveUserThemeHook from 'hooks/common/saveUserThemeHook';
import { useTranslations } from 'next-intl';
import { useTheme } from 'next-themes';
import toast from 'react-hot-toast';

import { useSession } from '@/hooks/useSession';

const ThemeSelector = () => {
  const t = useTranslations();
  const { theme, setTheme } = useTheme();
  const { data: session } = useSession();
  const { categories } = useCategoryStore();
  const themesConfigList: CategoryEntity[] =
    categories?.filter((item: CategoryEntity) => item.parent_id === CategoryEnum.THEME_LIST) || [];

  const handleSetTheme = (key: SharedSelection) => {
    const selectedId = key.currentKey as string;
    const myTheme = themesConfigList.find((item) => item.keyx === selectedId);
    if (!myTheme) return;
    const toastId = toast.loading(t('message.toast_loading'));
    saveUserThemeHook(myTheme as CategoryEntity, session.accessToken).then(() => {
      setTheme(myTheme.keyx);
      toast.dismiss(toastId);
    });
  };
  if (!themesConfigList) return <></>;
  return (
    <>
      <div className={'flex justify-between items-center mt-4'}>
        <div className={''}>
          <h6 className={'text-sm'}>{t('settings.workspace.theme.title')}</h6>
          <h6 className={'text-sm text-color-minor mt-1'}>
            {t('settings.workspace.theme.description')}{' '}
          </h6>
        </div>

        <Select
          label={'Theme'}
          size={'sm'}
          value={'keyx'}
          selectedKeys={[theme || 'light']}
          items={themesConfigList}
          onSelectionChange={handleSetTheme}
          classNames={{
            base: 'w-32 !py-[5px]',
            trigger: 'h-[29px] bg-bg-general hover:bg-bg-general',
          }}
        />
      </div>
      <hr className="my-4 md:min-w-full border-color-border " />
    </>
  );
};
export default ThemeSelector;
