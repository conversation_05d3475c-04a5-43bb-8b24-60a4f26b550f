import React from 'react';

import { Locale } from '@/configs/config.i18n';
import { SharedSelection } from '@heroui/system';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import Select from '@/components/Select';

import { getLocales } from '@/hooks/Ent/useUserConfig';
import saveUserLanguageHook from '@/hooks/common/saveUserLanguageHook';
import { useSession } from '@/hooks/useSession';

const LanguageSelector = ({ locale }) => {
  const t = useTranslations();
  const { data } = useSession();
  const handleSetLang = (key: SharedSelection) => {
    const selectedLocale = key.currentKey as Locale;
    const toastId = toast.loading(t('message.toast_loading'));
    saveUserLanguageHook(selectedLocale, data.accessToken).then(() => toast.dismiss(toastId));
  };
  const locales = getLocales(locale);
  if (!locales) return <></>;
  return (
    <>
      <div className={'flex justify-between items-center'}>
        <div className={'mt-4'}>
          <h6 className={'text-sm'}>{t('settings.workspace.language.title')}</h6>
          <h6 className={'text-sm text-color-minor mt-1'}>
            {t('settings.workspace.language.description')}{' '}
          </h6>
        </div>

        <Select
          label={'Language'}
          size={'sm'}
          value={'keyx'}
          selectedKeys={[locale]}
          items={locales}
          onSelectionChange={handleSetLang}
          classNames={{
            base: 'w-32 !py-[5px]',
            trigger: 'h-[29px] bg-bg-general hover:bg-bg-general',
          }}
        />
      </div>
      <hr className="my-4 md:min-w-full border-color-border " />
    </>
  );
};

export default LanguageSelector;
