import React from 'react';

import { SharedSelection } from '@heroui/system';
import { useTranslations } from 'next-intl';

import Select from '@/components/Select';

import { getFontSize } from '@/hooks/Ent/useUserConfig';
import saveUserFontSizeHook from '@/hooks/common/saveUserFontSizeHook';

const FontSelector = ({ locale }) => {
  const t = useTranslations();
  const handleSetFontSize = (key: SharedSelection) => {
    const selectedFontSize = key.currentKey as string;
    saveUserFontSizeHook(selectedFontSize);
  };
  const listFontSize = getFontSize(locale);
  if (!listFontSize) return <></>;
  return (
    <>
      <div className={'flex justify-between items-center'}>
        <div className={'mt-4'}>
          <h6 className={'text-sm'}>{t('settings.workspace.fontsize.title')}</h6>
          <h6 className={'text-sm text-color-minor mt-1'}>
            {t('settings.workspace.fontsize.description')}{' '}
          </h6>
        </div>
        <Select
          label={'Font chữ'}
          size={'sm'}
          value={'valuex'}
          items={listFontSize}
          onSelectionChange={handleSetFontSize}
          classNames={{
            base: 'w-32 !py-[5px]',
            trigger: 'h-[29px] bg-bg-general hover:bg-bg-general',
          }}
        />
      </div>
      <hr className="my-4 md:min-w-full border-color-border " />
    </>
  );
};
export default FontSelector;
