'use client';

import React from 'react';

import UsageSkeleton from '@/containers/setting/usage/skeleton/UsageSkeleton';
import ScrollArea from 'components/ScrollArea';
import { format } from 'date-fns';
import useTransactionMember from 'hooks/Ent/useTransactionMember';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';

const UsageMemberPageContainer = () => {
  const { memberList, page, setPage, isReachingEnd, isLoading } = useTransactionMember();
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 0px)'}
        key="member"
        dataLength={memberList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full ml-[30px]">
          <thead className="sticky top-0 z-[1] bg-bg-general">
            <tr className={'border-b border-color-border text-[13px] h-[32px] font-medium'}>
              <th className={'px-2 py-1 text-left '}>Thời gian</th>
              <th className={'px-2 py-1 text-left '}>Kim cương</th>
              <th className={'px-2 py-1 text-left '}>Trạng thái</th>
              <th className={'px-2 py-1 text-left '}>Nạp từ</th>
            </tr>
          </thead>
          <tbody>
            {memberList
              ? map(memberList, (member, key) => (
                  <tr key={key} className={'border-b border-color-border p-2 h-[42px] '}>
                    <td className={'px-2'}>
                      {format(new Date(member.created_at * 1000), 'dd/MM/yyyy HH:mm')}
                    </td>
                    <td className={'px-2'}>
                      {new Intl.NumberFormat('vi-VN').format(member.quantity)}
                    </td>
                    <td className={'px-2'}>{member.status === 2 ? 'Thành công' : 'Thất bại'}</td>
                    <td className={'px-2'}>{member?.Account?.fullname}</td>
                  </tr>
                ))
              : null}
            {isLoading ? <UsageSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};

export default UsageMemberPageContainer;
