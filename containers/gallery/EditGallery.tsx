'use client';

import React, { useEffect, useState } from 'react';

import { StatusEnum } from '@/configs/StatusEnum';
import { UploadImage } from '@/containers/gallery/UploadImage';
import { ModalConfirmDeleteSentenceGroup } from '@/containers/learn/ModalConfirmDeleteSentenceGroup';
import ModalEditGalleryContent from '@/containers/learn/ModalEditGalleryContent';
import { decodeHtmlEntities } from '@/helpers';
import { SentenceGroup } from '@/interfaces';
import { SentenceEntity } from '@/types/model';
import { AxiosError } from 'axios';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import { useUpdateSentenceGroup } from '@/hooks/Ent/useGallery';

export const EditGallery = ({
  sentenceGroup,
  sentences,
  setIsFetchingSentenceGroupsFirstTime,
}: {
  sentenceGroup: SentenceGroup | null;
  sentences: SentenceEntity[];
  setIsFetchingSentenceGroupsFirstTime: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const t = useTranslations();
  const [file, setFile] = useState<File | null>(null);
  const [openEditContent, setOpenEditContent] = useState<boolean>(false);
  const uploadImageWrapperRef = React.useRef<HTMLDivElement>(null);

  const handleChange = async (file: File) => {
    setFile(file);
  };
  const [newContent, setNewContent] = useState<string>('');

  useEffect(() => {
    if (sentences.length > 0) {
      setNewContent(
        sentences
          .flat()
          .map((item) => item.content)
          .join(' ')
      );
    }
  }, [sentences]);

  const updateSentenceGroup = useUpdateSentenceGroup();

  const handleUpdateSentenceGroup = async () => {
    try {
      if (!sentenceGroup) return;
      const oldContentDecode = decodeHtmlEntities(
        sentences
          .flat()
          .map((item) => item.content)
          .join(' ')
      );
      const newContentDecode = decodeHtmlEntities(newContent).trim();
      const formContent = newContentDecode === oldContentDecode ? '' : newContentDecode;

      if (!file && !formContent) {
        toast.error(t('message.error.gallery.content'));
        return;
      }

      await updateSentenceGroup.mutateAsync({
        file,
        sentenceGroupId: sentenceGroup.id.toString(),
        content: formContent,
      });

      setFile(null);
      setOpenEditContent(false);
      toast.success(t('message.success.update'));
    } catch (e: unknown) {
      console.log(e);
      toast.error(e instanceof AxiosError ? e.response?.data?.message : t('message.error.unknown'));
    }
  };

  useEffect(() => {
    if (file) {
      handleUpdateSentenceGroup();
    }
  }, [file]);

  const handleClickSelectFile = () => {
    setFile(null);
    const element = uploadImageWrapperRef.current?.querySelector('input[type="file"]');
    (element as HTMLElement).click();
  };

  const [openDeleteSentenceGroup, setOpenDeleteSentenceGroup] = useState<boolean>(false);

  const handleRemoveGalleryItem = async () => {
    try {
      if (!sentenceGroup) return;
      await updateSentenceGroup.mutateAsync({
        file: null,
        sentenceGroupId: sentenceGroup.id.toString(),
        status: StatusEnum.OFF,
      });
      setIsFetchingSentenceGroupsFirstTime(false);
      toast.success(t('message.success.delete_gallery'));
      setOpenDeleteSentenceGroup(false);
    } catch (e: unknown) {
      console.log(e);
      toast.error(e instanceof AxiosError ? e.response?.data?.message : t('message.error.unknown'));
    }
  };

  return (
    <>
      <ModalEditGalleryContent
        open={openEditContent}
        setOpen={setOpenEditContent}
        newContent={newContent}
        setNewContent={setNewContent}
        handleSaveGallery={handleUpdateSentenceGroup}
        isLoading={updateSentenceGroup.isPending}
      />

      <ModalConfirmDeleteSentenceGroup
        openAction={openDeleteSentenceGroup}
        setOpenAction={setOpenDeleteSentenceGroup}
        onSubmitAction={handleRemoveGalleryItem}
      />

      <div
        onClick={() => {
          handleClickSelectFile();
        }}
        className={
          'cursor-pointer w-6 h-6 rounded-full hover:bg-bg-general absolute top-2.5 right-14 flex items-center justify-center z-20'
        }
      >
        <i className={'text-base icon-image-add-fill text-color-minor'} />
      </div>
      <div
        onClick={() => {
          setOpenEditContent(true);
        }}
        className={
          'cursor-pointer w-6 h-6 rounded-full hover:bg-bg-general absolute top-2.5 right-8 flex items-center justify-center z-20'
        }
      >
        <i className={'text-base icon-pencil-line !fill-white !stroke-white text-color-minor'} />
      </div>

      <div
        onClick={() => {
          setOpenDeleteSentenceGroup(true);
        }}
        className={
          'cursor-pointer w-6 h-6 rounded-full hover:bg-bg-general absolute top-2.5 right-1.5 flex items-center justify-center z-20'
        }
      >
        <i className={'text-base icon-delete-bin text-color-minor'} />
      </div>

      <div className={'hidden'} ref={uploadImageWrapperRef}>
        <UploadImage handleChange={handleChange} type={'edit'} />
      </div>
    </>
  );
};
