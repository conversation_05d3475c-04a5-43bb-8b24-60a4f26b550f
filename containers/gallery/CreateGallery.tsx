'use client';

import React, { Dispatch, SetStateAction } from 'react';

import NextImage from 'next/image';

import ScrollArea from '../../components/ScrollArea';

type CreateGallerySecondStepProps = {
  files: FileList;
  contents: string[];
  setContents: Dispatch<SetStateAction<string[]>>;
  onDelete: (index: number) => void;
};

export const CreateGallery = ({
  files,
  onDelete,
  contents,
  setContents,
}: CreateGallerySecondStepProps) => {
  return (
    <div
      className={
        'w-full flex-1 bg-bg-general gap-1 rounded-[5px] border border-solid border-color-border flex flex-col items-center justify-center'
      }
    >
      <ScrollArea className={'w-full h-[calc(100vh-168px)] flex flex-col gap-4'}>
        {Object.keys(files).map((key, index) => {
          return (
            <div key={key} className={'w-full flex flex-col items-center'}>
              <div
                className={
                  'w-full h-[330px] gap-5 flex flex-col items-center justify-center px-[15px] border-b-[10px] border-solid border-color-border'
                }
              >
                <div className={'relative w-full h-full bg-bg-box rounded-[5px]'}>
                  <NextImage
                    src={URL.createObjectURL(files[key])}
                    alt={''}
                    fill
                    objectFit={'contain'}
                  />
                  <div
                    onClick={() => onDelete(Number(key))}
                    className={
                      'w-5 h-5 cursor-pointer absolute top-2.5 right-2.5 bg-[#6B6F76] opacity-40 rounded-full flex items-center justify-center'
                    }
                  >
                    <i className={'w-4 h-4 icon-close text-lg text-white'} />
                  </div>
                </div>
                <textarea
                  className={
                    'w-full bg-transparent text-black text-base font-medium text-color-major'
                  }
                  placeholder={'Điền nội dung'}
                  rows={3}
                  value={contents[index]}
                  onChange={(e) => {
                    const newContents = [...contents];
                    newContents[index] = e.target.value;
                    setContents(newContents);
                  }}
                />
              </div>
            </div>
          );
        })}
      </ScrollArea>
    </div>
  );
};
