'use client';

import React, { useState } from 'react';

import { FILE_TYPES } from '@/constant';
import { useTranslations } from 'next-intl';
import { FileUploader } from 'react-drag-drop-files';

type UploadImageProps = {
  handleChange: (files: FileList | File) => void;
  type?: 'create' | 'edit';
};

export const UploadImage = ({ handleChange, type = 'create' }: UploadImageProps) => {
  const [error, setError] = useState<string>('');
  const t = useTranslations();
  return (
    <FileUploader
      classes={
        'w-full flex-1 bg-bg-box gap-1 rounded-[5px] border border-solid border-color-border flex flex-col items-center justify-center cursor-pointer'
      }
      multiple={type === 'create'}
      handleChange={handleChange}
      name="file"
      maxSize={5}
      types={FILE_TYPES}
      onTypeError={() => {
        setError(`Sai định dạng ảnh, vui lòng chọn file định dạng ${FILE_TYPES.join(', ')}`);
      }}
      onSizeError={() => {
        setError('Dung lượng ảnh không được vượt quá 5MB');
      }}
    >
      <i className={'icon-image-add-fill text-[72px]'} />
      <p className={'text-[#3C4149] font-medium text-base'}>{t('gallery.form.add_image')}</p>
      <p className={'text-[#6B6F76] font-medium text-sm'}>{t('gallery.form.or_drag_drop')}</p>
      {error && <p className={'text-red font-semibold text-base'}>{error}</p>}
    </FileUploader>
  );
};
