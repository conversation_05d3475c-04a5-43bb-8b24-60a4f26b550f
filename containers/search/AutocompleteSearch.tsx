'use client';

import React, { ReactElement, memo, useEffect, useRef, useState } from 'react';

import { useRouter } from 'next/navigation';

import { POPULAR_KEYWORD_PARENT_ID } from '@/configs';
import { CategoryEntity } from '@/types/model';
import classNames from 'classnames';
import Input from 'components/Input';
import ScrollArea from 'components/ScrollArea';
import Skeleton from 'components/Skeleton';
import EntRouters from 'configs/EntRouters';
import { createRouterLearn } from 'helpers';
import useAutocomplete from 'hooks/Ent/useAutocomplete';
import useSearch from 'hooks/Ent/useSearch';
import useClickOutside from 'hooks/common/useClickOutside';
import { useTranslations } from 'next-intl';
import useCategoryStore from 'store/category';
import useLayoutStore from 'store/layout';
import useSearchStore from 'store/search';
import { SearchResultItem } from 'types/hooks';
import { typeStringToIconMap } from 'utils/common';

const AutocompleteSearch = () => {
  const overlayRef = useRef<HTMLDivElement | null>(null);
  const inputSearchRef = useRef<ReactElement>(null);
  const { categories } = useCategoryStore();
  const [popularKeywords, setPopularKeywords] = useState<CategoryEntity[]>([]);
  const router = useRouter();
  const t = useTranslations();
  const blurInput = () => {
    setShowPanel(false);
  };
  const { keywordDebounce, keyword, setPageSearch } = useSearchStore();
  useClickOutside(overlayRef, blurInput);

  const { searchResults, isLoading } = useSearch({
    title: keywordDebounce,
    shouldNotFetch: keywordDebounce.length === 0,
  });
  const { setEnableScroll } = useLayoutStore();

  const handleViewItem = (item: SearchResultItem) => {
    return router.push(createRouterLearn(item));
  };
  const {
    activeSuggestion,
    keywordsHistory,
    showSearchPanel,
    handleKeyDown,
    handleChangeKeyword,
    handleClick,
    setShowPanel,
    removeFromHistory,
  } = useAutocomplete(searchResults, inputSearchRef.current);

  useEffect(() => {
    if (showSearchPanel) {
      setEnableScroll(false);
    } else {
      setEnableScroll(true);
    }
  }, [showSearchPanel]);

  useEffect(() => {
    if (categories) {
      const _popularKeywords =
        categories &&
        categories.filter((item: CategoryEntity) => item.parent_id === POPULAR_KEYWORD_PARENT_ID);
      setPopularKeywords(_popularKeywords);
    }
  }, [categories]);

  const isEmptySearchResult =
    (!searchResults || searchResults.length <= 0) && keywordDebounce.length > 2;
  // eslint-disable-next-line react/display-name
  const LoadingSearch = memo(() => {
    return (
      <div className={'max-h-[40vh] w-full overflow-y-scroll'}>
        <div className={classNames('w-full gap-x-2 px-4')}>
          <Skeleton className="my-2 rounded-sm w-full h-6" />
          <Skeleton className="my-2 rounded-sm w-full h-6" />
          <Skeleton className="my-2 rounded-sm w-full h-6" />
          <Skeleton className="my-2 rounded-sm w-full h-6" />
          <Skeleton className="my-2 rounded-sm w-full h-6" />
          <Skeleton className="my-2 rounded-sm w-full h-6" />
          <Skeleton className="my-2 rounded-sm w-full h-6" />
          <Skeleton className="my-2 rounded-sm w-full h-6" />
          <Skeleton className="my-2 rounded-sm w-full h-6" />
        </div>
      </div>
    );
  });
  const handleViewAll = () => {
    blurInput();
    setPageSearch(1);
    router.push(EntRouters.search);
  };
  // eslint-disable-next-line react/display-name
  const EmptySearch = memo(() => {
    return (
      <div className={'h-32 w-full flex justify-center items-center min-h-[100px] shadow-lg'}>
        <h4>{t('search.no_search_result')}</h4>
      </div>
    );
  });
  return (
    <div className={'relative w-full flex items-center h-[35px] lg:w-8/12'} ref={overlayRef}>
      <Input
        type={'text'}
        placeholder={t('search.placeholder')}
        className={'h-9 w-full border-0 z-0'}
        startContent={<i className={'text-base icon-search'} />}
        defaultValue={keyword}
        onValueChange={handleChangeKeyword}
        onKeyDown={handleKeyDown}
        ref={inputSearchRef}
        onFocus={() => setShowPanel(true)}
        classNames={{
          mainWrapper: 'rounded-md bg-bg-box',
          innerWrapper: 'bg-bg-box border-0 rounded-md w-full ',
          input: 'text-medium',
        }}
      />

      <div
        className={classNames(
          'bg-bg-general absolute top-9 w-full flex rounded-md shadow-lg z-20 ',
          {
            hidden: !showSearchPanel,
          }
        )}
      >
        {!keywordDebounce.length ? (
          <ScrollArea className={'w-full text-normal h-[45vh] p-4 !overflow-y-scroll'}>
            <div className={'w-full'}>
              {popularKeywords ? (
                <>
                  <h4 className={'text-normal pb-4 text-color-minor'}>
                    {t('search.popular_keyword')}
                  </h4>
                  {popularKeywords.map((popularKw: any) => (
                    <span
                      key={popularKw.title}
                      onClick={() => handleClick(popularKw.title)}
                      className={
                        'py-1 px-2 cursor-pointer bg-bg-box border border-color-border rounded-[5px] mr-2 hover:bg-bg-box'
                      }
                    >
                      {popularKw.title}
                    </span>
                  ))}
                  <h4 className={'text-normal pb-2 text-color-minor mt-4 pt-2'}>
                    {t('search.search_history')}
                  </h4>
                  {keywordsHistory.map((kw: string, index) => (
                    <div
                      key={index}
                      className={classNames(
                        'group-hover:!block w-full h-8 flex items-center justify-between cursor-pointer leading-8 px-[10px] hover:bg-bg-box',
                        {}
                      )}
                    >
                      <span className={'block w-full'} onClick={() => handleClick(kw)}>
                        {kw}
                      </span>
                      <div onClick={() => removeFromHistory(kw)}>
                        <i
                          className={
                            'text-base icon-close text-color-minor group-hover:!block cursor-pointer'
                          }
                        />
                      </div>
                    </div>
                  ))}
                </>
              ) : null}
            </div>
          </ScrollArea>
        ) : (
          <>
            {isLoading ? (
              <LoadingSearch />
            ) : isEmptySearchResult ? (
              <EmptySearch />
            ) : (
              <ScrollArea className={'w-full text-normal h-[45vh] !overflow-y-scroll'}>
                <div className={'-mr-2 flex flex-col justify-between content-between w-full'}>
                  <div>
                    {searchResults.map((item: SearchResultItem, index) => (
                      <div
                        key={index}
                        className={classNames(
                          'w-full py-[5px] px-4 flex items-center gap-x-2 cursor-pointer hover:bg-bg-box justify-between',
                          {
                            'bg-bg-box': index === activeSuggestion - 1,
                          }
                        )}
                        onClick={() => handleViewItem(item)}
                      >
                        <div className={'flex  gap-x-2'}>
                          <div className="pt-[3px]">
                            <i className={`text-normal ${typeStringToIconMap[item?.item] || ''}`} />
                          </div>

                          {/* {item.item === 'course' ? <i className={'text-normal icon-course'} /> : null}
                          {item.item === 'document' ? <i className={'text-normal icon-document'} /> : null}
                          {item.item === 'paragraph' ? <i className={'text-normal icon-gallery'} /> : null} */}
                          <span className={'items-center'}>{item.title}</span>
                        </div>
                        {/* <span>{item.item}</span> */}
                      </div>
                    ))}
                  </div>
                  {searchResults.length >= 100 && (
                    <div className=" flex items-center justify-center w-full">
                      <div
                        onClick={handleViewAll}
                        className={
                          'text-center bg-bg-box/60 px-2 py-1 m-2 border border-color-border cursor-pointer hover:bg-bg-box rounded-sm'
                        }
                      >
                        {t('home.viewMoreResult')}
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default AutocompleteSearch;
