import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const SearchContainerSkeleton = () => {
  return (
    <>
      {map(range(1, 30), (index) => (
        <tr className="border-b border-bg-box h-[43px] items-center" key={index}>
          <td className={'pl-[30px] min-h-[43px] flex items-center gap-x-3 py-[10px] pr-[5px]'}>
            <Skeleton className="rounded-sm h-6 w-6" />
            <div className={'flex flex-col gap-1 w-full'}>
              <Skeleton className="rounded-sm w-2/5 h-3" />
              <Skeleton className="rounded-sm w-1/5 h-2" />
            </div>
          </td>
          <td>
            <Skeleton className="rounded-sm w-3/5 h-3" />
          </td>
          <td>
            <Skeleton className="rounded-sm w-3/5 h-3" />
          </td>
          <td className={'w-[120px] text-color-minor pr-[20px] flex justify-end py-[10px]'}>
            <Skeleton className="rounded-sm w-20 h-3" />
          </td>
        </tr>
      ))}
    </>
  );
};
export default SearchContainerSkeleton;
