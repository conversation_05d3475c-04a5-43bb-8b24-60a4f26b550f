'use client';

import React, { useEffect } from 'react';

import Link from 'next/link';

import EntRouters from '@/configs/EntRouters';
import SearchContainerSkeleton from '@/containers/search/SearchContainerSkeleton';
import { SearchResultItem } from '@/types/hooks';
import { typeStringToIconMap } from '@/utils/common';
import useFavouriteAll from 'hooks/Ent/useFavouriteAll';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';
import useSearchStore from 'store/search';

import Button from '@/components/Button';
import FavouriteButton from '@/components/FavouriteButton';
import GemIcon from '@/components/Icons/GemIcon';
import NoContent from '@/components/NoContent';
import ScrollArea from '@/components/ScrollArea';

import useSearchEngine from '@/hooks/Ent/useSearchEngine';

const SearchContainer = () => {
  const { favouriteAll } = useFavouriteAll();
  const t = useTranslations();
  const { type, keyword, setType } = useSearchStore();

  useEffect(() => {
    setType('all');
  }, []);

  const { searchResults, isLoading, isReachingEnd, setPage, page } = useSearchEngine({
    title: keyword,
    item: type,
  });

  const buildLink = (row: SearchResultItem) => {
    if (row.item === 'document') return `${EntRouters.document}/${row.document_id}`;
    if (row.item === 'course') return `${EntRouters.course}/${row.course_id}`;
    return `${EntRouters.learn}/${row.keyx}`;
  };

  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 15px)'}
        dataLength={searchResults.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{t('search.lesson')}</th>
              <th className={'px-2 py-1 text-left font-normal '}>Trình độ</th>
              <th>&nbsp;</th>
              <th>&nbsp;</th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {!isLoading && !searchResults.length ? (
              <tr>
                <td colSpan={4}>
                  <NoContent />
                </td>
              </tr>
            ) : null}
            {map(searchResults, (item, key) => (
              <tr
                key={`tr-${key}`}
                className="bg-bg-general hover:bg-bg-box border-b border-bg-box [&>td>div]:hidden [&>td>div]:hover:flex [&>td>button]:hidden [&>td>button]:hover:flex"
              >
                <td key={`td-1-${key}`} className={'p-2 pl-[30px] h-[42px] flex items-center'}>
                  <Link className="text-normal flex items-center gap-x-2" href={buildLink(item)}>
                    <i
                      className={`text-normal ${
                        item?.payload?.item ? typeStringToIconMap[item.payload.item] : ''
                      }`}
                    />
                    {item?.payload?.title}
                  </Link>
                  <FavouriteButton favouriteList={favouriteAll} item={''} object_id={0} />
                </td>
                <td>{item?.payload?.level}</td>
                <td key={`td-2-${key}`} className={'p-2 text-purple w-[100px] hidden'}>
                  {item.id === 2 ? (
                    <span className={'bg-bg-box px-2 py-1 rounded-sm'}>{t('payed')}</span>
                  ) : (
                    <span className={'flex items-center'}>
                      <GemIcon className={'fill-purple w-4 h-4 mr-1'} />
                      {item.id}
                    </span>
                  )}
                </td>
                <td key={`td-3-${key}`} className={'w-[120px]'}>
                  {item.id === 2 ? (
                    <Button color={'primary'} size={'xs'}>
                      {t('course.learnNow')}
                    </Button>
                  ) : null}
                </td>
              </tr>
            ))}
            {isLoading ? <SearchContainerSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};

export default SearchContainer;
