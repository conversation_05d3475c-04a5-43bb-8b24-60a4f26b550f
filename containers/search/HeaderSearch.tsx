'use client';

import React from 'react';

import AutocompleteSearch from '@/containers/search/AutocompleteSearch';
import AppHeader from 'containers/layout/AppHeader';

const HeaderSearch: React.FunctionComponent = () => {
  return (
    <AppHeader bottom={'1px'} className={'h-[58px]'}>
      <div className={'px-[30px] relative w-full'}>
        <div className="w-full ">
          <AutocompleteSearch key={'autocomplete'} />
        </div>
      </div>
    </AppHeader>
  );
};

export default HeaderSearch;
