'use client';

import React from 'react';

import AutocompleteSearch from '@/containers/search/AutocompleteSearch';
import classNames from 'classnames';
import AppHeader from 'containers/layout/AppHeader';
import { useTranslations } from 'next-intl';
import useHeaderStore from 'store/header';

const Header: React.FunctionComponent = () => {
  const [showSearch, setShowSearch] = React.useState(false);
  const { title } = useHeaderStore();
  const t = useTranslations();
  return (
    <AppHeader bottom={'1px'}>
      <div className={'h-[43px] px-[30px] w-full flex items-center justify-between'}>
        <div className={'flex items-center '}>
          <span className={classNames('flex gap-x-2', { '!hidden': showSearch })}>
            <i className={'icon-course text-[16px]'} />
            {title != '' ? title : t('course.title')}
          </span>
          <div
            className={classNames(' w-0 opacity-0', {
              'transition-all duration-200 ease-in-out !w-full !opacity-100': showSearch,
            })}
          >
            <AutocompleteSearch key={'autocomplete-course-root'} />
          </div>
        </div>
        <div className={'flex flex-grow-0 items-center justify-end'}>
          <div
            className={
              'border cursor-pointer border-color-border mr-3 align-baseline rounded-md inline-block p-1'
            }
            onClick={() => setShowSearch(showSearch)}
          >
            <i className={'text-base icon-search'} />
          </div>
        </div>
      </div>
    </AppHeader>
  );
};

export default Header;
