import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const AlphabetSkeleton = () => {
  return (
    <>
      <div className="w-full flex items-center gap-4 p-4" key={-1}>
        <Skeleton className="rounded-sm h-32 w-1/3" />
        <Skeleton className="rounded-sm h-32 w-1/3" />
        <Skeleton className="rounded-sm h-32 w-1/3" />
      </div>
      <div className="h-[10px] bg-bg-box/60 w-full"></div>

      {map(range(1, 11), (index) => (
        <div className="w-full space-y-3 p-4" key={index}>
          <Skeleton className="rounded-sm h-4 w-full" />
          <Skeleton className="rounded-sm w-3/5 h-3" />
          <div className="h-[10px] bg-bg-box/60 w-full"></div>
        </div>
      ))}
    </>
  );
};
export default AlphabetSkeleton;
