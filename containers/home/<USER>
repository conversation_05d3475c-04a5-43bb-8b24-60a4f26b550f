'use client';

import React from 'react';

import { NewsTypeEnum } from '@/configs/NewsTypeEnum';
import classNames from 'classnames';
import FacebookStyleGallery from 'components/FacebookStyleGallery';
import Link from 'components/Link';
import SentenceHome from 'containers/home/<USER>';
import { map } from 'lodash';
import { typeStringToIconMap } from 'utils/common';

const Alphabet = ({ newsItem }) => {
  return (
    <>
      {newsItem.type === NewsTypeEnum.SENTENCE && newsItem?.[newsItem.item]?.description && (
        <>
          <div key={`div-${newsItem.type}-${newsItem.id}`} className="ml-[30px] py-[10px]">
            <Link
              href={`/${
                newsItem?.[newsItem.item]?.item === 'conversation' ||
                newsItem?.[newsItem.item]?.item === 'essay' ||
                newsItem?.[newsItem.item]?.item === 'gallery'
                  ? 'learn'
                  : 'learn'
              }/${newsItem?.[newsItem.item]?.keyx}`}
            >
              <div className="flex items-center font-bold">
                <i
                  className={`text-base ${typeStringToIconMap[newsItem?.[newsItem.item]?.item] || ''}`}
                />{' '}
                <span className="ml-[10px]">{newsItem?.[newsItem.item]?.title}</span>
              </div>

              <span className="text-color-minor text-[13px] ml-[32px]">
                {newsItem?.[newsItem.item]?.title_vi}
              </span>
            </Link>
          </div>
          {!newsItem.new_details && (
            <>
              <div
                key={`div-${newsItem.type}-1-${newsItem.id}`}
                className="flex items-center justify-between mx-[30px] py-[9px]"
              >
                <span
                  dangerouslySetInnerHTML={{ __html: newsItem?.[newsItem.item]?.description }}
                />
              </div>
              {newsItem[newsItem.item].description_vi ? (
                <div
                  key={`div-${newsItem.type}-1-${newsItem.id}-vi`}
                  className={'px-[30px] text-color-minor pb-3'}
                  dangerouslySetInnerHTML={{ __html: newsItem[newsItem.item]?.description_vi }}
                />
              ) : null}
            </>
          )}

          {!newsItem.new_details && (
            <div
              key={`div-${newsItem.type}-2-${newsItem.id}`}
              className="h-[10px] bg-bg-box/60 w-full"
            ></div>
          )}
        </>
      )}
      {(newsItem.type === NewsTypeEnum.CONVERSATION || newsItem.type === NewsTypeEnum.ESSAY) &&
        newsItem[newsItem.item]?.description && (
          <>
            <div key={`div-${newsItem.type}-${newsItem.id}`} className="ml-[30px] py-[10px]">
              <Link
                prefetch={false}
                href={`/${
                  newsItem?.[newsItem.item]?.item === 'conversation' ||
                  newsItem?.[newsItem.item]?.item === 'essay' ||
                  newsItem?.[newsItem.item]?.item === 'gallery'
                    ? 'learn'
                    : 'learn'
                }/${newsItem?.[newsItem.item]?.keyx}`}
              >
                <div className="flex items-center font-bold">
                  <i
                    className={`text-base ${
                      typeStringToIconMap[newsItem?.[newsItem.item]?.item] || ''
                    }`}
                  />{' '}
                  <span className="ml-[10px]">{newsItem?.[newsItem.item]?.title}</span>
                </div>

                <span className="text-color-minor text-[13px] ml-[32px]">
                  {newsItem?.[newsItem.item]?.title_vi}
                </span>
              </Link>
            </div>
            {!newsItem.new_details && (
              <>
                <div
                  key={`div-${newsItem.type}-1-${newsItem.id}`}
                  className="px-[30px] py-[9px]"
                  dangerouslySetInnerHTML={{ __html: newsItem?.[newsItem.item]?.description }}
                />
                {newsItem[newsItem.item].description_vi ? (
                  <div
                    key={`div-${newsItem.type}-1-${newsItem.id}-vi`}
                    className={'px-[30px] text-color-minor py-[9px]'}
                    dangerouslySetInnerHTML={{ __html: newsItem[newsItem.item]?.description_vi }}
                  />
                ) : null}
              </>
            )}

            {!newsItem.new_details && (
              <div
                key={`div-${newsItem.type}-2-${newsItem.id}`}
                className="h-[10px] bg-bg-box/60 w-full"
              ></div>
            )}
          </>
        )}

      {newsItem.type === NewsTypeEnum.GALLERY && (
        <>
          <div key={`div-${newsItem.type}-${newsItem.id}`} className="ml-[30px] py-[10px]">
            <Link
              prefetch={false}
              href={`/${
                newsItem?.[newsItem.item]?.item === 'conversation' ||
                newsItem?.[newsItem.item]?.item === 'essay' ||
                newsItem?.[newsItem.item]?.item === 'gallery'
                  ? 'learn'
                  : 'learn'
              }/${newsItem?.[newsItem.item]?.keyx}`}
            >
              <div className="flex items-center font-bold">
                <i
                  className={`text-base ${
                    typeStringToIconMap[newsItem?.[newsItem.item]?.item] || ''
                  }`}
                />{' '}
                <span className="ml-[10px]">{newsItem?.[newsItem.item]?.title}</span>
              </div>

              <span className="text-color-minor text-[13px] ml-[32px]">
                {newsItem?.[newsItem.item]?.title_vi}
              </span>
            </Link>
          </div>

          <div
            key={`div-${newsItem.type}-1-${newsItem.id}`}
            className="flex justify-center px-[30px]"
          >
            <Link
              prefetch={false}
              href={`/${
                newsItem?.[newsItem.item]?.item === 'conversation' ||
                newsItem?.[newsItem.item]?.item === 'essay' ||
                newsItem?.[newsItem.item]?.item === 'gallery'
                  ? 'learn'
                  : 'learn'
              }/${newsItem?.[newsItem.item]?.keyx}`}
            >
              {newsItem?.[newsItem.item]?.sentence_groups && (
                <FacebookStyleGallery images={newsItem?.[newsItem.item]?.sentence_groups} />
              )}
            </Link>
          </div>
          <div
            key={`div-${newsItem.type}-2-${newsItem.id}`}
            className={classNames(
              'h-[10px] bg-bg-box/60 w-full',
              newsItem?.[newsItem.item]?.sentence_groups?.length ? 'mt-[20px]' : ''
            )}
          ></div>
        </>
      )}
      {newsItem.new_details &&
        map(
          newsItem.new_details,
          (sen, key) =>
            sen.sentence?.content && (
              <SentenceHome
                key={`sentence-${sen.sentence.id}`}
                content={sen.sentence.content}
                audio={sen?.sentence.audios?.[0]?.url}
                index={parseInt(key)}
                sentences={sen.sentence}
              />
            )
        )}
      {newsItem.new_details ? <div className="h-[10px] bg-bg-box/60 w-full"></div> : null}
    </>
  );
};

export default Alphabet;
