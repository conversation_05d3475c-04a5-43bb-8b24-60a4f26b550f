'use client';

import React from 'react';

import AutocompleteSearch from '@/containers/search/AutocompleteSearch';
import AppHeader from 'containers/layout/AppHeader';

// import Headlayout from 'components/HeadLayout';

const HeaderSection: React.FC = () => {
  return (
    <AppHeader className={'h-[58px]'} bottom={'0px'}>
      <div className={'px-[30px] relative grid w-full'}>
        <div className="w-full">
          <AutocompleteSearch key={'autocomplete'} />
        </div>
      </div>
    </AppHeader>
  );
};

export default HeaderSection;
