'use client';

import React, { useEffect } from 'react';

import AlphabetSkeleton from '@/containers/home/<USER>/AlphabetSkeleton';
import Alphabet from 'containers/home/<USER>';
import useHome from 'hooks/Ent/useHome';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';
import useSearchStore from 'store/search';

const SectionWelcome: React.FC = () => {
  const { setType } = useSearchStore();
  const { newList, setPage, page, isLoading, isReachingEnd } = useHome();
  useEffect(() => {
    setType('');
  }, []);

  return (
    <>
      <div className="h-[10px] bg-bg-box w-full"></div>
      <InfiniteScroll
        height={'calc(100vh - 63px)'}
        dataLength={newList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        {newList && newList.length > 0 ? (
          map(newList, (newsItem) => <Alphabet newsItem={newsItem} key={newsItem.id} />)
        ) : (
          <AlphabetSkeleton />
        )}
        {isLoading ? <AlphabetSkeleton /> : null}
      </InfiniteScroll>
    </>
  );
};

export default SectionWelcome;
