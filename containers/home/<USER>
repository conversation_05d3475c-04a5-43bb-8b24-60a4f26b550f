class AudioManager {
  private currentAudio: HTMLAudioElement | null;

  constructor() {
    this.currentAudio = null;
  }

  playAudio(audioUrl: string) {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
    }

    this.currentAudio = new Audio(audioUrl);
    this.currentAudio.play();

    this.currentAudio.onended = () => {
      this.currentAudio = null;
    };
  }
}

const audioManager = new AudioManager();
export default audioManager;
