'use client';

import React, { useEffect, useState } from 'react';

import { HOMEPAGE_PANEL_ID } from '@/configs';
import HomePanelSkeleton from '@/containers/home/<USER>/HomePanelSkeleton';
import { useAppContext } from '@/store/contexts/AppContext';
import { Card, CardBody, CardHeader } from '@heroui/react';
import classNames from 'classnames';
import Button from 'components/Button';
import { map } from 'lodash';
import useCategoryStore from 'store/category';
import { CategoryEntity } from 'types/model';

import ScrollArea from '@/components/ScrollArea';

import useHome from '@/hooks/Ent/useHome';

const CardPanel = ({ items }: { items: CategoryEntity }) => {
  return (
    <Card shadow="none" className="w-full bg-bg-general flex-0 rounded-none">
      <CardHeader className="pb-0">
        <h2 className="font-bold text-color-major mb-0">{items.title}</h2>
      </CardHeader>
      <CardBody className="pt-2">
        <div className="flex gap-x-3 flex-wrap">
          {items.items &&
            map(items.items, (item) =>
              item.title ? (
                <Button
                  key={item.id}
                  as={'a'}
                  className="!px-2.5 mb-3 shadow text-color-major border border-color-border"
                  href={`${item.keyx}/${item.valuex ?? ''}`}
                  variant={'bordered'}
                  size="sm"
                >
                  {item.title}
                </Button>
              ) : (
                <div key={item.id} className="w-full flex-0" />
              )
            )}
        </div>
      </CardBody>
    </Card>
  );
};

const HomePanel = () => {
  const { isOpenPanel } = useAppContext();
  const { categories } = useCategoryStore();
  const [homePanelItems, setHomePanelItems] = useState<CategoryEntity[] | null>([]);
  useEffect(() => {
    const _homePanelItems =
      categories &&
      categories.filter((item: CategoryEntity) => item.parent_id === HOMEPAGE_PANEL_ID);
    setHomePanelItems(_homePanelItems);
  }, [categories]);

  const { newList } = useHome();
  if (!newList || newList.length === 0) return <HomePanelSkeleton />;
  return (
    <ScrollArea
      className={classNames(
        ' bg-bg-general top-0 border-l border-bg-box h-screen overflow-x-hidden max-w-[450px] hidden md:block',
        {
          'right-0 w-panel': !isOpenPanel,
          '-right-1/2 w-0': isOpenPanel,
        }
      )}
    >
      <div className="h-full py-2.5 px-2">
        {homePanelItems &&
          map(homePanelItems, (items, index) => <CardPanel key={index} items={items} />)}
      </div>
    </ScrollArea>
  );
};

export default HomePanel;
