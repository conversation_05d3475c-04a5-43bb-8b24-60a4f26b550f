'use client';

import React from 'react';

// import { useTranslations } from 'next-intl';
import classNames from 'classnames';

const Music: React.FC = () => {
  //   const t = useTranslations();
  const handleAudioWord = (audio) => {
    const audioElement = new Audio(audio);
    // Phát âm thanh
    audioElement.play();
  };
  return (
    <div className="flex items-center justify-between ml-[30px] py-[10px]">
      <div className={' flex items-center'}>
        <i className={' icon-music text-medium mr-[10px]'} />
        <div>The wheel on the bus</div>
      </div>
      <div
        className={classNames(
          'flex items-center justify-center rounded-full h-[27px] w-[28px] mr-[10px] bg-bg-box cursor-pointer'
        )}
      >
        <i
          onClick={() => handleAudioWord('yyy')}
          className={' icon-play text-[20px]  text-color-minor'}
        />
      </div>
    </div>
  );
};

export default Music;
