'use client';

import React, { useCallback } from 'react';

import TranslationsSentence from '@/containers/history/Translation/TranslationsSentence';
import { useAppContext } from '@/store/contexts/AppContext';
import classNames from 'classnames';
import audioManager from 'containers/home/<USER>';
import useSentenceStore from 'store/sentence';
import { SentenceEntity } from 'types/model';
import { AppContextProps } from 'types/theme';

interface Props {
  content: string;
  audio?: string;
  index: number;
  sentences: SentenceEntity;
}

const SentenceHome: React.FC<Props> = ({ content, audio, index, sentences }) => {
  const { setSelectedSentence } = useSentenceStore();
  const { setPanel, setOpenPanel }: AppContextProps = useAppContext();
  const handleAudioWord = (audio: string) => {
    audioManager.playAudio(audio);
  };

  const handleChangeSentence = (sentence: SentenceEntity) => {
    setSelectedSentence(sentence);
    // @ts-ignore
    setPanel(TranslationCallBack);
    setOpenPanel(true);
  };
  const TranslationCallBack = useCallback(() => {
    return <TranslationsSentence />;
  }, []);
  return (
    <div
      className={classNames('flex items-center justify-between mx-[30px] py-[9px]', {
        'border-t border-bg-box border-solid': index !== 0,
      })}
    >
      <div
        onClick={() => handleChangeSentence(sentences)}
        style={{ width: 'calc(100% - 30px)' }}
        className={'cursor-pointer'}
        dangerouslySetInnerHTML={{ __html: content }}
      ></div>
      <div
        className={classNames('flex items-center justify-center rounded-full h-[24px] w-[24px]', {
          '': !audio,
          'bg-bg-box cursor-pointer': audio,
        })}
      >
        {audio ? (
          <i
            onClick={() => handleAudioWord(audio)}
            className={' icon-volume-up text-medium   text-color-minor'}
          />
        ) : (
          <span></span>
        )}
      </div>
    </div>
  );
};

export default SentenceHome;
