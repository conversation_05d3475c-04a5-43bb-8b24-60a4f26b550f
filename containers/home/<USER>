'use client';

import React, { useEffect } from 'react';

import Link from 'next/link';

import { HOMEPAGE_PANEL_ID } from '@/configs';
import { CategoryEntity } from '@/types/model';
import { map } from 'lodash';
import useCategoryStore from 'store/category';
import { CategoryChildProps } from 'types/component';

const CateChild = ({ categories, position }: CategoryChildProps) => {
  const pl = position * 30;

  return (
    <>
      {categories &&
        categories.length > 0 &&
        categories.map((category) => (
          <React.Fragment key={`tr-${category.id}`}>
            <tr className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group [&>td>button]:hidden [&>td>button]:hover:flex">
              {category.title ? (
                <>
                  <td
                    className={'p-2 h-[42px] flex items-center'}
                    style={{ paddingLeft: `${pl}px` }}
                  >
                    <Link className={'flex'} href={`${category.keyx}/${category.valuex ?? ''}`}>
                      <i className={'icon-course text-medium mr-2'} />
                      {category.title}
                    </Link>
                  </td>
                </>
              ) : (
                <td
                  colSpan={2}
                  className={'bg-bg-box'}
                  style={{ lineHeight: '10px', height: '10px' }}
                ></td>
              )}
            </tr>
            <CateChild key={category.id} categories={category.items} position={position + 1} />
          </React.Fragment>
        ))}
    </>
  );
};

const HomeTag = () => {
  const { categories } = useCategoryStore();
  const [homePanelItems, setHomePanelItems] = React.useState<CategoryEntity[]>([]);
  useEffect(() => {
    if (categories) {
      const _homePanelItems =
        categories &&
        categories.filter((item: CategoryEntity) => item.parent_id === HOMEPAGE_PANEL_ID);
      setHomePanelItems(_homePanelItems);
    }
  }, [categories]);
  return (
    <table className="table-auto w-full">
      <tbody className={'text-[0.8123rem]'}>
        {homePanelItems &&
          map(homePanelItems, (items) => (
            <React.Fragment key={`tr-${items.id}`}>
              <tr className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
                <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{items.title}</th>
                <th>&nbsp;</th>
              </tr>
              <CateChild key={items.id} categories={items.items} position={1} />
            </React.Fragment>
          ))}
      </tbody>
    </table>
  );
};

export default HomeTag;
