import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const HomePanelSkeleton = () => {
  return (
    <div
      className={
        'bg-bg-general top-0 border-l mt-2 border-bg-box h-screen overflow-x-hidden max-w-[450px] w-panel'
      }
    >
      {map(range(1, 7), (index) => (
        <div className="w-full space-y-3 p-4 mb-4" key={index}>
          <Skeleton className="rounded-sm h-4 w-4/5" />
          <div className={'flex gap-3 flex-wrap'}>
            {map(range(1, 6), (index1) => (
              <Skeleton className="rounded-sm w-24 h-6" key={index1} />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
export default HomePanelSkeleton;
