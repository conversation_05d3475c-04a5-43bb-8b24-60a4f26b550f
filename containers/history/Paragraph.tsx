'use client';

import React from 'react';

import Link from 'next/link';

import HistoryParagraphSkeleton from '@/containers/history/skeleton/HistoryParagraphSkeleton';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import useTransactionParagraph from 'hooks/Ent/useTransactionParagraph';
import { map } from 'lodash';
import { useLocale, useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';
import { typeStringToIconMap } from 'utils/common';
import { formatDateDay } from 'utils/datetime';

import NoContent from '@/components/NoContent';

const Paragraph = () => {
  const locale = useLocale();
  const { paragraphList, isReachingEnd, setPage, page, isLoading } = useTransactionParagraph();
  const t = useTranslations();
  if (!isLoading && !paragraphList.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
            <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
              {t('history.paragraph.title')}
            </th>
            <th className={'py-1 text-left font-normal'}>{t('history.paragraph.category')}</th>
            <th className={'py-1 text-right font-normal pr-[30px]'}></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={3}>
              <NoContent />
            </td>
          </tr>
        </tbody>
      </table>
    );
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 77px)'}
        key="paragraph"
        dataLength={paragraphList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px] '}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
                {t('history.paragraph.title')}
              </th>
              <th className={'py-1 text-left font-normal'}>{t('history.paragraph.category')}</th>
              <th className={'py-1 text-right font-normal pr-[30px] w-[130px]'}></th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {paragraphList.length
              ? map(paragraphList, (paragraph, key) => (
                  <tr
                    key={`tr-${key}`}
                    className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box [&>td>div]:hidden [&>td>div]:hover:flex [&>td>button]:hidden [&>td>button]:hover:flex"
                  >
                    <td key={`td-1-${key}`} className={'py-[12px] pl-[30px] flex items-center'}>
                      <Link
                        href={`${EntRouters.history}/paragraph/speak/${paragraph?.paragraph?.keyx}`}
                      >
                        <div className="flex gap-x-2">
                          <div className="pt-[3px]">
                            <i
                              className={`text-[16px] ${
                                typeStringToIconMap[paragraph?.paragraph?.item] || ''
                              }`}
                            />
                          </div>
                          <div>{paragraph?.paragraph?.title}</div>
                        </div>
                        <div className="text-color-minor text-[13px] ml-[31px]">
                          {paragraph?.paragraph?.title_vi}
                        </div>
                      </Link>
                    </td>

                    <td key={`td-3-${key}`} className="content-start py-[12px]">
                      {paragraph?.course?.title}
                    </td>
                    <td
                      key={`td-4-${key}`}
                      className={'w-[120px] text-color-minor pr-[30px] text-right'}
                    >
                      {paragraph?.updated_at ? formatDateDay(paragraph?.updated_at, locale) : ''}
                    </td>
                  </tr>
                ))
              : null}
            {isLoading ? <HistoryParagraphSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default Paragraph;
