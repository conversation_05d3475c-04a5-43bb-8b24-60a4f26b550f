'use client';

import React, { useCallback, useState } from 'react';

import HistoryWordSkeleton from '@/containers/history/skeleton/HistoryWordSkeleton';
import { useAppContext } from '@/store/contexts/AppContext';
import classNames from 'classnames';
import ScrollArea from 'components/ScrollArea';
import TranslationWords from 'containers/history/Translation/TranslationWords';
import useTransactionWord from 'hooks/Ent/useTransactionWord';
import { map } from 'lodash';
import { useLocale, useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';
import useWordStore from 'store/word';
import { AppContextProps } from 'types/theme';
import { formatDateDay } from 'utils/datetime';

import NoContent from '@/components/NoContent';

const Word = ({ historyTab }: { historyTab: string }) => {
  const { setPanel }: AppContextProps = useAppContext();
  const locale = useLocale();
  const { setSelectedSentence, setSelectedSentencePos } = useWordStore();
  const [, setPosition] = useState<number>(-1);
  const { wordList, isLoading, page, setPage, isReachingEnd } = useTransactionWord();
  const TranslationCallBack = useCallback((positionWord: number) => {
    return <TranslationWords position={positionWord} setPosition={setPosition} />;
  }, []);
  const t = useTranslations();
  const handleChangeWord = (word) => {
    setSelectedSentence(word.sentence);
    setSelectedSentencePos(word.sentence_pos);
    setPosition(word.sentence_pos.position);
    setPanel(TranslationCallBack(word.sentence_pos.position));
  };
  const handleAudioWord = (audio) => {
    const audioElement = new Audio(audio);
    // Phát âm thanh
    audioElement.play();
  };
  if (!isLoading && !wordList.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
            <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
              {t('history.word.title')}
            </th>
            <th className={'py-1 text-left font-normal'}>{t('history.word.type')}</th>
            <th className={'py-1 text-left font-normal'}>{t('history.word.inSentence')}</th>
            <th className={'py-1 text-left font-normal'}></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={4}>
              <NoContent />
            </td>
          </tr>
        </tbody>
      </table>
    );
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 77px)'}
        key={historyTab}
        dataLength={wordList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
                {t('history.word.title')}
              </th>
              <th className={'py-1 text-left font-normal pl-[5px]'}>{t('history.word.type')}</th>
              <th className={'py-1 text-left font-normal pl-[5px]'}>
                {t('history.word.inSentence')}
              </th>
              <th className={'py-1 text-left font-normal w-[130px]'}></th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {wordList.length
              ? map(wordList, (word, key) => (
                  <tr
                    key={`tr-${key}`}
                    className=" bg-bg-general hover:bg-bg-box/60 border-b border-bg-box min-h-[43px] items-center [&>td>button]:hidden [&>td>button]:hover:flex"
                  >
                    <td key={'td-1-1'} className={' pl-[30px] min-h-[43px] flex py-3 pr-[5px]'}>
                      <div className={'flex items-start justify-center'}>
                        <div
                          className={classNames(
                            'flex items-center justify-center rounded-md w-7 mr-2.5 cursor-pointer',
                            {
                              '': !word?.sentence_pos?.audio,
                              'bg-bg-box cursor-pointer h-[27px]': word?.sentence_pos?.audio,
                            }
                          )}
                        >
                          {word?.sentence_pos?.audio ? (
                            <i
                              onClick={() => handleAudioWord(word?.sentence_pos?.audio)}
                              className={'icon-volume-up text-medium text-color-minor'}
                            />
                          ) : (
                            <span></span>
                          )}
                        </div>
                        <div
                          className={'leading-5 cursor-pointer'}
                          onClick={() => handleChangeWord(word)}
                        >
                          <div className="font-medium text-[16px]">{word?.sentence_pos?.word}</div>
                          <div className="text-color-minor text-[13px]">
                            {word?.sentence_pos?.phonetic}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td key={`td-2-${key}`} className={'content-start pl-[5px] py-[10px]'}>
                      <div className={'pt-[2px]'}>{word?.sentence_pos?.word_type}</div>
                    </td>
                    <td key={`td-3-${key}`} className={'content-start  pl-[5px] py-[10px]'}>
                      <div
                        className={'pt-[2px]'}
                        dangerouslySetInnerHTML={{ __html: word?.sentence?.content || '' }}
                      ></div>
                    </td>
                    <td
                      key={`td-4-${key}`}
                      className={
                        'w-[120px] content-start text-color-minor pr-[30px] text-right py-[10px]'
                      }
                    >
                      <div className={'pt-[2px]'}>
                        {word?.updated_at ? formatDateDay(word?.updated_at, locale) : ''}
                      </div>
                    </td>
                  </tr>
                ))
              : null}
            {isLoading ? <HistoryWordSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default Word;
