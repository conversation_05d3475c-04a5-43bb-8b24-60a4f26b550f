'use client';

import React, { useState } from 'react';

import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';

import HistorySpeakSkeleton from '@/containers/history/skeleton/HistorySpeakSkeleton';
import { Skeleton } from '@heroui/react';
import classNames from 'classnames';
import WavePlayer from 'components/Audio/WavePlayer';
import Button from 'components/Button';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';

import useMemberExerciseDetail from '@/hooks/Ent/useMemberExerciseDetail';
import {useParagraph} from '@/hooks/Ent/useParagraph';

const HistorySpeaking = () => {
  const router = useRouter();
  const params = useParams();
  const { learnId } = params;
  const [page, setPage] = useState(1);
  if (!learnId) router.back();
  // const { paragraph } = useParagraph(learnId as string, Number(learnId) === 0);

  const { data: paragraph } = useParagraph({ keyx: learnId as string });

  console.log('paragraph', paragraph);
  console.log('learnId', learnId);
  const { exerciseLists, isLoading } = useMemberExerciseDetail({
    offset: page - 1,
    isFetch: true,
    keyx: (learnId ?? '').toString(),
  });

  return (
    <>
      <div className="flex pl-[20px] pb-[10px] justify-between items-center">
        <div className="flex items-center">
          <Link href={`${EntRouters.history}/paragraph`}>
            <Button
              size={'xs'}
              variant={'bordered'}
              color={'default'}
              className={'float-end px-2 ml-2.5'}
            >
              <i className={classNames('icon-arrow-left text-normal')} /> Quay lại
            </Button>
          </Link>
          <div className="ml-[20px]">
            {isLoading ? (
              <Skeleton className={`rounded-sm w-64 h-4`} />
            ) : (
              <div dangerouslySetInnerHTML={{ __html: paragraph?.title ?? '' }} />
            )}
          </div>
        </div>
      </div>
      <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
        <InfiniteScroll
          height={'calc(100vh - 77px)'}
          key="paragraph"
          dataLength={exerciseLists.length}
          next={() => setPage(page + 1)}
          hasMore={true}
          loader={null}
          className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
        >
          <table className="table-auto w-full">
            <thead className="sticky top-0 z-[3]">
              <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px] '}>
                <th className={'pr-2 pl-[34px] py-1 text-left font-normal w-[400px]'}>Câu</th>
                <th className={'py-1 text-left font-normal '}>Ghi âm đọc của học sinh</th>
                <th className={'py-1 text-left font-normal pl-[30px] pr-[30px] w-[80px]'}>Score</th>
              </tr>
            </thead>
            <tbody className={'text-[0.8123rem]'}>
              {map(exerciseLists, (item) => (
                <tr
                  key={`tr-${item.id}`}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group "
                >
                  <td className="">
                    <div className="flex items-center pl-[30px] min-h-[43px]">
                      <div>
                        <i className={'icon-sentence mr-[10px] text-[16px]'}></i>
                      </div>

                      <div className="">{item.template}</div>
                    </div>
                  </td>
                  <td className="py-[5px] pr-[30px]">
                    {item.url && (
                      <div>
                        <WavePlayer mb={0} height={32} url={item.url} />
                      </div>
                    )}
                  </td>
                  <td>
                    <div className="flex pl-[30px] items-center">
                      {item.score && (
                        <div className={'opacity-100  w-4 h-4'}>
                          <i
                            className={classNames('text-base icon-ok-circled', {
                              'text-red': item.score < 50,
                              'text-yellow': item.score < 80 && item.score >= 50,
                              'text-primary': item.score > 80,
                            })}
                          />
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
              {isLoading ? <HistorySpeakSkeleton /> : null}
            </tbody>
          </table>
        </InfiniteScroll>
      </ScrollArea>
    </>
  );
};
export default HistorySpeaking;
