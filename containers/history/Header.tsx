'use client';

import React from 'react';

import CreateCoursePopup from 'components/ServicePopup/CreateCoursePopup';
import AppHeader from 'containers/layout/AppHeader';
import { useTranslations } from 'next-intl';

const Header: React.FunctionComponent = () => {
  const [open, setOpen] = React.useState(false);
  const t = useTranslations();
  return (
    <AppHeader bottom={'0px'}>
      <div
        className={'h-[43px] px-[30px] w-full  items-center flex content-between align-baseline'}
      >
        <span className="flex items-center gap-x-2">
          <i className={'icon-history text-[16px]'} />
          <div>{t('history.title')}</div>
        </span>

        <CreateCoursePopup open={open} onOpen={setOpen} />
      </div>
    </AppHeader>
  );
};

export default Header;
