import { Skeleton } from '@heroui/react';
import classNames from 'classnames';
import { map, range } from 'lodash';

const HistoryWordSkeleton = () => {
  return (
    <>
      {map(range(1, 20), (index) => (
        <tr className="border-b border-bg-box h-[43px]" key={index}>
          <td className={'py-[12px] pl-[30px] '}>
            <div className={'flex items-center gap-x-3'}>
              <Skeleton className="rounded-sm h-4 w-4" />
              <Skeleton
                className={classNames('rounded-sm w-3/5 h-3', {
                  'w-4/5': index % 2 === 0,
                })}
              />
            </div>
          </td>
          <td className={'text-color-minor'}>
            <Skeleton
              className={classNames('rounded-sm w-3/5 h-3', {
                'w-4/5': index % 2 === 0,
              })}
            />
          </td>
          <td className={'text-color-minor pr-[30px] flex justify-end py-[10px]'}>
            <Skeleton className="rounded-sm w-20 h-3" />
          </td>
        </tr>
      ))}
    </>
  );
};
export default HistoryWordSkeleton;
