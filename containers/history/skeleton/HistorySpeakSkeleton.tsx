import { Skeleton } from '@heroui/react';
import classNames from 'classnames';
import { map, range } from 'lodash';

const HistorySpeakSkeleton = () => {
  return (
    <>
      {map(range(1, 20), (index) => (
        <tr className="border-b border-bg-box h-[43px]" key={index}>
          <td className={'py-[12px] pl-[30px] '}>
            <div className={'flex items-center gap-x-3'}>
              <Skeleton className="rounded-sm h-4 w-4" />
              <Skeleton
                className={classNames('rounded-sm w-3/5 h-3', {
                  'w-4/5': index % 2 === 0,
                })}
              />
            </div>
          </td>
          <td className={'text-color-minor'}>
            <Skeleton
              className={classNames('rounded-sm w-3/5 h-3', {
                'w-4/5': index % 2 === 0,
              })}
            />
          </td>
        </tr>
      ))}
    </>
  );
};
export default HistorySpeakSkeleton;
