'use client';

import React, { useCallback } from 'react';

import Link from 'next/link';

import TranslationsSentence from '@/containers/history/Translation/TranslationsSentence';
import HistorySentenceSkeleton from '@/containers/history/skeleton/HistorySentenceSkeleton';
import { useAppContext } from '@/store/contexts/AppContext';
import ScrollArea from 'components/ScrollArea';
import useSentences from 'hooks/Ent/useSentences';
import { map } from 'lodash';
import { useLocale, useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';
import useSentenceStore from 'store/sentence';
import { AppContextProps } from 'types/theme';
import { formatDateDay } from 'utils/datetime';

import NoContent from '@/components/NoContent';

const Sentence = ({ historyTab }: { historyTab: string }) => {
  const locale = useLocale();
  const { sentenceList, isLoading, page, setPage, isReachingEnd } = useSentences();
  const t = useTranslations();
  const { setSelectedSentence } = useSentenceStore();
  const { setPanel }: AppContextProps = useAppContext();

  const TranslationCallBack = useCallback(() => {
    return <TranslationsSentence />;
  }, []);
  const handleChangeSentence = (sentence) => {
    setSelectedSentence(sentence);
    // @ts-ignore
    setPanel(TranslationCallBack);
  };
  if (!isLoading && !sentenceList.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
            <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
              {t('history.sentence.title')}
            </th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={2}>
              <NoContent />
            </td>
          </tr>
        </tbody>
      </table>
    );
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 77px)'}
        key={historyTab}
        dataLength={sentenceList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
                {t('history.sentence.title')}
              </th>
              <th className="w-[130px]"></th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {sentenceList.length
              ? map(sentenceList, (sen, key) => (
                  <tr
                    key={`tr-${key}`}
                    onClick={() => handleChangeSentence(sen.sentence)}
                    className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box [&>td>div]:hidden [&>td>div]:hover:flex [&>td>button]:hidden [&>td>button]:hover:flex"
                  >
                    <td key={`td-1-${key}`} className={'py-[12px] pl-[30px] flex items-center'}>
                      <Link className="flex gap-x-2" href="#">
                        <div className="pt-[3px]">
                          <i className={'icon-sentence text-[16px]'} />
                        </div>{' '}
                        <div
                          dangerouslySetInnerHTML={{ __html: sen?.sentence?.content || '' }}
                        ></div>
                      </Link>
                    </td>
                    <td key={`td-3-${key}`} className={'text-color-minor pr-[30px] text-right'}>
                      {sen.updated_at ? formatDateDay(sen.updated_at, locale) : ''}
                    </td>
                  </tr>
                ))
              : null}
            {isLoading ? <HistorySentenceSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default Sentence;
