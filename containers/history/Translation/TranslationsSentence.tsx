'use client';

import React, { useEffect, useState } from 'react';

import TranslationsSkeleton from '@/containers/history/Translation/skeleton/TranslationsSkeleton';
import WavePlayer from 'components/Audio/WavePlayer';
import Image from 'components/Image';
import ScrollArea from 'components/ScrollArea';
import SentencePhrases from 'containers/learn/Translation/SentencePhrases';
import SentencePoses from 'containers/learn/Translation/SentencePoses';
import useDictionary from 'hooks/Ent/useDictionary';
import { useTranslations } from 'next-intl';
import useSentenceStore from 'store/sentence';

const TranslationsSentence = () => {
  const t = useTranslations();
  const { selectedSentence, activeParagraph } = useSentenceStore();
  const [position, setPosition] = useState<number>(-1);
  const [fileUrl, setFileUrl] = useState<string>('');

  const { dictionary, sentenceDetails, sentencePhrases, isLoadingPos, isLoadingSentence } =
    useDictionary(selectedSentence?.id || 0, position);

  useEffect(() => {
    if (selectedSentence && selectedSentence.audios && selectedSentence.audios.length > 0) {
      setFileUrl(selectedSentence?.audios[0]?.url || '');
    }
  }, [selectedSentence]);
  return (
    <>
      {!selectedSentence ? (
        <div className={'py-6 pr-4'}>
          <Image
            src={activeParagraph?.image || ''}
            alt={activeParagraph?.title || ''}
            className={'mx-auto bg-bg-box border-none rounded-md w-full h-auto'}
          />

          <h5
            className={'text-black text-normal mt-2 mb-3 text-left font-[Inter]'}
            dangerouslySetInnerHTML={{ __html: activeParagraph?.description || '' }}
          ></h5>
        </div>
      ) : (
        <div className={'h-[calc(100vh_-_44px)]'}>
          <ScrollArea className="h-full pr-4 pt-4">
            {isLoadingSentence ? (
              <TranslationsSkeleton />
            ) : (
              <>
                <h5
                  className={'text-normal mb-4 text-color-major'}
                  dangerouslySetInnerHTML={{ __html: selectedSentence?.content || '' }}
                ></h5>
                {fileUrl !== '' && <WavePlayer url={fileUrl} />}

                <div className={'mt-0'}>
                  <h5 className={'text-color-minor text-normal'}>{t('sidebar.right.translate')}</h5>
                  <h5
                    className={'text-normal text-color-major'}
                    dangerouslySetInnerHTML={{
                      __html: selectedSentence.translation?.translate_google || '',
                    }}
                  ></h5>
                </div>
                {sentencePhrases && <SentencePhrases phrases={sentencePhrases} />}
                {sentenceDetails && (
                  <SentencePoses
                    isLoadingPos={isLoadingPos}
                    sentenceDetails={sentenceDetails}
                    position={position}
                    setPosition={setPosition}
                    dictionary={dictionary}
                  />
                )}
              </>
            )}
          </ScrollArea>
        </div>
      )}
    </>
  );
};
export default TranslationsSentence;
