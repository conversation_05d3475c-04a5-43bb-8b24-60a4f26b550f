'use client';

import React from 'react';

import TranslationWordsSkeleton from '@/containers/history/Translation/skeleton/TranslationWordsSkeleton';
import WavePlayer from 'components/Audio/WavePlayer';
import Image from 'components/Image';
import ScrollArea from 'components/ScrollArea';
import SentencePoses from 'containers/learn/Translation/SentencePoses';
import useDictionary from 'hooks/Ent/useDictionary';
import useWordStore from 'store/word';

interface TranslationWordsProps {
  position: number; // Kiểu dữ liệu của prop 'position'
  setPosition: (value: number) => void; // Kiểu dữ liệu của prop 'setPosition'
}
const TranslationWords: React.FC<TranslationWordsProps> = ({ position, setPosition }) => {
  const { selectedSentence, activeParagraph, activeSentencePos } = useWordStore();
  const { dictionary, sentenceDetails, isLoadingPos, isLoadingSentence } = useDictionary(
    selectedSentence?.id || 0,
    position
  );
  return (
    <>
      {!selectedSentence ? (
        <div className={'py-6 pr-4'}>
          <Image
            src={activeParagraph?.image || ''}
            alt={activeParagraph?.title || ''}
            className={'mx-auto bg-bg-box border-none rounded-md w-full h-auto'}
          />

          <h5
            className={'text-black text-normal mt-2 mb-3 text-left font-[Inter]'}
            dangerouslySetInnerHTML={{ __html: activeParagraph?.description || '' }}
          ></h5>
        </div>
      ) : (
        <div className={'h-[calc(100vh_-_44px)]'}>
          <ScrollArea className="h-full pr-4 pt-4">
            {isLoadingSentence || isLoadingPos ? (
              <TranslationWordsSkeleton />
            ) : (
              <>
                <h5
                  className={'text-normal mb-4 text-color-major text-4xl'}
                  dangerouslySetInnerHTML={{ __html: activeSentencePos?.word || '' }}
                ></h5>
                <WavePlayer
                  // @ts-ignore
                  url={dictionary?.audio ?? ''}
                />

                {activeSentencePos && (
                  <SentencePoses
                    isLoadingPos={isLoadingPos}
                    sentenceDetails={sentenceDetails}
                    position={position}
                    setPosition={setPosition}
                    dictionary={dictionary}
                  />
                )}
              </>
            )}
          </ScrollArea>
        </div>
      )}
    </>
  );
};
export default TranslationWords;
