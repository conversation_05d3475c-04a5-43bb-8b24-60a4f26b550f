import { Skeleton } from '@heroui/react';

const TranslationWordsSkeleton = () => {
  return (
    <>
      <div className="flex gap-4 mt-4 flex-col">
        <Skeleton className="rounded-sm w-2/5 h-4" />
        <Skeleton className="rounded-sm w-full h-10" />
        <Skeleton className="rounded-sm w-1/5 h-3" />
        <div className={'flex gap-2'}>
          <Skeleton className="rounded-sm w-5 h-3" />
          <Skeleton className="rounded-sm w-6 h-3" />
          <Skeleton className="rounded-sm w-5 h-3" />
          <Skeleton className="rounded-sm w-7 h-3" />
          <Skeleton className="rounded-sm w-5 h-3" />
          <Skeleton className="rounded-sm w-4 h-3" />
          <Skeleton className="rounded-sm w-7 h-3" />
          <Skeleton className="rounded-sm w-5 h-3" />
          <Skeleton className="rounded-sm w-4 h-3" />
          <Skeleton className="rounded-sm w-7 h-3" />
          <Skeleton className="rounded-sm w-5 h-3" />
          <Skeleton className="rounded-sm w-4 h-3" />
        </div>
        <Skeleton className="rounded-sm w-full h-40" />
      </div>
    </>
  );
};
export default TranslationWordsSkeleton;
