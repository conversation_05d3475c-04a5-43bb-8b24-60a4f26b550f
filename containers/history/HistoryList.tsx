'use client';

import React, { useEffect, useState } from 'react';

import { useParams, usePathname, useRouter } from 'next/navigation';

import Tabs from 'components/Tabs';
import { PARAGRAPH, SENTENCE, WORD } from 'configs';
import Paragraph from 'containers/history/Paragraph';
import Sentence from 'containers/history/Sentence';
import Word from 'containers/history/Word';
import { useTranslations } from 'next-intl';
import useSearchStore from 'store/search';

const HistoryList = () => {
  const params = useParams();
  const router = useRouter();
  const pathname = usePathname();
  const [activeHistoryTab, setActiveTab] = useState(WORD);
  const { setType, setKeyword } = useSearchStore();
  const { keyword, item } = params;
  const t = useTranslations();
  useEffect(() => {
    setKeyword(keyword?.toString() || '');
    setType(item?.toString() || '');
  }, [keyword, item]);

  const tabs = [
    {
      id: 10,
      title: t('history.tab.word'),
      keyx: WORD,
    },
    {
      id: 11,
      title: t('history.tab.sentence'),
      keyx: SENTENCE,
    },
    {
      id: 12,
      title: t('history.tab.paragraph'),
      keyx: PARAGRAPH,
    },
  ];

  useEffect(() => {
    // Đọc tab từ URL khi component mount và khi URL thay đổi
    const tabFromUrl = pathname.split('/').pop();
    if (tabFromUrl && tabs.some((tab) => tab.keyx === tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [pathname]);

  const handleChangeTab = (newActiveTab: string) => {
    setActiveTab(newActiveTab);
    setType(newActiveTab);
    // Cập nhật URL khi tab thay đổi
    router.push(`/history/${newActiveTab}`);
  };

  return (
    <>
      <div className={'flex items-center'}>
        <div className="pb-[10px]">
          {tabs.length > 0 && (
            <Tabs
              tabs={tabs}
              className={'ml-[30px]'}
              value={'keyx'}
              activeTabId={activeHistoryTab}
              onClick={(newActiveTab) => handleChangeTab(newActiveTab)}
            />
          )}
        </div>
      </div>
      <div>
        {activeHistoryTab === WORD && <Word historyTab={activeHistoryTab} />}
        {activeHistoryTab === SENTENCE && <Sentence historyTab={activeHistoryTab} />}
        {activeHistoryTab === PARAGRAPH && <Paragraph />}
      </div>
    </>
  );
};
export default HistoryList;
