import { Skeleton } from '@heroui/react';

const TranslationsSkeleton = () => {
  return (
    <>
      <div className="flex gap-4 mt-4 flex-col">
        <Skeleton className="rounded-sm w-3/5 h-4" />
        <Skeleton className="rounded-sm w-full h-3" />

        <Skeleton className="rounded-sm w-1/5 h-3" />
        <Skeleton className="rounded-sm w-full h-3" />
        <Skeleton className="rounded-sm w-4/5 h-3" />

        <Skeleton className="rounded-sm w-3/5 h-3" />
        <Skeleton className="rounded-sm w-full h-3" />

        <Skeleton className="rounded-sm w-4/5 h-3" />
        <Skeleton className="rounded-sm w-full h-3" />

        <Skeleton className="rounded-sm w-3/5 h-3" />
        <Skeleton className="rounded-sm w-4/5 h-3" />
      </div>
    </>
  );
};
export default TranslationsSkeleton;
