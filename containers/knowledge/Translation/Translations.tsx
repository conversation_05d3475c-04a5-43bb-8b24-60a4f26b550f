'use client';

import React, { ReactElement } from 'react';

import ScrollArea from 'components/ScrollArea';
import { map } from 'lodash';

const Translations = ({ knowledge }) => {
  const boldWordInSentence = (sentence: string, wordToBold: string) => {
    const regex = new RegExp(`(?<!\\w)${wordToBold}(?!\\w)`, 'gi');
    const parts = sentence.split(regex);

    if (parts.length <= 1) {
      // Trả về câu không thay đổi nếu từ không được tìm thấy
      return sentence;
    }

    const result: (string | ReactElement)[] = [];
    for (let i = 0; i < parts.length; i++) {
      result.push(parts[i]);
      if (i < parts.length - 1) {
        result.push(
          <span key={i} className="font-black">
            {wordToBold}
          </span>
        );
      }
    }

    return <>{result}</>;
  };

  return (
    <>
      {!knowledge ? (
        <div className={'py-6 pr-4'}></div>
      ) : (
        <div className={'h-[calc(100vh_-_44px)]'}>
          <ScrollArea className="h-full pr-4 pt-4">
            <h5 className={'text-normal mb-4 text-color-major text-[24px]'}>{knowledge?.title}</h5>

            <div className={'mt-0'}>
              <h5 className={'text-normal mb-[10px]'}>{knowledge?.definition_vi}</h5>
            </div>
            <div className="text-[13px] text-color-minor">Example</div>
            <div className={' mb-0.5 mr-[15px]'}>
              {knowledge?.knowledge_examples &&
                map(knowledge.knowledge_examples, (example, index) => (
                  <>
                    <div key={index} className="text-[14px] mb-[5px]">
                      {boldWordInSentence(example.content, example.keyword)}
                    </div>
                    <div key={index + 'vi'} className="italic text-[14px] mb-[20px]">
                      {boldWordInSentence(example.content_vi, example.keyword_vi)}
                    </div>
                  </>
                ))}
            </div>
          </ScrollArea>
        </div>
      )}
    </>
  );
};
export default Translations;
