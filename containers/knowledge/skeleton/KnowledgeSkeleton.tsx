import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const KnowledgeSkeleton = () => {
  return (
    <>
      {map(range(1, 30), (index) => (
        <tr className="border-b border-bg-box h-[43px] items-center" key={index}>
          <td className={'p-2 pl-[30px] h-[42px] '}>
            <div className="flex items-center w-full gap-4">
              <Skeleton className="rounded-sm h-3 w-3" />
              <Skeleton className={`rounded-sm ${index % 2 ? 'w-2/5' : 'w-3/5'} h-3`} />
              <Skeleton className="rounded-sm w-3 h-3" />
            </div>
          </td>
        </tr>
      ))}
    </>
  );
};
export default KnowledgeSkeleton;
