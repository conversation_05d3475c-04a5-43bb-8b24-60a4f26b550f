'use client';

import React, { useCallback } from 'react';

import Link from 'next/link';

import KnowledgeSkeleton from '@/containers/knowledge/skeleton/KnowledgeSkeleton';
import { useAppContext } from '@/store/contexts/AppContext';
import { KnowledgeEntity } from '@/types/model';
import classNames from 'classnames';
import ScrollArea from 'components/ScrollArea';
import Translations from 'containers/knowledge/Translation/Translations';
import useKnowledge from 'hooks/Ent/useKnowledge';
import { map } from 'lodash';
import InfiniteScroll from 'react-infinite-scroll-component';
import useKnowledgeStore from 'store/knowledge';
import { AppContextProps } from 'types/theme';

import NoContent from '@/components/NoContent';

const KnowledgeContainer = () => {
  const { knowledge, isLoading } = useKnowledge();
  const { setKnowledge } = useKnowledgeStore();
  const { setPanel }: AppContextProps = useAppContext();
  const TranslationCallBack = useCallback((knowledgechild) => {
    return <Translations knowledge={knowledgechild} />;
  }, []);

  const handleChangeSentence = (event, knowledgechild, type) => {
    event.preventDefault();

    if (type !== 2) {
      setKnowledge(knowledgechild);
      setPanel(TranslationCallBack(knowledgechild));
    }
  };

  const TableRow = ({
    item,
    position,
  }: {
    item: KnowledgeEntity['children'];
    position: number;
  }) => {
    const mr = position * 30;

    return (
      <>
        {item &&
          item.map((child) => (
            <React.Fragment key={child.id}>
              <tr
                className={classNames('bg-bg-general hover:bg-bg-box/60 border-b border-bg-box', {
                  'text-color-minor': child.type === 2,
                  // 'hidden': position > 3 && !openRows[parentId] // Ẩn TableRow nếu position > 3 và TableRow cha chưa được mở
                })}
                onClick={(event) => handleChangeSentence(event, child, child.type)} // Truyền id của TableRow vào handleChangeSentence
              >
                <td
                  className={`py-2 pl-[${mr}px] h-[42px] flex items-center`}
                  style={{ paddingLeft: `${mr}px` }}
                >
                  <Link className="flex items-center gap-x-2" href="">
                    {child?.title}
                  </Link>
                </td>
              </tr>
              {child.children && <TableRow position={position + 1} item={child.children} />}
            </React.Fragment>
          ))}
      </>
    );
  };

  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll dataLength={1} next={() => 1} hasMore={false} loader={null}>
        {!knowledge?.length && !isLoading ? <NoContent /> : null}
        <table className="table-auto w-full">
          <tbody className={'text-[0.8123rem]'}>
            {knowledge &&
              map(knowledge, (know) => (
                <React.Fragment key={know.id}>
                  <tr
                    onClick={(event) => handleChangeSentence(event, know, know.type)}
                    className="text-color-minor bg-bg-box border-b border-bg-box "
                  >
                    <td className={'p-2 pl-[30px] h-[32px] flex items-center'}>
                      <div className="flex items-center gap-x-2">{know?.title}</div>
                    </td>
                  </tr>
                  <TableRow key={know.id} position={2} item={know.children} />
                </React.Fragment>
              ))}
            {isLoading ? <KnowledgeSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default KnowledgeContainer;
