'use client';

import React, { useCallback, useState } from 'react';

import { useParams } from 'next/navigation';

import classNames from 'classnames';
import Button from 'components/Button';
import GroupAddPopup from 'components/ServicePopup/GroupAddPopup';
import { ROLE_APPROVE } from 'configs';
import AppHeader from 'containers/layout/AppHeader';
import { useTranslations } from 'next-intl';

import { useSession } from '@/hooks/useSession';

interface HeaderProps {
  mutate?: (() => void) | null;
}

interface ModalData {
  title: string;
  type: number;
}

const Header: React.FC<HeaderProps> = ({ mutate }) => {
  const { data: sessionData } = useSession();
  const params = useParams();
  const parent_id = params.id?.toString() || '';
  const tabActivated = params.tabs?.toString() || '';
  const [open, setOpen] = useState(false);
  const [modalData, setModalData] = useState<ModalData>({ title: '', type: 1 });
  const t = useTranslations();

  const handleClose = useCallback(() => {
    setOpen(false);
    mutate?.();
  }, [mutate]);

  const handleOpen = useCallback((type: number, modalTitle: string) => {
    setModalData({ type, title: modalTitle });
    setOpen(true);
  }, []);

  const isApprover =
    sessionData?.user.role_id === ROLE_APPROVE || sessionData?.user.type === ROLE_APPROVE;
  const showAddClass = sessionData && !parent_id && !tabActivated && isApprover;

  return (
    <AppHeader bottom={'0px'}>
      <div className={'h-[43px] px-[30px] w-full grid grid-cols-12 items-center'}>
        <div className={'col-span-6 flex items-center'}>
          <span className={classNames('flex gap-x-2')}>
            <i className={'icon-group text-[16px]'} />
            {t('group.title')}
          </span>
        </div>
        <div className="col-span-6 flex flex-grow-0 items-center justify-end">
          {!parent_id && sessionData?.user.role_id === ROLE_APPROVE && (
            <Button
              onClick={() => handleOpen(1, t('group.add'))}
              size="xs"
              variant="bordered"
              color="default"
              className="float-end px-2"
            >
              <i className={classNames('icon-add text-normal')} />
              {t('group.button.addOrganization')}
            </Button>
          )}

          {showAddClass && (
            <Button
              onClick={() => handleOpen(2, t('group.button.addClass'))}
              size="xs"
              variant="bordered"
              color="default"
              className="float-end px-2 ml-2.5"
            >
              <i className={classNames('icon-add text-normal')} />
              {t('group.button.addClass')}
            </Button>
          )}
        </div>
      </div>
      <GroupAddPopup
        title={modalData.title}
        type={modalData.type}
        groupId={parseInt(parent_id)}
        open={open}
        onOpen={setOpen}
        onClose={handleClose}
      />
    </AppHeader>
  );
};

export default Header;
