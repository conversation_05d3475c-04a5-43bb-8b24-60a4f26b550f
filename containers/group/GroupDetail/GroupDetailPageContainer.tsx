'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { useParams, usePathname, useRouter } from 'next/navigation';

import GroupTypeEnum from '@/configs/GroupTypeEnum';
import ListItemInGroup from '@/containers/group/ListItemInGroup';
import classNames from 'classnames';
import Button from 'components/Button';
import GroupAddPopup from 'components/ServicePopup/GroupAddPopup';
import ManagerAddPopup from 'components/ServicePopup/ManagerAddPopup';
import Tabs from 'components/Tabs';
import { ROLE_APPROVE } from 'configs';
import Header from 'containers/group/GroupDetail/Header';
import ManagerList from 'containers/group/GroupDetail/ManagerList';
import { GroupReportContainer, ReportHeader } from 'containers/group/GroupDetail/reports';
import { useTranslations } from 'next-intl';

import useGroupMember from '@/hooks/Ent/useGroupMember';
import { useSession } from '@/hooks/useSession';

type Tab = {
  id: number;
  keyx: string;
  title: string;
};

type ModalData = {
  title: string;
  type: number;
};

/**
 * Custom hook for managing tabs in the Group Detail page
 */
const useGroupTabs = (sessionData: any): Tab[] => {
  const t = useTranslations();

  return useMemo(() => {
    const baseTabs: Tab[] = [
      { id: 1, keyx: 'dashboard', title: t('group.tab.dashboard') },
      { id: 2, keyx: 'class', title: t('group.tab.class') },
    ];

    // Only add manager tab if user session exists
    if (sessionData?.user && Object.keys(sessionData.user).length > 0) {
      baseTabs.push({ id: 3, keyx: 'manager', title: t('group.tab.manager') });
    }

    return baseTabs;
  }, [sessionData?.user?.id, t]); // Only depend on user.id instead of entire sessionData
};

/**
 * Custom hook for managing the active tab state and URL synchronization
 */
const useTabNavigation = (tabs: Tab[], groupId: number) => {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const [activeGroupTab, setActiveTab] = React.useState<string>('dashboard');

  useEffect(() => {
    // Read tab from URL when component mounts and when URL changes
    const tabFromUrl = pathname.split('/').pop();
    if (tabFromUrl && tabs.some((tab) => tab.keyx === tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [params, pathname, tabs]);

  const handleChangeTab = (newActiveTab: string | number | boolean) => {
    setActiveTab(String(newActiveTab));
    // Update URL when tab changes
    router.push(`/group/${groupId}/${newActiveTab}`);
  };

  return { activeGroupTab, handleChangeTab };
};

/**
 * Custom hook for managing modals (popups)
 */
const useModal = (mutateFn?: () => void) => {
  const [isOpen, setIsOpen] = React.useState<boolean>(false);
  const [modalData, setModalData] = React.useState<ModalData>({ title: '', type: 1 });

  // const handleClose = () => setIsOpen(false);
  const handleClose = useCallback(() => {
    setIsOpen(false);
    mutateFn?.();
  }, [mutateFn]);

  // const handleClose = () => {
  //   setIsOpen(false);
  //   mutateFn?.();
  // };
  const handleOpen = (type: number, title: string) => {
    setModalData({ type, title });
    setIsOpen(true);
  };

  return {
    isOpen,
    modalData,
    handleOpen,
    handleClose,
    setIsOpen,
  };
};

interface TabContentProps {
  activeTab: string;
  groupId: number;
  setMutateFn?: (fn: () => void) => void;
}

/**
 * Tab content component that renders the appropriate content based on active tab
 */
const TabContent: React.FC<TabContentProps> = ({ activeTab, groupId, setMutateFn }) => {
  switch (activeTab) {
    case 'dashboard':
      return <GroupReportContainer groupId={groupId} />;
    case 'class':
      return <ListItemInGroup group_id={groupId} setMutateFn={setMutateFn} />;
    case 'manager':
      return <ManagerList group_id={groupId} />;
    default:
      return null;
  }
};

interface TabActionsProps {
  activeTab: string;
  groupId: number;
  onOpenManager: (type: number, title: string) => void;
  onOpenClass: (type: number, title: string) => void;
}

/**
 * Tab actions component that displays action buttons based on the active tab
 */
const TabActions: React.FC<TabActionsProps> = ({
  activeTab,
  groupId,
  onOpenManager,
  onOpenClass,
}) => {
  const t = useTranslations();
  const { data: sessionData } = useSession();

  if (activeTab === 'dashboard') {
    return <ReportHeader groupId={groupId} />;
  }

  if (activeTab === 'manager') {
    return (
      <Button
        onClick={() => onOpenManager(2, t('group.button.addManager'))}
        size={'xs'}
        variant={'bordered'}
        color={'default'}
        className={'float-end px-2 ml-2.5'}
      >
        <i className={classNames('icon-add text-normal')} /> {t('group.button.addManager')}
      </Button>
    );
  }

  if (activeTab === 'class' && sessionData) {
    const canAddClass =
      sessionData.user.role_id === ROLE_APPROVE ||
      sessionData.user.type === ROLE_APPROVE ||
      sessionData.user.role_id === 8 ||
      sessionData.user.role_id === 9;

    if (canAddClass) {
      return (
        <Button
          onClick={() => onOpenClass(2, t('group.button.addClass'))}
          size="xs"
          variant="bordered"
          color="default"
          className="float-end px-2 ml-2.5"
        >
          <i className={classNames('icon-add text-normal')} /> {t('group.button.addClass')}
        </Button>
      );
    }
  }

  return null;
};

interface GroupDetailPageContainerProps {
  group_id: number;
}

/**
 * Main component for the Group Detail page container
 */
const GroupDetailPageContainer: React.FC<GroupDetailPageContainerProps> = ({ group_id }) => {
  const [mutateFn, setMutateFn] = useState<(() => void) | null>(null);
  const { data: sessionData } = useSession();
  const router = useRouter();
  const { group } = useGroupMember(group_id);
  const [breadcrumbs, setBreadcrumbs] = React.useState<string[]>([]);

  // Custom hooks
  const tabs = useGroupTabs(sessionData);
  const { activeGroupTab, handleChangeTab } = useTabNavigation(tabs, group_id);
  const managerModal = useModal();
  const classModal = useModal(mutateFn ?? undefined);

  // Update breadcrumbs when group or active tab changes
  useEffect(() => {
    if (!group) return;

    if (group.type !== GroupTypeEnum.GROUP) {
      console.log(group);
      router.push('/404');
      return;
    }

    const tab = tabs.find((item) => item.keyx === activeGroupTab);
    setBreadcrumbs([group.title, tab?.title ?? '']);
  }, [group, activeGroupTab, tabs, router]);

  if (!group_id) return null;

  return (
    <>
      <Header breadcrumbs={breadcrumbs} group_id={group_id} mutate={mutateFn} />

      {group_id > 0 && tabs.length > 0 && (
        <div>
          <div className="flex items-center justify-between pr-[30px] pl-[30px]">
            <div className="mb-[10px]">
              <Tabs
                tabs={tabs}
                // classNames={{
                //   'ml-[30px]'
                // }}
                value="keyx"
                activeTabId={activeGroupTab}
                onClick={(tabId) => handleChangeTab(tabId)}
              />
            </div>
            <div className="mb-[10px]">
              <TabActions
                activeTab={activeGroupTab}
                groupId={group_id}
                onOpenManager={managerModal.handleOpen}
                onOpenClass={classModal.handleOpen}
              />
            </div>
          </div>

          {activeGroupTab === 'dashboard' && (
            <div className="border-bg-box border-b-[10px] h-0 w-full" />
          )}
        </div>
      )}

      <TabContent activeTab={activeGroupTab} groupId={group_id} setMutateFn={setMutateFn} />

      <ManagerAddPopup
        title={managerModal.modalData.title}
        type={managerModal.modalData.type}
        open={managerModal.isOpen}
        groupId={group_id}
        onOpen={managerModal.setIsOpen}
        onClose={managerModal.handleClose}
      />

      <GroupAddPopup
        title={classModal.modalData.title}
        type={classModal.modalData.type}
        open={classModal.isOpen}
        groupId={group_id}
        onOpen={classModal.setIsOpen}
        onClose={classModal.handleClose}
      />
    </>
  );
};

export default GroupDetailPageContainer;
