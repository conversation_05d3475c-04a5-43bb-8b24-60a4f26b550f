'use client';

import React from 'react';

import ManagerListSkeleton from '@/containers/group/skeleton/ManagerListSkeleton';
import ScrollArea from 'components/ScrollArea';
import useManagers from 'hooks/Ent/useManager';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';

const ManagerList = ({ group_id }) => {
  const { managerList, page, setPage, isLoading, isReachingEnd } = useManagers(group_id);
  const t = useTranslations();
  if (!isLoading && !managerList.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={'bg-bg-box text-color-minor text-[13px] leading-[8px] hidden'}>
            <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{t('group.name')}</th>
            <th className={'py-1 text-left font-normal'}>{t('group.address')}</th>
            <th className={'py-1 text-right font-normal pr-[30px]'}>{t('group.user_manager')}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={3}>
              <div className={'flex h-72 justify-center w-full items-center flex-col'}>
                <i className={'w-20 h-20 text-5xl  icon-alert-line animate-bounce opacity-50'} />
                <span
                  className={'text-color-minor'}
                  dangerouslySetInnerHTML={{ __html: t('group.no_groups') }}
                />
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    );
  // @ts-ignore
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 85px)'}
        key="paragraph"
        dataLength={managerList.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>
                {t('group.common.manager')}
              </th>
              <th className={'py-1 text-left font-normal'}>{t('group.common.phone')}</th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {isLoading && managerList.length <= 0 && <ManagerListSkeleton />}
            {managerList &&
              map(managerList, (group, key) => (
                <tr
                  key={`tr-${key}`}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group [&>td>button]:hidden [&>td>button]:hover:flex"
                >
                  <td key={`td-1-${key}`} className={'p-2 h-[43px] pl-[30px] flex items-center'}>
                    <div className="flex gap-x-2">
                      <div className="pt-[3px]">
                        <i className={'text-[16px] icon-account'} />
                      </div>
                      {group?.account?.fullname}
                    </div>
                  </td>

                  <td key={`td-2-${key}`} className={'w-[60%]'}>
                    <div className="flex gap-x-2">{group?.account?.phone}</div>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default ManagerList;
