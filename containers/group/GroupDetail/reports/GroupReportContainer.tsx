'use client';

import React, { useEffect } from 'react';

import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import ClassWithMemberReport from '@/containers/group/report-item/ClassWithMemberReport';
import TokenReport from '@/containers/group/report-item/TokenReport';
import useReportStore from '@/store/report';
import ScrollArea from 'components/ScrollArea';
import { useTranslations } from 'next-intl';

const GroupReportContainer = ({ groupId }) => {
  const t = useTranslations();
  const { params, setParams } = useReportStore();
  useEffect(() => {
    setParams({
      ...params,
      item: ReportTypeEnum.ORGANIZATION,
      object_id: groupId,
    });

    return () => {
      const tooltipEl = document.getElementById('chartjs-tooltip');
      if (tooltipEl) {
        document.body.removeChild(tooltipEl);
      }
    };
  }, []);

  return (
    <ScrollArea
      className={'!h-[calc(100vh_-_33px)] relative w-full flex-1 bg-bg-general overflow-x-hidden'}
    >
      <div className="pt-[15px] pb-[30px] mb-24">
        <div className={'grid grid-cols-12 gap-4 px-[30px]'}>
          <TokenReport
            title={t('group.dashboard.label_chart_group_token')}
            label={t('group.dashboard.balance')}
          />
        </div>

        <div className={'grid grid-cols-12 gap-4 mt-10 px-[30px]'}>
          <ClassWithMemberReport title={t('group.dashboard.label_chart_group_member_number')} />
        </div>
      </div>
    </ScrollArea>
  );
};
export default GroupReportContainer;
