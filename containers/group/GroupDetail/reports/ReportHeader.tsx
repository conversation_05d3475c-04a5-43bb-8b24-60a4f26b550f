'use client';

import React, { useEffect } from 'react';

import useReportStore from '@/store/report';
import { endOfMonth, startOfMonth } from 'date-fns';

import MonthYearSelector from '@/components/MonthYearSelector';

const ReportHeader = ({ groupId }) => {
  const { date, params, setDate, setParams } = useReportStore();
  useEffect(() => {
    const startMonthDate = startOfMonth(date);
    const endMonthDate = endOfMonth(date);

    const start_day = Math.floor(
      Date.UTC(startMonthDate.getFullYear(), startMonthDate.getMonth(), startMonthDate.getDate()) /
        1000
    );
    const end_day = Math.floor(
      Date.UTC(endMonthDate.getFullYear(), endMonthDate.getMonth(), endMonthDate.getDate()) / 1000
    );
    setParams({
      ...params,
      start_day: start_day,
      end_day: end_day,
      object_id: groupId,
    });
  }, [date]);

  return (
    <div className={'flex'}>
      <MonthYearSelector setDate={setDate} date={date} />
    </div>
  );
};
export default ReportHeader;
