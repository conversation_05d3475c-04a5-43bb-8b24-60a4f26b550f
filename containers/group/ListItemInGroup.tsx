'use client';

import React, { useEffect } from 'react';

import Link from 'next/link';

import GroupTypeEnum from '@/configs/GroupTypeEnum';
import GroupSkeleton from '@/containers/group/skeleton/GroupSkeleton';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import useGroup from 'hooks/Ent/useGroup';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';
import useHeaderStore from 'store/header';
import { typeStringToIconMap } from 'utils/common';

import NoContent from '@/components/NoContent';

const ListItemInGroup = ({
  group_id,
  setMutateFn,
}: {
  group_id: number;
  setMutateFn?: (fn: () => void) => void;
}) => {
  // const { favouriteAll } = useFavouriteAll();
  const { setTitle } = useHeaderStore();
  const { groupParent, groupsList, isLoading, page, setPage, isReachingEnd, mutate } =
    useGroup(group_id);

  const groupIds = groupsList.map((g) => g.id);
  const visibleGroups = groupsList.filter((group) => !groupIds.includes(group.parent_id));

  useEffect(() => {
    if (setMutateFn) {
      setMutateFn(() => mutate); // Truyền mutate lên component cha
    }
  }, [mutate, setMutateFn]);
  useEffect(() => {
    if (groupParent) {
      setTitle(groupParent.title);
    } else {
      setTitle(t('group.title'));
    }
  }, [groupParent]);
  const t = useTranslations();
  if (!isLoading && !visibleGroups.length)
    return (
      <table className="table-auto w-full">
        <thead>
          <tr className={'bg-bg-box text-color-minor text-[13px] leading-[8px] hidden'}>
            <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{t('group.name')}</th>
            <th className={'py-1 text-left font-normal'}>{t('group.address')}</th>
            <th className={'py-1 text-right font-normal pr-[30px]'}>{t('group.user_manager')}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td colSpan={3}>
              <NoContent title={t('group.no_groups')} />
            </td>
          </tr>
        </tbody>
      </table>
    );
  // @ts-ignore
  return (
    <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
      <InfiniteScroll
        height={'calc(100vh - 85px)'}
        key="paragraph"
        dataLength={visibleGroups.length}
        next={() => setPage(page + 1)}
        hasMore={!isReachingEnd}
        loader={null}
        className="scroll-area scrollbar-w-2 scrollbar-thumb-rounded-full scrollbar-track-rounded-full scrollbar scrollbar-thumb-color-line scrollbar-track-bg-general"
      >
        <table className="table-auto w-full">
          <thead className="sticky top-0 z-[1]">
            <tr key={'tr-head'} className={'bg-bg-box text-color-minor text-[13px] h-[32px]'}>
              <th className={'px-2 pl-[30px] py-1 text-left font-normal'}>{t('group.name')}</th>
              <th className={'py-1 text-left font-normal'}>{t('group.address')}</th>
              <th className={'py-1 text-right font-normal pr-[30px]'}>
                {t('group.common.manager')}
              </th>
            </tr>
          </thead>
          <tbody className={'text-[0.8123rem]'}>
            {visibleGroups &&
              map(visibleGroups, (group, key) => (
                <tr
                  key={`tr-${key}`}
                  className="bg-bg-general hover:bg-bg-box/60 border-b border-bg-box group [&>td>button]:hidden [&>td>button]:hover:flex"
                >
                  <td key={`td-1-${key}`} className={'p-2 h-[42px] pl-[30px] flex items-center'}>
                    <Link
                      className="flex gap-x-2"
                      href={`${
                        group.type == GroupTypeEnum.CLASS ? EntRouters.class : EntRouters.group
                      }/${group.id}`}
                    >
                      <div className="pt-[3px]">
                        <i
                          className={`text-[16px] ${
                            typeStringToIconMap[
                              group.type == GroupTypeEnum.GROUP ? 'workspace' : 'groups'
                            ]
                          }`}
                        />
                      </div>
                      {group?.title}
                    </Link>
                    {/* <FavouriteButton
                      favouriteList={favouriteAll}
                      item={EntRouters.group}
                      object_id={group.id}
                    /> */}
                  </td>

                  <td key={`td-2-${key}`} className={'w-[20%]'}>
                    <div className="flex gap-x-2">{group?.address}</div>
                  </td>
                  <td key={`td-3-${key}`} className={'w-[40%] pr-[30px] text-right'}>
                    {group?.account_groups?.[0]?.account?.fullname}
                  </td>
                </tr>
              ))}
            {isLoading ? <GroupSkeleton /> : null}
          </tbody>
        </table>
      </InfiniteScroll>
    </ScrollArea>
  );
};
export default ListItemInGroup;
