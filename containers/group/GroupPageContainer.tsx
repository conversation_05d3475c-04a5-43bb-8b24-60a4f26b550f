'use client';

import React, { useState } from 'react';

import ListItemInGroup from '@/containers/group/ListItemInGroup';
import Header from 'containers/group/Header';

const GroupPageContainer = () => {
  const [mutateFn, setMutateFn] = useState<(() => void) | null>(null);
  return (
    <>
      <Header mutate={mutateFn} />
      <ListItemInGroup group_id={-1} setMutateFn={setMutateFn} />
    </>
  );
};

export default GroupPageContainer;
