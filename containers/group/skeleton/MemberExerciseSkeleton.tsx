import React from 'react';

import { Skeleton } from '@heroui/react';
import { map, range } from 'lodash';

const MemberExerciseSkeleton = () => {
  return (
    <>
      {map(range(1, 25), (index) => (
        <tr className="border-b border-bg-box h-[43px] items-center" key={index}>
          <td className={'pr-2 pl-[30px] py-1 text-left font-normal'}>
            <div className="flex items-center w-full gap-4">
              <Skeleton className="rounded-sm h-3 w-3" />
              <Skeleton className={`rounded-sm ${index % 2 ? 'w-2/5' : 'w-3/5'} h-3`} />
            </div>
          </td>
          <td className={'py-1 text-left font-normal'}>
            <Skeleton className="rounded-sm h-3 w-3" />
          </td>
          <td className={'py-1 text-left font-normal'}>
            <Skeleton className="rounded-sm h-3 w-3" />
          </td>
          <td className={'py-1 text-left font-normal'}>
            <div className="flex items-center w-full gap-2">
              <Skeleton className="rounded-sm h-3 w-3" />
              <Skeleton className="rounded-sm h-3 w-3" />
              <Skeleton className="rounded-sm h-3 w-3" />
              <Skeleton className="rounded-sm h-3 w-3" />
              <Skeleton className="rounded-sm h-3 w-3" />
              <Skeleton className="rounded-sm h-3 w-3" />
            </div>
          </td>
          <td className={'py-1 text-left font-normal'}>
            <Skeleton className={`rounded-sm ${index % 2 ? 'w-2/5' : 'w-3/5'} h-3`} />
          </td>
          <td className={'py-1 text-left font-normal'}>
            <div className="flex items-center justify-center w-full gap-4">
              <Skeleton className="rounded-sm h-3 w-3" />
            </div>
          </td>
        </tr>
      ))}
    </>
  );
};
export default MemberExerciseSkeleton;
