'use client';

import React from 'react';

import CreateCoursePopup from 'components/ServicePopup/CreateCoursePopup';
import AppHeader from 'containers/layout/AppHeader';
import { useTranslations } from 'next-intl';

const Header: React.FunctionComponent = () => {
  const t = useTranslations();
  const [open, setOpen] = React.useState(false);
  return (
    <>
      <AppHeader>
        <div
          className={'h-[43px] px-[30px] w-full  items-center flex content-between align-baseline'}
        >
          <div className={'col-span-6 flex items-center'}>
            <span className="flex items-center gap-x-2">
              <i className={'icon-voiceprint-line text-[16px]'} />
              {t('pronounce.title')}
            </span>
          </div>

          <CreateCoursePopup open={open} onOpen={setOpen} />
        </div>
      </AppHeader>

      <div className={'border-b border-bg-box h-0 w-full'}></div>
    </>
  );
};

export default Header;
