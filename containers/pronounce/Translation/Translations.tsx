'use client';

import React, { useEffect, useState } from 'react';

import TranslationsSkeleton from '@/containers/pronounce/Translation/TranslationsSkeleton';
import WavePlayer from 'components/Audio/WavePlayer';
import usePronounce from 'hooks/Ent/usePronounce';
import { map } from 'lodash';
import usePronounceStore from 'store/pronounce';

const Translations = () => {
  const { selectedSentence, activePronounceId } = usePronounceStore();

  const { isLoadingSentence, pronounce } = usePronounce(activePronounceId || 0);
  const [fileUrl, setFileUrl] = useState<string>('');
  useEffect(() => {
    if (pronounce && pronounce.url?.length > 0) {
      setFileUrl(pronounce.url);
    }
  }, [pronounce]);

  const playAudio = (audioUrl: string) => {
    const audioElement = new Audio(audioUrl);
    // Phát âm thanh
    audioElement.play();
  };
  return (
    <>
      {!selectedSentence ? (
        <div className={'py-6 pr-4'}></div>
      ) : (
        <div className={'pt-4 pr-7 sticky top-0 h-full'}>
          {isLoadingSentence ? (
            <TranslationsSkeleton />
          ) : (
            <>
              <h5 className={'text-normal mb-4 text-color-major text-[64px]'}>{pronounce?.word}</h5>
              {fileUrl !== '' && <WavePlayer url={fileUrl} />}

              <div className={'mt-0'}>
                <h5 className={'text-normal mb-[10px]'}>{pronounce?.guide}</h5>
              </div>
              <div className={'flex flex-wrap mb-0.5 mr-[15px]'}>
                {pronounce?.phonetics &&
                  map(pronounce.phonetics, (phonetic, key) => (
                    <div
                      onClick={() => playAudio(phonetic?.audio)}
                      key={key}
                      className={
                        'cursor-pointer px-[15px] py-[10px] rounded bg-bg-box font-normal text-[13px] text-center justify-center mr-5 mb-[20px]'
                      }
                    >
                      <div className="font-bold">{phonetic?.word}</div>
                      <div>/{phonetic?.text}/</div>
                    </div>
                  ))}
              </div>
            </>
          )}
        </div>
      )}
    </>
  );
};
export default Translations;
