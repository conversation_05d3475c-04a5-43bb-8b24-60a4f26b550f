'use client';

import React, { useCallback } from 'react';

import Link from 'next/link';

import { useAppContext } from '@/store/contexts/AppContext';
import Translations from 'containers/pronounce/Translation/Translations';
import usePronounceStore from 'store/pronounce';
import { AppContextProps } from 'types/theme';

const PronounceContainer = () => {
  const { setSelectedSentence, setActivePronounceId } = usePronounceStore();
  const { setPanel }: AppContextProps = useAppContext();
  const TranslationCallBack = useCallback(() => {
    return <Translations />;
  }, []);
  const handleOpenPronounce = (id) => {
    setSelectedSentence(id);
    setActivePronounceId(id);
    // @ts-ignore
    setPanel(TranslationCallBack);
  };

  return (
    <>
      <div
        className={
          'bg-bg-box text-color-minor leading-[24px] text-[13px] h-[32px] px-2 pl-[30px] py-1 text-left font-normal'
        }
      >
        <PERSON>uyên âm
      </div>
      <div className={'p-[30px] flex'}>
        <div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(1)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              iː
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(2)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ɪ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(7)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ʊ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(8)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              uː
            </Link>
          </div>
          <div className={'flex mb-0.5'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(3)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              e
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(4)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ə
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(9)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ɛː
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(10)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ɔː
            </Link>
          </div>
          <div className={'flex mb-0.5'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(5)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              æ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(6)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ʌ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(11)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ɑː
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(12)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ɒ
            </Link>
          </div>
        </div>
        <div>
          <div className={'flex mb-0.5'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(13)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ɪə
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(14)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              eɪ
            </Link>
          </div>
          <div className={'flex mb-0.5'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(15)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ʊə
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(16)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ɔɪ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(17)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              əʊ
            </Link>
          </div>
          <div className={'flex mb-0.5'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(18)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              eə
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(19)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              aɪ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(20)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              aʊ
            </Link>
          </div>
        </div>
      </div>
      <div
        className={
          'leading-[24px] bg-bg-box text-color-minor text-[13px] h-[32px] px-2 pl-[30px] py-1 text-left font-normal'
        }
      >
        Phụ âm
      </div>
      <div className={'p-[30px] flex'}>
        <div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(21)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              p
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(22)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              b
            </Link>
          </div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(29)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              f
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(30)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              v
            </Link>
          </div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(37)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              m
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(38)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              n
            </Link>
          </div>
        </div>
        <div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(23)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              t
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(24)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              d
            </Link>
          </div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(31)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              θ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(32)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ð
            </Link>
          </div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(39)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ŋ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(40)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              h
            </Link>
          </div>
        </div>
        <div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(25)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              tʃ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(26)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              dʒ
            </Link>
          </div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(33)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              s
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(34)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              z
            </Link>
          </div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(41)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              l
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(42)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              r
            </Link>
          </div>
        </div>
        <div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(27)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              k
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(28)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ɡ
            </Link>
          </div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(35)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ʃ
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(36)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              ʒ
            </Link>
          </div>
          <div className={'flex mb-0.5 mr-[15px]'}>
            <Link
              href=""
              onClick={() => handleOpenPronounce(43)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              w
            </Link>
            <Link
              href=""
              onClick={() => handleOpenPronounce(44)}
              className="pointer-events-auto  w-[50px] h-[50px] rounded bg-bg-box font-normal text-[24px] flex items-center justify-center mr-0.5"
            >
              j
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

// export default HomePage;

export default PronounceContainer;
