FROM node:22.14.0-alpine AS depend
WORKDIR /fruit/longan
RUN apk update && apk add --no-cache tzdata bash jq gettext util-linux curl
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
COPY package.json ./
COPY yarn.lock* pnpm-lock.lock* ./
RUN pnpm cache clean && pnpm install

FROM node:22.14.0-alpine AS builder
WORKDIR /fruit/longan
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
COPY . .
COPY --from=depend /fruit/longan/node_modules ./node_modules
RUN pnpm run build \
    && rm -rf node_modules \
    && pnpm install --prod  --ignore-scripts

FROM node:22.14.0-alpine AS runner
WORKDIR /fruit/longan
ENV NODE_ENV=production
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
EXPOSE 3000

COPY --from=builder /fruit/longan/node_modules ./node_modules
COPY --from=builder /fruit/longan/.next ./.next
COPY --from=builder /fruit/longan/package.json ./package.json
COPY . .


RUN echo "start application with ENV=$NODE_ENV"
CMD ["pnpm", "run", "start"]
