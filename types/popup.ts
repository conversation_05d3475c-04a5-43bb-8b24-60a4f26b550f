export type ErrorModalProps = {
  opened: boolean;
  message?: string;
  title?: string;
};

export interface IPopupStore {
  openedExpiredSession: boolean;
  openedErrorModal: boolean;
  errorMessage: string;
  title: string;

  setExpiredSessionModal: (open: boolean) => void;
  setErrorModal: ({ opened, message, title }: ErrorModalProps) => void;
}

export type GroupAddPopupProps = {
  title: string;
  groupId: number;
  open: boolean;
  type?: number;
  onOpen: (open: boolean) => void;
  onClose?: (params: any) => void;
  mutate?: () => void;
  onDataChange?: (data: any) => void;
};

export type CreateCoursePopupProps = {
  open: boolean;
  onOpen: (open: boolean) => void;
  onClose?: (params: any) => void;
};

export type MemberSetPassPopupPops = {
  open: boolean;
  onOpen: (open: boolean) => void;
  id: number;
};
