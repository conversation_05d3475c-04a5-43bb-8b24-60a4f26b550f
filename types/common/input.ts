import { ChangeEvent, ClipboardEvent, FocusEvent, KeyboardEvent, ReactNode } from 'react';

import { StaticImageData } from 'next/image';

export type InputMode =
  | 'search'
  | 'none'
  | 'text'
  | 'tel'
  | 'url'
  | 'email'
  | 'numeric'
  | 'decimal'
  | undefined;

export enum InputType {
  NORMAL = 'normal',
  LABEL = 'label',
}

export interface InputProps {
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  onPaste?: (e: ClipboardEvent<HTMLInputElement>) => void;
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (e: KeyboardEvent<HTMLInputElement>) => void;
  onRightIconClick?: () => void;
  onClick?: (val?: any) => void;
  className?: string;
  requiredClassName?: string;
  rightIconClass?: string;
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
  rows?: number;
  disabled?: boolean;
  value?: string;
  defaultValue?: ReactNode;
  label?: string;
  placeholder?: string;
  name?: string;
  type?: string;
  pattern?: string;
  errorMessage?: string;
  leftIcon?: ReactNode;
  inputMode?: InputMode;
  maxLength?: number;
  showIcon?: string;
  hideIcon?: string;
  rightIcon?: string | StaticImageData;
  errorIcon?: string | StaticImageData;
  readOnly?: boolean;
  required?: boolean;
  inputType?: InputType;
  autoFocus?: boolean;
  activeWhenFocus?: boolean;
}
