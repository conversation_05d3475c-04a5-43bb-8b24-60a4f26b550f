import React, { ReactNode } from 'react';

export type ThemeColor = {
  keyx: string;
  status: number;
  parent_id: number | null;
  icon: string | null;
  valuex: string;
};

export type ThemeItem = {
  id: number;
  keyx: string;
  status: number;
  parent_id: number | null;
  icon: string | null;
  title: string;
  items: ThemeColor[];
};
export type FontSizeList = {
  id: number;
  keyx: string;
  status: number;
  parent_id: number | null;
  icon: string | null;
  title: string;
};

export type LangList = {
  id: number;
  keyx: string;
  status: number;
  parent_id: number | null;
  icon: string | null;
  title: string;
  title_vi: string;
};

export type AppContextProps = {
  panel: React.ReactNode | Element;
  setPanel: (panel: ReactNode | Element | null) => void;
  listTheme: ThemeItem[];
  isOpenPanel: boolean;
  setOpenPanel: (isOpen: boolean) => void;
};
