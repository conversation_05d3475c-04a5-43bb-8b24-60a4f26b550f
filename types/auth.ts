export interface IRole {
  id: number;
  position?: number;
  title: string;
  list_access: Partial<Record<'learn' | 'home', string>>;
}
export interface SendOtpParams {
  phone: string;
  otp: string;
}
export type JWTParams = {
  accessToken: string;
  accessTokenExpiry: number;
  refreshToken: string;
};

export type Account = {
  id: number;
  phone: string;
  created_at: number;
  status: number;
  role_id?: number;
  role?: any;
  type?: number;
};

export type Member = {
  id: number;
  account_id: number;
  fullname: string;
  nickname: string;
  created_at: number;
  status: number;
  is_main?: number;
  token?: string;
  is_onboard?: number;
  role_id: number;
};
