import { ReportTypeEnum } from '@/configs/ReportTypeEnum';

export type UseSearchProps = {
  shouldNotFetch?: boolean;
  title: string;
  limit?: number;
  offset?: number;
};

export interface UseSearchEngineProps extends UseSearchProps {
  item: string;
}

export interface SearchResultItem {
  title: string;
  id: number;
  item: string;
  course_id: number;
  document_id: number;
  keywords: string;
  level: string;
  keyx: string;
  payload?: SearchResultItem;
}

export interface SearchItem {
  title: string;
  id: number;
}

export type ParamsEndListen = {
  document_id: number;
  member_id: number;
  sentence_ids: Array<number>;
  paragraph_id: number;
  member_exercise_token?: string;
};

export type ParamsEndSpeak = {
  character_id: number;
  member_id: number;
  sentence_id: number;
  paragraph_id: number;
  file: File;
  template: string;
  member_exercise_token?: string;
  access_token: string;
  transcript: string;
};
export type UseDocumentProps = {
  shouldNotFetch?: boolean;
  course_id?: number;
  id?: number;
  title?: string;
};
export type UseParagraphProps = {
  id?: number;
  document_id?: number;
  course_id?: number;
  group_id?: number;
  title?: string;
  item?: string;
  includes?: string;
  limit?: number;
  page?: number;
  status?: number;
};
export type UseCourseProps = {
  id?: number;
  limit?: number;
  title?: string;
};

export type UseMemberAddProps = {
  id?: number;
  nickname?: string;
  fullname: string;
  password: string;
  rePassword: string;
  avatar?: File;
};

export type UseTagDetailsProps = {
  id?: number;
  tag_id?: number;
  object_id?: number;
  status?: number;
  item?: string;
};

export type UseTagsProps = {
  parent_id?: number;
  item?: string;
};

export type UseOnboardingProps = {
  shouldNotFetch?: boolean;
  quest_id?: number;
};

export type UseMemberExerciseDetailProps = {
  shouldNotFetch?: boolean;
  offset?: number;
  memberId?: number;
  memberExerciseToken?: string;
  isFetch?: boolean;
  paragraph_id?: number;
  keyx?: string;
};
export type UseMemberExerciseProps = {
  groupId: number;
  memberId?: number;
};

export interface SentenceUpdateEntity {
  id?: number;
  content?: string;
  character_id?: number;
  course_id?: number;
  document_id?: number;
  paragraph_id?: number;
  position?: number;
  status?: number;
}

export interface UpsertCharacterEntity {
  id?: number;
  fullname: string;
  raw_fullname: string;
  gender: string;
  age: string;
  accent: string;
  paragraph_id: number | undefined;
  course_id: number | undefined;
}

export interface SentencePositionEntity {
  position: number;
  id: number;
}

export interface LearnProgramPositionEntity {
  position: number;
  id: number;
}
export type UseReportsProps = {
  item: ReportTypeEnum | '';
  start_day: number;
  end_day: number;
  object_id: number;
};

export type MemberToken = {
  token: string;
};

export type UseReportTokenProps = {
  item: ReportTypeEnum | '';
  start_at: number;
  end_at: number;
  member_id: number;
};
