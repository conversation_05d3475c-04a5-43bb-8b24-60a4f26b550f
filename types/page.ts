import { AppProps } from 'next/app';

import { CategoryEntity, GroupsEntity } from './model';

export type LoginPageProps = AppProps & {
  csrfToken: string;
};

export interface OtpPageProps {
  phone: string;
  deviceId: string;
  accessToken?: string;
}

export interface RegisterPageProps {
  deviceId: string;
}

export interface PasswordPageProps {
  deviceId: string;
  accessToken: string;
  fullname: string;
  phone: string;
}

export interface OnboardPageProps {}

export interface HomePageProps {
  categories: CategoryEntity[];
  sidebarType: number;
}

export interface LearnPageProps {
  categories: CategoryEntity[];
  token: string | null;
}

export interface DocumentPageProps {
  categories: CategoryEntity[];
}

export interface ParagraphPageProps {
  categories: CategoryEntity[];
}

export interface CoursePageProps {
  categories: CategoryEntity[];
}

export interface GroupPageProps {
  categories: CategoryEntity[];
}

export interface SettingPageProps {
  categories: CategoryEntity[];
}

export interface HistoryPageProps {
  categories: CategoryEntity[];
}

export interface ConversationPageProps {
  categories: CategoryEntity[];
}

export interface EssayPageProps {
  categories: CategoryEntity[];
}

export interface TagPageProps {
  categories: CategoryEntity[];
}

export interface FavouritePageProps {
  categories: CategoryEntity[];
}

export interface KnowledgePageProps {
  categories: CategoryEntity[];
}

export interface ComposeSentencePageProps {
  categories: ComposeSentencePageProps[];
}

type Member = {
  fullname: string;
  avatar: string;
  token: string;
  id: number;
  account_id: number;
  created_at: number;
};

export type MemberLoginTokenPageProps = AppProps & {
  csrfToken: string;
  dataMember: Member;
};

export type InviteGroupTokenPageProps = AppProps & {
  csrfToken: string;
  dataGroup: GroupsEntity;
};

export interface GalleryPageProps {
  categories: CategoryEntity[];
}
