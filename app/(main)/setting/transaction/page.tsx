'use client';

import React from 'react';

import UsageAccountPageContainer from '@/containers/setting/usage/UsageAccountPageContainer';
import UsageMemberPageContainer from '@/containers/setting/usage/UsageMemberPageContainer';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';

import ScrollArea from '@/components/ScrollArea';

// import Link from 'next/link';
import { useSession } from '@/hooks/useSession';

const UsagePage = () => {
  const { data: session } = useSession();
  const mounted = useMounted();
  const t = useTranslations();
  if (!mounted) return null;

  return (
    <>
      <ScrollArea
        className={'!h-[calc(100vh_-_0px)] relative w-full flex-1 bg-bg-general overflow-x-hidden'}
      >
        <div className={'mr-[265px] w-4/6'}>
          <div
            className={'flex justify-between border-b border-color-border py-3 ml-[30px] mb-[30px]'}
          >
            <div>
              <h6 className={'text-xl'}>{t('settings.usage.heading_page')}</h6>
              <h6 className={'text-sm text-color-minor mt-2'}>
                {t('settings.usage.descriptionUsage')}{' '}
              </h6>
            </div>
            <div className={'flex items-center'}>
              {/* <i className="text-[40px] ml-2 icon-diamond text-yellow-100" />
              <div className={'flex flex-col ml-4'}>
                <span>Số dư</span>
                <span className={'text-[22px]'}>1.000</span>
                <Link className={'text-[#2B398E]'} href="/setting/history">
                  Xem lịch sử nạp
                </Link>
              </div> */}
              {/* <Button size={'sm'} className={'ml-6'}>
                Nạp thêm
              </Button> */}
            </div>
          </div>
          {session.members ? <UsageAccountPageContainer /> : <UsageMemberPageContainer />}
        </div>
        {/* )} */}
      </ScrollArea>
    </>
  );
};

export default UsagePage;
