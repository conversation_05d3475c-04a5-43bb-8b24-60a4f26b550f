import React from 'react';

import Head from 'next/head';

import AccountPageContainer from '@/containers/setting/account/AccountPageContainer';
import { useTranslations } from 'next-intl';

const Account = () => {
  const t = useTranslations();

  return (
    <>
      <Head>
        <title>{t('settings.account.title')}</title>
      </Head>
      <div className={'pl-[30px] w-3/6'}>
        <div className={'border-b border-color-border py-[25px]'}>
          <h6 className={'text-2xl'}>{t('settings.account.title')}</h6>
        </div>
      </div>
      <AccountPageContainer />
      <div className="mb-5"></div>
    </>
  );
};

export default Account;
