'use client';

import React from 'react';

import Head from 'next/head';

import LanguageSelector from '@/containers/setting/workspace/LanguageSelector';
import ThemeSelector from 'containers/setting/workspace/ThemeSelector';
import { useMounted } from 'hooks/common/useMounted';
import { useLocale, useTranslations } from 'next-intl';

const Page = () => {
  const mounted = useMounted();
  const t = useTranslations();
  const locale = useLocale();
  if (!mounted) return null;
  return (
    <>
      <Head>
        <title>{t('settings.workspace.title')}</title>
      </Head>

      <>
        <div className={'pl-[30px] w-4/6'}>
          <div className={'border-b border-color-border py-[25px]'}>
            <h6 className={'text-2xl'}>{t('settings.workspace.title')}</h6>
            <h6 className={'text-sm text-grey-800 mt-2 !text-[13px]'}>
              <span>{t('settings.workspace.description')}</span>
            </h6>
          </div>
        </div>
        <div className={' py-3 pl-[30px] w-4/6'}>
          <ThemeSelector />
          {/*<FontSelector locale={locale} />*/}
          <LanguageSelector locale={locale} />
          <div className="mb-5"></div>
        </div>
      </>
    </>
  );
};

export default Page;
