import React from 'react';

import NotificationPage from 'containers/setting/NotificationPage';
import { useTranslations } from 'next-intl';

const Page = () => {
  const t = useTranslations();
  return (
    <>
      <div className={'border-b border-color-border  py-3 pl-[30px]'}>
        <h6 className={'text-xl'}>{t('settings.notification')}</h6>
        <h6 className={'text-sm text-color-minor mt-2'}>
          {t('settings.descriptionNotification')}{' '}
        </h6>
      </div>
      <NotificationPage />
    </>
  );
};

export default Page;
