'use client';

import React from 'react';

import TransactionPageContainer from '@/containers/setting/transaction/TransactionPageContainer';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';

import ScrollArea from '@/components/ScrollArea';

const TransactionPage = () => {
  const mounted = useMounted();
  const t = useTranslations();
  if (!mounted) return null;

  return (
    <>
      <ScrollArea
        className={'!h-[calc(100vh_-_33px)] relative w-full flex-1 bg-bg-general overflow-x-hidden'}
      >
        <div className={'mr-[265px] w-4/6'}>
          <div className={'flex justify-between border-b border-color-border py-3 ml-[30px]'}>
            <div>
              <h6 className={'text-xl'}>{t('settings.transactions.heading_page')}</h6>
              <h6 className={'text-sm text-color-minor mt-2'}>
                {t('settings.transactions.descriptionTransaction')}{' '}
              </h6>
            </div>
            <div className={'flex items-center'}>
              {/* <i className="text-[40px] ml-2 icon-diamond text-yellow-100" />
              <div className={'flex flex-col ml-4'}>
                <span>Số dư</span>
                <span className={'text-[22px]'}>1.000</span>
              </div> */}
              {/* <Button size={'sm'} className={'ml-6'}>
                Nạp thêm
              </Button> */}
            </div>
          </div>
          <TransactionPageContainer />
        </div>
      </ScrollArea>
    </>
  );
};

export default TransactionPage;
