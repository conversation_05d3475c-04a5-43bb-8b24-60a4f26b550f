'use client';

import React from 'react';

import Head from 'next/head';

import MemberListSkeleton from '@/containers/setting/member/MemberListSkeleton';
import classNames from 'classnames';
import Avatar from 'components/Avatar';
import Button from 'components/Button';
import ScrollArea from 'components/ScrollArea';
import EntRouters from 'configs/EntRouters';
import useListMembers from 'hooks/Ent/useListMembers';
import { useMounted } from 'hooks/common/useMounted';
import { map } from 'lodash';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import Link from '@/components/Link';

const Page = () => {
  const t = useTranslations();
  const mounted = useMounted();
  const { membersList, isLoading } = useListMembers();
  const copyLink = (token: string) => {
    navigator.clipboard
      .writeText(window.location.origin + '/member/' + token)
      .then(() => {
        toast.success('Token đã được sao chép vào clipboard');
      })
      .catch((err) => {
        console.error('Lỗi khi sao chép: ', err);
        toast.error('Xảy ra lỗi khi sao chép!');
      });
  };
  if (!mounted) return null;
  return (
    <>
      <Head>
        <title>{t('settings.member.title')}</title>
      </Head>
      <div className={'pl-[30px] w-4/6'}>
        <div className={'border-b border-color-border py-[25px]'}>
          <h6 className={'text-2xl'}>{t('settings.member.title')}</h6>
          <div
            className={
              'text-sm text-grey-800 mt-2 !text-[13px] flex items-center justify-between w-full'
            }
          >
            <span className={'float-start'}>{t('settings.member.description')}</span>
            <Link
              className={
                'flex justify-between items-center border border-color-border bg-bg-general px-2.5 py-0 rounded-md shadow-md text-medium'
              }
              href={EntRouters.member_add}
            >
              <i className={classNames('icon-add text-normal pr-2')} />
              {t('settings.member.add.title')}
            </Link>
          </div>
        </div>
      </div>
      <ScrollArea className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}>
        <div className={' py-3 pl-[30px] w-4/6'}>
          <table className="table-auto w-full" id="table_add_member">
            <tbody>
              {membersList &&
                map(membersList, (member, key) => (
                  <tr
                    key={`tr-${key}`}
                    className="bg-bg-general border-b border-color-line hover:bg-bg-box"
                  >
                    <td className="p-2 align-middle">
                      <Avatar
                        image={member.avatar}
                        size={32}
                        name={member.fullname}
                        className={'rounded-full w-[32px] h-[32px] float-left'}
                      />
                      <div className={'ml-[10px] float-left mt-[6px]'}>{member.fullname}</div>
                    </td>
                    <td className="p-2 align-middle">
                      <div className={'ml-[10px] float-left mt-[6px]'}>
                        {member?.member_token?.quantity}
                      </div>
                    </td>
                    <td className="p-2  w-10 align-middle">
                      <Button
                        size={'icon'}
                        variant={'bordered'}
                        color={'default'}
                        as={'a'}
                        onClick={() => copyLink(member.token)}
                      >
                        <i className={classNames('icon-links-line text-normal')} />
                      </Button>
                    </td>
                    <td className="w-10 text-right align-middle p-2">
                      <Link
                        className={
                          'w-8 h-8 flex items-center justify-center rounded-full hover:bg-bg-general hover:shadow-md'
                        }
                        href={EntRouters.member_setting + '/' + member.id}
                      >
                        <i className={classNames('icon-pencil-line text-normal')} />
                      </Link>
                    </td>
                  </tr>
                ))}
              {isLoading ? <MemberListSkeleton /> : null}
            </tbody>
          </table>
        </div>
        <div className="mb-5"></div>
      </ScrollArea>
    </>
  );
};

export default Page;
