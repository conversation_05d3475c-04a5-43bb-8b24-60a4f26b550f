'use client';

import React, { memo, useEffect, useState } from 'react';

import Head from 'next/head';
import { useParams, useRouter } from 'next/navigation';

import MemberDetailSkeleton from '@/containers/setting/member/MemberDetailSkeleton';
import { Avatar } from '@heroui/react';
import classNames from 'classnames';
import Button from 'components/Button';
import Input from 'components/Form/Input';
import ScrollArea from 'components/ScrollArea';
import { OTP_MEMBER_LENGTH } from 'configs';
import EntRouters from 'configs/EntRouters';
import useAuth from 'hooks/Ent/useAuth';
import useEditMember from 'hooks/Ent/useEditMember';
import useListMembers from 'hooks/Ent/useListMembers';
import { useTranslations } from 'next-intl';
import { SubmitHandler, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import OtpInput from 'react-otp-input';
import { UseMemberAddProps } from 'types/hooks';

import ErrorModal from '@/components/ServicePopup/ErrorModal';

import { useSession } from '@/hooks/useSession';

const MemberAdd: React.FC<UseMemberAddProps> = () => {
  const t = useTranslations();
  const router = useRouter();
  const params = useParams();
  const { data: session } = useSession();
  const [OTP, setOTP] = useState('');
  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
  } = useForm<UseMemberAddProps>();
  const [avatarImage, setAvatarImage] = useState<string | ArrayBuffer | null>(null); // State để lưu ảnh đại diện
  const [submitType, setSubmitType] = useState('');
  const { setMessage, errorMessage } = useAuth();
  const { doChangeMemberById } = useEditMember();
  const member_id = params.id?.toString() || '';
  if (!member_id) router.back();
  const { membersList, isLoading } = useListMembers(member_id ? parseInt(member_id) : 0);
  useEffect(() => {
    if (membersList && membersList.length > 0) {
      setValue('id', membersList[0].id);
      setAvatarImage(membersList[0].avatar);
      if (session && membersList[0].account_id !== session?.user?.id) {
        // Thực hiện các hành động cần thiết
        router.back();
      }
      setValue('fullname', membersList[0]?.fullname || '');
      setValue('nickname', membersList[0]?.nickname || '');
    }
  }, [membersList]);
  const onSubmit: SubmitHandler<UseMemberAddProps> = async (data) => {
    try {
      const updateParams = { ...data };
      if (submitType === 'changePass') {
        setMessage('');
        updateParams.fullname = '';
        updateParams.avatar = new File([''], 'avatar');
        updateParams.nickname = '';
        if (OTP === '') {
          setMessage(t('message.error.password.empty'));
          return;
        }
        if (OTP.length !== OTP_MEMBER_LENGTH) {
          setMessage(t('message.error.password.isset'));
          return;
        }
        updateParams.password = OTP;
      } else if (submitType === 'updateInfo') {
        updateParams.password = '';
      }
      const res = await doChangeMemberById(updateParams);
      if (res.success) {
        toast.success(t('message.success.common'));
        router.push(EntRouters.member_setting);
      } else {
        toast.error(t('message.error.exit'));
      }
    } catch {
      toast.error(t('message.error.exit'));
    }
  };

  const handleChangeOTP = (value: string) => {
    setOTP(value);
  };
  const handleAvatarClick = () => {
    const inputElement = document.querySelector('input[name="avatar"]');
    if (inputElement) {
      // @ts-ignore
      inputElement.click();
    }
  };
  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]; // Lấy tệp hình ảnh từ sự kiện onChange
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarImage(reader.result); // Cập nhật ảnh đại diện với dữ liệu của tệp hình ảnh
      };
      reader.readAsDataURL(file); // Đọc tệp hình ảnh
    }
  };
  return (
    <>
      <Head>
        <title>{t('settings.member.edit.title')}</title>
      </Head>
      {isLoading ? (
        <table className={'w-3/6'}>
          <tbody>
            <MemberDetailSkeleton />
          </tbody>
        </table>
      ) : (
        <>
          <div className={'pl-[30px] w-3/6'}>
            <div className={'border-b border-color-border py-[25px]'}>
              <h6 className={'text-2xl'}>
                {t('settings.member.edit.title')} - {membersList[0].fullname}
              </h6>
              <h6 className={'text-sm text-grey-800 mt-2 !text-[13px]'}>
                <span className={'float-start'}>{t('settings.member.description')}</span>
                <Button
                  size={'xs'}
                  variant={'bordered'}
                  color={'default'}
                  as={'a'}
                  className={'float-end'}
                  href={EntRouters.member_add}
                >
                  <i className={classNames('icon-add text-normal')} />{' '}
                  {t('settings.member.add.title')}
                </Button>
                <p className={'clear-end'}></p>
              </h6>
            </div>
          </div>
          <div className={' py-3 pl-[20px]'}>
            <ScrollArea
              className={'h-screen relative w-full flex-1 bg-bg-general overflow-x-hidden'}
            >
              <form onSubmit={handleSubmit(onSubmit)}>
                {/* {errorMessage !== '' && <div className={'mt-3 mb-4 text-[#EA1437]'}>{errorMessage}</div>}*/}
                <div className="mt-[20px] pl-2.5 mb-[30px]">
                  <div className={'pb-3 text-[14px]'}>{t('settings.avatar')}</div>
                  <Avatar
                    onClick={handleAvatarClick}
                    src={(avatarImage && avatarImage.toString()) || ''}
                    fallback={
                      <span className="whitespace-nowrap">
                        <i className={'icon-upload text-normal'}></i>
                        {t('settings.upload')}
                      </span>
                    }
                    showFallback
                    className={
                      'w-[128px] h-[128px] bg-bg-box border-[1px] border-color-border text-color-major  cursor-pointer'
                    }
                  />
                  <Input
                    {...register('avatar')}
                    name={'avatar'}
                    type={'file'}
                    className={'hidden'}
                    inputClassName={'p-0 !px-0 text-normal border-0 rounded-0'}
                    onChange={handleAvatarChange}
                  />
                </div>
                <div className="mt-[20px] pl-2.5 mb-[30px]">
                  <div className={'pb-3 text-[14px]'}>{t('settings.nickname')}</div>
                  <Input
                    {...register('nickname', { required: submitType === 'updateInfo' })}
                    inputMode={'text'}
                    className={' w-3/6 border-0 '}
                    inputClassName={
                      'p-2 text-normal border-1 rounded-[10px] border-color-border bg-bg-box'
                    }
                  />
                  {errors.nickname?.type === 'required' && (
                    <div className={'mt-3 mb-4 text-red'}>{t('message.error.nickname.empty')}</div>
                  )}
                </div>
                <div className="mb-[30px] pl-2.5">
                  <div className={'pb-3 text-[14px]'}>{t('settings.fullname')}</div>
                  <Input
                    {...register('fullname', { required: submitType === 'updateInfo' })}
                    inputMode={'text'}
                    type={'text'}
                    className={' w-3/6 border-0'}
                    inputClassName={
                      'p-2 text-normal border-1 rounded-[10px] border-color-border bg-bg-box'
                    }
                    aria-invalid={errors.fullname ? 'true' : 'false'}
                  />
                  {errors.fullname?.type === 'required' && (
                    <div className={'mt-3 mb-4 text-red'}>{t('message.error.fullname')}</div>
                  )}
                </div>
                <div className={'border-b border-color-border pl-2.5 mb-[40px]'}>
                  <Button
                    onClick={() => setSubmitType('updateInfo')}
                    type={'submit'}
                    className={'!w-auto  mb-[40px]'}
                    color={'primary'}
                    size={'lg'}
                  >
                    {t('settings.member.edit.button')}
                  </Button>
                </div>
                <div className="mb-[40px]">
                  <div className="mb-[20px] pl-2.5">
                    <h6 className={'text-xl'}>{t('settings.member.edit.pin')}</h6>
                  </div>
                  <div className="flex w-full flex-col">
                    <div className={'pb-3 text-[14px] pl-2.5'}>
                      {t('settings.member.edit.pinDescription')}
                    </div>
                    <OtpInput
                      value={OTP}
                      onChange={handleChangeOTP}
                      numInputs={OTP_MEMBER_LENGTH}
                      renderSeparator={''}
                      inputType={'tel'}
                      renderInput={(props) => <input {...props} />}
                      placeholder="0000"
                      containerStyle={{
                        fontSize: '40px',
                        marginLeft: '4px',
                      }}
                      inputStyle={{
                        color: 'Rgb(var(--color-major))',
                        width: '3rem',
                        margin: '0 5px',
                        border: '1px solid Rgb(var(--color-border))',
                        outline: 0,
                        borderRadius: '8px',
                        fontSize: 32,
                        background: 'var(--bg-box)',
                      }}
                    />
                    {errorMessage !== '' && (
                      <div className={'mt-3 text-[#EA1437]'}>{errorMessage}</div>
                    )}
                  </div>
                </div>
                <div className={'mb-[140px] pl-2.5'}>
                  <Button
                    onClick={() => setSubmitType('changePass')}
                    type={'submit'}
                    className={'!w-auto'}
                    color={'primary'}
                    size={'lg'}
                  >
                    {t('settings.member.edit.button')}
                  </Button>
                </div>
              </form>
              <div className="mb-5"></div>
            </ScrollArea>
          </div>
        </>
      )}

      <ErrorModal />
    </>
  );
};

export default memo(MemberAdd);
