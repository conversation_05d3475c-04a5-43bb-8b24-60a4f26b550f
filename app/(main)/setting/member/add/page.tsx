'use client';

import React, { useState } from 'react';

import Head from 'next/head';
import { useRouter } from 'next/navigation';

import { Avatar } from '@heroui/react';
import Button from 'components/Button';
import Input from 'components/Form/Input';
import { OTP_MEMBER_LENGTH } from 'configs';
import EntRouters from 'configs/EntRouters';
import useAddMember from 'hooks/Ent/useAddMember';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import { SubmitHandler, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import OtpInput from 'react-otp-input';
import { UseMemberAddProps } from 'types/hooks';

const Page = () => {
  const t = useTranslations();
  const router = useRouter();
  const [OTP, setOTP] = useState('');
  const mounted = useMounted();
  const {
    register,
    formState: { errors },
    handleSubmit,
    setValue,
    setError,
    clearErrors,
  } = useForm<UseMemberAddProps>();
  const [avatarImage, setAvatarImage] = useState<string | ArrayBuffer | null>(null); // State để lưu ảnh đại diện
  const { doAddMemberByFullName } = useAddMember();
  const onSubmit: SubmitHandler<UseMemberAddProps> = (data) => {
    if (data.password.length < OTP_MEMBER_LENGTH) {
      setError('password', { type: 'minLength', message: t('message.error.pin.atleast') });
      return;
    }
    try {
      console.log(data);
      doAddMemberByFullName(data).then((res) => {
        if (res.success) {
          toast.success(t('message.success.common'));
          router.push(EntRouters.member_setting);
          return;
        }
        toast.error(t('message.error.exit'));
      });
    } catch {
      toast.error(t('message.error.exit'));
    }
  };
  const handleAvatarClick = () => {
    const inputElement = document.querySelector('input[name="avatar"]');
    if (inputElement) {
      // @ts-ignore
      inputElement.click();
    }
  };
  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]; // Lấy tệp hình ảnh từ sự kiện onChange
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarImage(reader.result); // Cập nhật ảnh đại diện với dữ liệu của tệp hình ảnh
      };
      reader.readAsDataURL(file); // Đọc tệp hình ảnh
    }
  };
  if (!mounted) return null;
  const handleChangeOTP = (value: string) => {
    setOTP(value);
    setValue('password', value);
    // Kiểm tra nếu độ dài của OTP đạt đủ điều kiện để submit
    if (value.length === OTP_MEMBER_LENGTH) {
      clearErrors('password');
    }
  };
  return (
    <>
      <Head>
        <title>{t('settings.member.add.title')}</title>
      </Head>
      <>
        <div className={'pl-[30px] w-3/6'}>
          <div className={'border-b border-color-border py-[25px]'}>
            <h6 className={'text-2xl'}>{t('settings.member.add.title')}</h6>
            <h6 className={'text-sm text-grey-800 mt-2 !text-[13px]'}>
              <span>{t('settings.member.description')}</span>
            </h6>
          </div>
        </div>
        <div className={' py-3 pl-[30px] w-3/6'}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="mt-[20px] mb-[30px]">
              <div className={'pb-3 text-[14px]'}>{t('settings.avatar')}</div>
              <Avatar
                onClick={handleAvatarClick}
                src={(avatarImage && avatarImage.toString()) || ''}
                fallback={
                  <span className="whitespace-nowrap">
                    <i className={'icon-upload text-[24px]'}></i>
                    {t('settings.upload')}
                  </span>
                }
                showFallback
                className={
                  'w-[128px] h-[128px] bg-bg-box border-[1px] border-color-border text-color-major  cursor-pointer'
                }
              />
              <Input
                {...register('avatar')}
                name={'avatar'}
                type={'file'}
                className={'hidden'}
                inputClassName={'p-0 !px-0 text-normal border-0 rounded-0'}
                onChange={handleAvatarChange}
              />
            </div>
            <div className="mt-[20px] mb-[30px]">
              <div className={'pb-3 text-[14px]'}>{t('settings.nickname')}</div>
              <Input
                {...register('nickname', { required: true })}
                name={'nickname'}
                inputMode={'text'}
                type={'text'}
                className={'w-full border-0 '}
                inputClassName={
                  'p-2 text-normal border-1 rounded-[10px] border-color-border  bg-bg-box'
                }
                aria-invalid={errors.nickname ? 'true' : 'false'}
              />
              {errors.nickname?.type === 'required' && (
                <div className={'mt-3 mb-4 text-[#EA1437]'}>
                  {t('message.error.nickname.empty')}
                </div>
              )}
            </div>
            <div className="mb-[30px]">
              <div className={'pb-3 text-[14px]'}>{t('settings.fullname')}</div>
              <Input
                {...register('fullname', { required: true })}
                name={'fullname'}
                inputMode={'text'}
                type={'text'}
                className={'w-full border-0 '}
                inputClassName={
                  'p-2 text-normal border-1 rounded-[10px] border-color-border  bg-bg-box'
                }
                aria-invalid={errors.fullname ? 'true' : 'false'}
              />
              {errors.fullname?.type === 'required' && (
                <div className={'mt-3 mb-4 text-[#EA1437]'}>{t('message.error.fullname')}</div>
              )}
            </div>
            <div className=" mb-[40px]">
              <div className="flex w-full flex-col ">
                <div className={'pb-3 text-[14px]'}>{t('settings.member.add.pin')}</div>
                <OtpInput
                  {...register('password', { required: true })}
                  value={OTP}
                  onChange={handleChangeOTP}
                  numInputs={OTP_MEMBER_LENGTH}
                  renderSeparator={''}
                  inputType={'tel'}
                  renderInput={(props) => <input {...props} />}
                  placeholder="0000"
                  containerStyle={{
                    fontSize: '40px',
                    marginLeft: '-4px',
                  }}
                  inputStyle={{
                    color: 'Rgb(var(--color-major))',
                    width: '3rem',
                    margin: '0 5px',
                    border: '1px solid Rgb(var(--color-border))',
                    outline: 0,
                    borderRadius: '8px',
                    fontSize: 32,
                    background: 'var(--bg-box)',
                  }}
                />
                {errors.password && (
                  <div className={'mt-3 mb-4 text-[#EA1437]'}>
                    {errors.password.message || t('message.error.password.empty')}
                  </div>
                )}
              </div>
            </div>
            <Button type={'submit'} color={'primary'} size={'md'}>
              {t('settings.member.add.title')}
            </Button>
          </form>
          <div className="mb-5"></div>
        </div>
      </>
    </>
  );
};

export default Page;
