'use client';

import React from 'react';

import OnLoading from 'components/ServicePopup/OnLoading';
import PlansPage from 'containers/setting/PlansPage';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import useLoadingStore from 'store/loading';

//demo here https://www.material-tailwind.com/blocks#pricing
const Page = () => {
  const { loading } = useLoadingStore();
  const mounted = useMounted();
  const t = useTranslations();
  if (!mounted) return null;
  return (
    <>
      {loading ? (
        <OnLoading containerClassName={'h-[calc(100vh_-_100px)]'} />
      ) : (
        <>
          <div className={'border-b border-color-border  py-3 pl-[30px]'}>
            <h6 className={'text-xl'}>{t('settings.package')}</h6>
            <h6
              className={'text-sm text-color-minor mt-2'}
              dangerouslySetInnerHTML={{
                __html: t('settings.descriptionPackage', { memberName: '<PERSON><PERSON><PERSON><PERSON>' }),
              }}
            />
          </div>
          <PlansPage />
        </>
      )}
    </>
  );
};

export default Page;
