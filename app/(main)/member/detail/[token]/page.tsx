'use client';

import React from 'react';

import Head from 'next/head';
import { useParams } from 'next/navigation';

import useGetMember from '@/actions/member';
import MemberDetailContainer from '@/containers/member/MemberDetailContainer';
// import LoginMemberSkeleton from '@/containers/member/skeleton/loginMemberSkeleton';
import { MemberToken } from '@/types/hooks';

const Page: React.FC<MemberToken> = () => {
  const params = useParams();
  const { member, isLoading } = useGetMember((params.token as string) || '');
  return (
    <>
      <Head>
        <title>{member?.fullname}</title>
      </Head>
      {member && !isLoading && <MemberDetailContainer member={member} />}
    </>
  );
};

export default Page;
