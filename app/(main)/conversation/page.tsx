'use client';

import React from 'react';

import Head from 'next/head';

import { ITEM_CONVERSATION, SPECIAL_COURSE_ID } from 'configs';
// import TagsBar from 'containers/conversation/TagsBar';
import ConversationList from 'containers/conversation/ConversationList';
import Header from 'containers/conversation/Header';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import { ConversationPageProps } from 'types/page';

const Conversation: React.FC<ConversationPageProps> = () => {
  const mounted = useMounted();
  const t = useTranslations();
  if (!mounted) return null;
  return (
    <>
      <Head>
        <title>{t('conversation.title')}</title>
      </Head>
      <Header page={ITEM_CONVERSATION} />
      <div className="relative h-[calc(100vh_-_200px)]">
        <div className={'flex items-center'}>{/* <TagsBar categoryParentId={'203'} /> */}</div>
        <div
          className={'border-b border-bg-box h-0 w-full'}
          style={{
            borderBottomWidth: '10px',
          }}
        ></div>
        <div>
          <ConversationList type={ITEM_CONVERSATION} course_id={SPECIAL_COURSE_ID} />
        </div>
      </div>
    </>
  );
};

export default Conversation;
