'use client';

import React from 'react';

import Header from 'containers/document/Header';
import ParagraphListContainer from 'containers/paragraph/ParagraphListContainer';
import { useMounted } from 'hooks/common/useMounted';
import { ParagraphPageProps } from 'types/page';

const ParagraphPage: React.FC<ParagraphPageProps> = () => {
  const mounted = useMounted();

  if (!mounted) return null;
  return (
    <>
      <Header />
      <ParagraphListContainer />
    </>
  );
};

export default ParagraphPage;
