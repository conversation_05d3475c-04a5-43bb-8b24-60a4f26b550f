'use client';

import React from 'react';

// import { useTranslations } from 'next-intl';
import { ITEM_GALLERY, SPECIAL_COURSE_ID } from 'configs';
import ConversationList from 'containers/conversation/ConversationList';
import Header from 'containers/conversation/Header';
// import Head from 'next/head';
import TagsBar from 'containers/conversation/TagsBar';
import { useMounted } from 'hooks/common/useMounted';
import { GalleryPageProps } from 'types/page';

const Gallery: React.FC<GalleryPageProps> = () => {
  const mounted = useMounted();
  // const t = useTranslations();
  if (!mounted) return null;
  return (
    <>
      {/* <Head>
        <title>{t('conversation.title')}</title>
      </Head> */}
      <Header page={ITEM_GALLERY} />
      <div className="relative h-[calc(100vh_-_200px)]">
        <div className={'flex items-center'}>
          <TagsBar categoryParentId={'211'} />
        </div>
        <div
          className={'border-b border-bg-box h-0 w-full'}
          style={{
            borderBottomWidth: '10px',
          }}
        ></div>
        <div>
          <ConversationList type={ITEM_GALLERY} course_id={SPECIAL_COURSE_ID} />
        </div>
      </div>
    </>
  );
};

export default Gallery;
