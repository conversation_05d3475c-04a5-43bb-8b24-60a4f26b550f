'use client';

import React, { useEffect, useState } from 'react';

import Head from 'next/head';
import { useParams, useRouter, useSearchParams } from 'next/navigation';

import { SPECIAL_DOCUMENT_ID } from '@/configs';
import { ConversationTypeEnum } from '@/configs/ConversationEnum';
import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { CreateGallery } from '@/containers/gallery/CreateGallery';
import { UploadImage } from '@/containers/gallery/UploadImage';
import useLearnStore from '@/store/learn';
import { Divider } from '@heroui/react';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';

import Button from '@/components/Button';
import OnLoading from '@/components/ServicePopup/OnLoading';

import useConversations from '@/hooks/Ent/useConversations';
import useDocument from '@/hooks/Ent/useDocument';
import { useCreateSentenceGroup } from '@/hooks/Ent/useGallery';
import useParagraph from '@/hooks/Ent/useParagraph';
import { useSentenceGroups } from '@/hooks/Ent/useSentenceGroups';

const AddSentencesPage = () => {
  const router = useRouter();
  const t = useTranslations();
  const params = useParams();
  const { paragraph_id } = params;
  const searchParams = useSearchParams();
  const prevSentenceGroupIdUrlParams = searchParams.get('prevSentenceGroupId');
  const [prevSentenceGroupId, setPrevSentenceGroupId] = useState<string>('0');

  useEffect(() => {
    const prevSentenceGroupIdNumber = Number(prevSentenceGroupIdUrlParams);
    if (prevSentenceGroupId && !isNaN(prevSentenceGroupIdNumber)) {
      setPrevSentenceGroupId(prevSentenceGroupIdUrlParams || '0');
    }
  }, [prevSentenceGroupIdUrlParams]);

  const [contents, setContents] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const { activeParagraph, setCourse, setParagraphs, setActiveDocument, setActiveParagraph } =
    useLearnStore();

  useEffect(() => {
    if (!paragraph_id || parseInt(paragraph_id as string) === 0) router.back();
  }, [params]);

  const { paragraph } = useParagraph((paragraph_id as string) || '', !!activeParagraph);

  const { mutate: mutateConversation } = useConversations(
    LearnTypeEnum.APPROVE,
    activeParagraph?.item === ConversationTypeEnum.GALLERY
  );

  const { mutate: mutateSentenceGroups } = useSentenceGroups({
    paragraph_id: paragraph?.id || 0,
    item: activeParagraph?.item || '',
  });

  useEffect(() => {
    if (paragraph?.id) {
      setActiveParagraph(paragraph);
    } else {
      setActiveParagraph(null);
    }
  }, [paragraph]);

  const { document } = useDocument(
    activeParagraph?.document_id ? activeParagraph.document_id : SPECIAL_DOCUMENT_ID,
    !activeParagraph || activeParagraph.document_id === SPECIAL_DOCUMENT_ID
  );

  useEffect(() => {
    if (document?.id) {
      setParagraphs(document.paragraphs || []);
      setCourse(document.course || null);
      setActiveDocument(document);
    }
  }, [document]);

  const [files, setFiles] = useState<FileList | null>(null);
  const handleChange = (files: FileList) => {
    setFiles(files);
  };

  const onDelete = (index: number) => {
    if (!files) return;
    if (Object.keys(files).length === 1) {
      setFiles(null);
      return;
    }
    const newFiles = { ...files };
    delete newFiles[index];
    setFiles(newFiles);
    setContents((prev) => {
      const newContents = [...prev];
      newContents.splice(index, 1);
      return newContents;
    });
  };

  const handleCreateGallery = async () => {
    try {
      if (!files || Object.keys(files).length <= 0) {
        toast.error(t('message.error.gallery.empty_image'));
        return;
      }
      if (
        contents.length !== Object.keys(files).length ||
        contents.some((content) => !content || content.trim() === '')
      ) {
        toast.error(t('message.error.gallery.content'));
        return;
      }
      setLoading(true);
      for (const key of Object.keys(files)) {
        await useCreateSentenceGroup({
          course_id: activeParagraph?.course_id?.toString() || '',
          paragraph_id: activeParagraph?.id?.toString() || '',
          document_id: activeParagraph?.document_id?.toString() || '',
          file: files[key],
          content: contents[key],
          pre_sentence_group_id: prevSentenceGroupId,
        });
      }
      setLoading(false);
      toast.success(t('message.success.create_gallery'));
      setFiles(null);
      setContents([]);
      await mutateConversation();
      await mutateSentenceGroups();
      return router.back();
    } catch (e) {
      setLoading(false);
      console.log(e);
      toast.error(e?.response?.data?.message || t('message.error.unknown'));
    }
  };

  return (
    <>
      <Head>
        <title>Tạo Gallery</title>
      </Head>

      <div className={'flex flex-col min-h-screen'}>
        <div className={'w-full flex flex-col flex-1 px-[15px] pt-[15px]'}>
          <div className={'flex items-center'}>
            <p className={'text-sm font-medium'}>{paragraph?.title || ''}</p>
            <i className={'text-sm icon-arrow-right-fill'} />
            <p className={'text-sm font-medium'}>{t('gallery.create_gallery')}</p>
          </div>
          <div className={'mt-[15px] flex flex-col flex-1 pb-[71px]'}>
            {loading && (
              <div className={'absolute h-full w-full z-50 pb-[101px]'}>
                <div className={'relative w-full h-full'}>
                  <OnLoading />
                </div>
              </div>
            )}
            {!loading && (!files || Object.keys(files).length <= 0) && (
              <UploadImage handleChange={handleChange} />
            )}
            {!loading && files && Object.keys(files).length > 0 && (
              <CreateGallery
                onDelete={onDelete}
                files={files}
                contents={contents}
                setContents={setContents}
              />
            )}
          </div>
        </div>
        <div className={'absolute bottom-0 left-0 w-full'}>
          <Divider orientation={'horizontal'} className={'bg-color-line'} />
          <div className="flex h-[50px] items-center py-2 justify-end w-full px-[15px]">
            <Button
              className={'bg-bg-button'}
              size={'sm'}
              disabled={loading}
              onClick={() => {
                if (files && Object.keys(files).length > 0) {
                  setFiles(null);
                  return;
                }
                router.back();
              }}
            >
              {t('gallery.cancel')}
            </Button>
            <Button
              color={'primary'}
              className={'!shadow-none ml-2'}
              size={'sm'}
              disabled={loading}
              onClick={async () => {
                await handleCreateGallery();
              }}
            >
              {t('gallery.save')}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default AddSentencesPage;
