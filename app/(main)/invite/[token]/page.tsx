'use client';

import React, { useState } from 'react';

import Head from 'next/head';
import { useParams, useRouter } from 'next/navigation';

import InviteMemberContainer from '@/containers/invite/InviteMember';
import Avatar from 'components/Avatar';
import Button from 'components/Button';
import useAddMemberGroups from 'hooks/Ent/useAddMemberGroups';
import useGroupMemberByToken from 'hooks/Ent/useGroupMemberByToken';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';
import { InviteGroupTokenPageProps } from 'types/page';

import { useSession } from '@/hooks/useSession';

const InviteGroupToken: React.FC<InviteGroupTokenPageProps> = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const params = useParams();
  const t = useTranslations();
  const token = params.token?.toString() || '';
  if (!token) router.back();
  const { group } = useGroupMemberByToken(token);
  const mounted = useMounted();
  const { doAddMemberGroups } = useAddMemberGroups();
  const [isHidden, setIsHidden] = useState(false);

  const handleInvite = async (group_id: number) => {
    if (group_id === 0) {
      toast.error(t('group.class.invalid_group'));
      return;
    }
    setIsHidden(true); // Ẩn nút ngay khi click
    try {
      const res = await doAddMemberGroups(group_id);
      if (res.success) {
        toast.success(t('group.class.request_sent_sus'));
        setIsHidden(true);
      } else if (res.message === 'member_group_is_processing') {
        toast.error(t('group.class.request_sent_false'));
        setIsHidden(true); // Hiển thị lại nếu thất bại
      } else {
        toast.error(t('group.class.request_sent_false'));
        setIsHidden(false); // Hiển thị lại nếu thất bại
      }
    } catch (e) {
      toast.error(t('group.class.request_sent_false'));
      setIsHidden(false); // Hiển thị lại nếu có lỗi
    }
  };

  if (!mounted) return null;
  return (
    <>
      <Head>
        <title>{group?.title}</title>
      </Head>
      {/* {session.members ? (
        <InviteMemberContainer group={group} />
      ) : ( */}
      <div className="container mx-auto px-4 h-full">
        <div className="flex content-center items-center justify-center h-full">
          <div className="w-full lg:w-5/12 px-4">
            <div className="relative border-0 flex flex-col min-w-0 break-words w-full mb-2 ">
              <div className="flex-auto pt-10">
                <div className={'text-color-major font-medium text-base text-center mb-4'}>
                  {group?.parent_title ? `${group.parent_title} /` : ''} {group?.title}
                </div>
                <div className="flex items-center justify-center mb-[40px]">
                  <Avatar
                    size={50}
                    name={group?.account_manager?.fullname}
                    className={'rounded-full w-[50px] h-[50px] object-center'}
                  />
                  <div className={'text-left my-2 ml-2'}>
                    <div className={'text-color-major font-normal text-base'}>
                      {group?.account_manager?.fullname}
                    </div>
                    <div className={'text-color-minor font-medium text-sm '}>
                      {t('group.class.management')}
                    </div>
                  </div>
                </div>
                <div className={'text-color-major text-center text-[13px] font-normal mb-[20px]'}>
                  {t('group.class.join_note')}
                </div>
                {session.members ? (
                  <InviteMemberContainer group={group} />
                ) : (
                  <div className={'text-center mb-2'}>
                    {!isHidden && (
                      <Button
                        color={'primary'}
                        size={'xs'}
                        className={'h-[35px] font-medium text-base'}
                        onClick={() => handleInvite(group?.id)}
                      >
                        {t('group.class.request')}
                      </Button>
                    )}
                  </div>
                )}

                {/*<div className={'text-center text-[10px] font-medium'}>Enter</div>*/}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* )} */}
    </>
  );
};
export default InviteGroupToken;
