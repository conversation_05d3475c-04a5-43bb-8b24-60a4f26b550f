import React from 'react';

import categoryAction from '@/actions/category';
import AppPanel from '@/containers/layout/AppPanel';
import classNames from 'classnames';
import { Toaster } from 'react-hot-toast';

import Sidebar from '@/components/Sidebar/Sidebar';

export async function generateMetadata() {
  return {
    title: 'Lang Enter',
    description: 'lang enter',
  };
}

async function RootLayout({ children }) {
  const categories = await categoryAction();
  return (
    <div className={classNames('flex w-screen overflow-hidden h-full font-family text-normal')}>
      <Sidebar categories={categories} />
      <div className={'h-screen w-full flex-1 bg-bg-general relative'}>
        <div className="relative w-full h-full md:px-0">{children}</div>
      </div>
      <Toaster position="bottom-right" />
      <AppPanel />
    </div>
  );
}

export default RootLayout;
