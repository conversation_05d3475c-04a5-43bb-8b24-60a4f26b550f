'use client';

import React from 'react';

import Head from 'next/head';

import CourseListContainer from '@/containers/course/CourseListContainer';
import Header from 'containers/course/Header';
import { useTranslations } from 'next-intl';
import { CoursePageProps } from 'types/page';

const CoursePage: React.FC<CoursePageProps> = () => {
  const t = useTranslations();
  return (
    <>
      <Head>
        <title>{t('course.title')}</title>
      </Head>
      <Header />
      <CourseListContainer />
    </>
  );
};

export default CoursePage;
