'use client';

import React, { useEffect } from 'react';

import Head from 'next/head';
import { useParams, useRouter } from 'next/navigation';

import DocumentContainer from '@/containers/document/DocumentContainer';
import Header from 'containers/course/Header';
import useCourse from 'hooks/Ent/useCourse';
import { useMounted } from 'hooks/common/useMounted';
import useHeaderStore from 'store/header';
import { DocumentPageProps } from 'types/page';

const DocumentPage: React.FC<DocumentPageProps> = () => {
  const mounted = useMounted();
  const router = useRouter();
  const params = useParams();
  const { setTitle, title } = useHeaderStore();
  const course_id = params.id?.toString() || '';
  if (!course_id) router.back();
  const { courseList } = useCourse({
    id: course_id ? parseInt(course_id) : 0,
  });
  useEffect(() => {
    if (courseList.length) {
      setTitle(courseList[0].title);
    } else {
      setTitle('Document');
    }
  }, [courseList]);

  if (!mounted) return null;
  return (
    <>
      <Head>
        <title>{title}</title>
      </Head>
      <Header />
      <DocumentContainer />
    </>
  );
};

export default DocumentPage;
