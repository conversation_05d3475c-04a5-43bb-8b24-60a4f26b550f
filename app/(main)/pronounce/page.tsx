'use client';

import React from 'react';

import PronounceContainer from '@/containers/pronounce/PronounceContainer';
import Header from 'containers/pronounce/Header';
import { useMounted } from 'hooks/common/useMounted';

const PronouncePage = () => {
  const mounted = useMounted();
  if (!mounted) return null;
  return (
    <>
      <Header />
      <PronounceContainer />
    </>
  );
};

export default PronouncePage;
