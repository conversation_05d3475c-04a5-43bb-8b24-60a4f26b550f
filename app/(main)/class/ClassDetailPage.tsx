import React from 'react';

import Head from 'next/head';
import { useParams, useRouter } from 'next/navigation';

import ClassPageContainer from '@/containers/class/ClassPageContainer';
import { GroupPageProps } from '@/types/page';
import { useTranslations } from 'next-intl';

const ClassDetailPage: React.FC<GroupPageProps> = () => {
  const t = useTranslations();
  const router = useRouter();
  const params = useParams();
  const id = params.id?.toString() || '';
  if (!id) router.back();
  return (
    <>
      <Head>
        <title>{t('group.title')}</title>
      </Head>
      <ClassPageContainer classId={id} key={'main-member'} />
    </>
  );
};
export default ClassDetailPage;
