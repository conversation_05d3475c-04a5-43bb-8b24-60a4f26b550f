'use client';

import React, { useEffect, useState } from 'react';

import { useParams, useRouter } from 'next/navigation';

import GroupTypeEnum from '@/configs/GroupTypeEnum';
import DashboardHeader from '@/containers/class/dashboard/DashboardHeader';
import MemberDetailReportContainer from '@/containers/class/member/MemberDetailReportContainer';
import { Entity } from '@/types/model';
import Header from 'containers/class/Header';
import useGroupMember from 'hooks/Ent/useGroupMember';
import { useTranslations } from 'next-intl';
import { GroupPageProps } from 'types/page';

const MemberChartPage: React.FC<GroupPageProps> = () => {
  const t = useTranslations();
  const router = useRouter();
  const params = useParams();
  const id = params.id?.toString() || '';
  const memberId = params.memberId?.toString() || '';
  if (!id) router.back();
  const [headerData, setHeaderData] = useState<Entity[]>([]);
  const { group } = useGroupMember(parseInt(id));

  useEffect(() => {
    if (group) {
      if (group.type !== GroupTypeEnum.CLASS) {
        router.push('/');
        return;
      }

      const newData: { id: number; title: string }[] = [];
      if (group.parent_title) {
        newData.push({ id: group.parent_id, title: group.parent_title });
      }
      newData.push({ id: group.id, title: group.title });
      if (group.member_groups?.length > 0) {
        const memberGroup = group.member_groups.find(
          (item) => item.member_id.toString() === memberId && item.group_id.toString() === id
        );
        if (memberGroup) {
          newData.push({ id: 0, title: memberGroup.member?.fullname });
        }
      }
      setHeaderData(newData);
    }
  }, [group]);

  return (
    <>
      <Header
        key={`header-${id}`}
        token={group?.token}
        breadcrumbs={headerData}
        tooltipContent={t('group.class.linkMemberLogin')}
        link={null}
      />
      <div className={'flex items-center justify-between pr-[30px] border-b-[10px] border-bg-box'}>
        <div className="mb-[10px]">
          <div className="flex items-center"></div>
        </div>
        <div className={'mb-[10px]'}>
          <DashboardHeader />
        </div>
      </div>
      <MemberDetailReportContainer memberId={memberId} />
    </>
  );
};

export default MemberChartPage;
