'use client';

import React from 'react';

import Head from 'next/head';

import HeaderSearch from 'containers/search/HeaderSearch';
import SearchContainer from 'containers/search/SearchContainer';
import { HomePageProps } from 'types/page';

const SearchPage: React.FC<HomePageProps> = () => {
  return (
    <>
      <Head>
        <title>T<PERSON><PERSON> kiếm</title>
      </Head>
      <HeaderSearch />
      <SearchContainer />
    </>
  );
};
export default SearchPage;
