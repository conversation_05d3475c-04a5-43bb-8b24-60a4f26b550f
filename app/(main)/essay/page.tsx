'use client';

import React from 'react';

import Head from 'next/head';

import { ITEM_ESSAY, SPECIAL_COURSE_ID } from 'configs';
import ConversationList from 'containers/conversation/ConversationList';
import Header from 'containers/conversation/Header';
import TagsBar from 'containers/conversation/TagsBar';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import { EssayPageProps } from 'types/page';

const Essay: React.FC<EssayPageProps> = () => {
  const mounted = useMounted();
  const t = useTranslations();
  if (!mounted) return null;
  return (
    <>
      <Head>
        <title>{t('essay.title')}</title>
      </Head>
      <Header page={ITEM_ESSAY} />
      <div className="relative h-[calc(100vh_-_200px)]">
        <div className={'flex items-center'}>
          <TagsBar categoryParentId={'211'} />
        </div>
        <div
          className={'border-b border-bg-box h-0 w-full'}
          style={{
            borderBottomWidth: '10px',
          }}
        ></div>
        <div>
          <ConversationList type={ITEM_ESSAY} course_id={SPECIAL_COURSE_ID} />
        </div>
      </div>
    </>
  );
};

export default Essay;
