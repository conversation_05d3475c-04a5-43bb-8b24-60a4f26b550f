'use client';

import React from 'react';

import Head from 'next/head';
import { useParams, useRouter } from 'next/navigation';

import ConversationTagsList from 'containers/conversation/ConversationTagsList';
import Header from 'containers/conversation/Header';
import TagsBar from 'containers/conversation/TagsBar';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import { ConversationPageProps } from 'types/page';

const Essay: React.FC<ConversationPageProps> = () => {
  const mounted = useMounted();
  const t = useTranslations();
  const router = useRouter();
  const params = useParams();
  const tag_id = params.id?.toString() || '';
  if (!tag_id) router.back();

  if (!mounted) return null;
  return (
    <>
      <Head>
        <title>{t('essay.title')}</title>
      </Head>
      <Header page={'essay'} />

      <div className={'flex items-center'}>
        <TagsBar categoryParentId={'211'} />
      </div>
      <div
        className={'border-b border-bg-box h-0 w-full'}
        style={{
          borderBottomWidth: '10px',
        }}
      ></div>

      <ConversationTagsList />
    </>
  );
};

export default Essay;
