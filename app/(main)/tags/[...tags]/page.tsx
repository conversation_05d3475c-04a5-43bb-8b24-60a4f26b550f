'use client';

import React from 'react';

import { useParams } from 'next/navigation';

import TagList from 'containers/tag/TagList';
import { useMounted } from 'hooks/common/useMounted';
import { TagPageProps } from 'types/page';

const Tag: React.FC<TagPageProps> = () => {
  const mounted = useMounted();
  const params = useParams();
  if (!mounted) return null;
  return (
    <>
      {params.tags && params.tags.length >= 1 && (
        <TagList tag_id={Number(params.tags[0])} items="tag" />
      )}
    </>
  );
};

export default Tag;
