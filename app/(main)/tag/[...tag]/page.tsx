'use client';

import React from 'react';

import { useParams } from 'next/navigation';

import TagList from 'containers/tag/TagList';
import { useMounted } from 'hooks/common/useMounted';
import { TagPageProps } from 'types/page';

const Page: React.FC<TagPageProps> = () => {
  const mounted = useMounted();
  const params = useParams();
  if (!mounted) return null;
  return (
    <>
      {params.tag && params.tag.length >= 2 && (
        <TagList tag_id={Number(params.tag[1])} items={params.tag[0]} />
      )}
    </>
  );
};

export default Page;
