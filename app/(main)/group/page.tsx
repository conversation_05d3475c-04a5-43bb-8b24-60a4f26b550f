'use client';

import React from 'react';

import Head from 'next/head';

import GroupPageContainer from 'containers/group/GroupPageContainer';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import { GroupPageProps } from 'types/page';

const GroupPage: React.FC<GroupPageProps> = () => {
  const mounted = useMounted();
  const t = useTranslations();
  if (!mounted) return null;
  return (
    <>
      <Head>
        <title>{t('group.title')}</title>
      </Head>
      <GroupPageContainer />
    </>
  );
};

export default GroupPage;
