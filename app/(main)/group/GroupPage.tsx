'use client';

import React from 'react';

import Head from 'next/head';
import { useParams, useRouter } from 'next/navigation';

import GroupDetailPageContainer from 'containers/group/GroupDetail/GroupDetailPageContainer';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import { GroupPageProps } from 'types/page';

const GroupPage: React.FC<GroupPageProps> = () => {
  const mounted = useMounted();
  const t = useTranslations();
  const router = useRouter();
  const params = useParams();
  const groupId = params.id?.toString() || '';

  // Early return if no group ID is found
  if (!groupId) {
    router.back();
    return null;
  }

  // Wait for client-side mounting
  if (!mounted) return null;

  return (
    <>
      <Head>
        <title>{t('group.title')}</title>
      </Head>
      <GroupDetailPageContainer group_id={parseInt(groupId)} />
    </>
  );
};

export default GroupPage;
