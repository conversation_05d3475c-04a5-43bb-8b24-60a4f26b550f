'use client';

import React from 'react';

import { useParams, useRouter } from 'next/navigation';

import MemberExercisesList from 'containers/class/member/MemberExercisesList';
import { useMounted } from 'hooks/common/useMounted';
import { GroupPageProps } from 'types/page';

const MemberExercisesPage: React.FC<GroupPageProps> = () => {
  const mounted = useMounted();
  const router = useRouter();
  const params = useParams();
  const { id: groupId, accountExerciseId } = params;
  if (!groupId) router.back();
  if (!mounted) return null;
  return (
    <>
      <MemberExercisesList groupId={groupId} accountExerciseId={accountExerciseId} />
    </>
  );
};

export default MemberExercisesPage;
