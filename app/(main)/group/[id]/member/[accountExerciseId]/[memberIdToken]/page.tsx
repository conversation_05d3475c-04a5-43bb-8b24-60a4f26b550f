'use client';

import React from 'react';

import { useParams, useRouter } from 'next/navigation';

import MemberDetailList from 'containers/class/MemberDetailList';
import { GroupPageProps } from 'types/page';

const MemberSpeakPage: React.FC<GroupPageProps> = () => {
  const router = useRouter();
  const params = useParams();
  const { id, memberIdToken, accountExerciseId } = params;
  if (!id || !memberIdToken) router.back();
  return (
    <>
      <MemberDetailList
        groupId={id}
        memberIdToken={memberIdToken}
        accountExerciseId={accountExerciseId}
      />
    </>
  );
};

export default MemberSpeakPage;
