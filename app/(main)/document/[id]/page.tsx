'use client';

import React, { useEffect } from 'react';

import { useParams, useRouter } from 'next/navigation';

import EntRouters from 'configs/EntRouters';
import useParagraphs from 'hooks/Ent/useParagraphs';

import LoadingPage from '@/components/LoadingPage';

const RedirectPage = () => {
  const router = useRouter();
  const params = useParams();
  if (!params) {
    router.back();
    return;
  }
  const document_id = params.id?.toString() || '';
  if (!document_id) {
    router.back();
    return;
  }
  const { paragraphList } = useParagraphs({
    document_id: parseInt(document_id.toString()),
  });
  useEffect(() => {
    if (paragraphList.length > 0) {
      router.push(`${EntRouters.learn}/${paragraphList[0].keyx}`);
    }
  }, [paragraphList]);
  return (
    <>
      <div className={'flex flex-col min-h-screen'}>
        <div className="relative">
          <LoadingPage containerClassName={'!h-[calc(100vh)] bg-white'} />
        </div>
      </div>
    </>
  );
};
export default RedirectPage;
