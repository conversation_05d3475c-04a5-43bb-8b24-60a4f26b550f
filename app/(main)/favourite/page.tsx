'use client';

import React from 'react';

import FavouriteListContainer from '@/containers/favourite/FavouriteListContainer';
import Header from 'containers/favourite/Header';
import { useMounted } from 'hooks/common/useMounted';
import { FavouritePageProps } from 'types/page';

const FavouritePage: React.FC<FavouritePageProps> = () => {
  const mounted = useMounted();
  if (!mounted) return null;
  return (
    <>
      <Header />
      <FavouriteListContainer />
    </>
  );
};

export default FavouritePage;
