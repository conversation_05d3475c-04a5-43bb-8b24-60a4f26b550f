import { NextRequest, NextResponse } from 'next/server';

import httpProxy from 'http-proxy';
import isEmpty from 'lodash/isEmpty';
import omit from 'lodash/omit';
import qs from 'qs';

const proxy = httpProxy.createProxyServer({
  target: process.env.NEXT_PUBLIC_BASE_API,
});

// export const runtime = 'edge'; // Optional: Use edge runtime for better performance

export async function handler(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const path = req.nextUrl.pathname.split('/').slice(3); // Adjust the slice value based on your path structure

  try {
    return new Promise((resolve, reject) => {
      let url = path.join('/');

      const queryObject = omit(Object.fromEntries(searchParams), ['path', 'publisher']);
      if (!isEmpty(queryObject)) {
        url += '?' + qs.stringify(queryObject);
      }

      const proxyReq = new NextRequest(new URL(url, process.env.NEXT_PUBLIC_BASE_API), {
        method: req.method,
        headers: req.headers,
        body: req.body,
      });

      proxy.web(
        // @ts-ignore
        proxyReq,
        {
          changeOrigin: true,
          selfHandleResponse: false,
        },
        (err: any) => {
          if (err) {
            console.error(err);
            reject(
              new NextResponse(
                JSON.stringify({ message: 'Internal server error', status: false }),
                { status: 500 }
              )
            );
          }
        }
      );

      proxy.on('proxyRes', (proxyRes: any) => {
        proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST';
        resolve(new NextResponse(null, { status: proxyRes.statusCode }));
      });
    });
  } catch (e) {
    return new NextResponse(JSON.stringify({ message: e.message || 'Internal server error', status: false }), {
      status: 500,
    });
  }
}

export { handler as GET, handler as POST };
