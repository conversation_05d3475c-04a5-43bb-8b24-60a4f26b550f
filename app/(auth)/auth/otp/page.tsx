'use client';

import React, { useEffect, useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import classNames from 'classnames';
import Button from 'components/Button';
import EntLogoIcon from 'components/Icons/EntLogoIcon';
import { OTP_EXPIRED_TIME_IN_SECOND, OTP_LENGTH } from 'configs';
import EntRouters from 'configs/EntRouters';
import useAuth from 'hooks/Ent/useAuth';
import { useTranslations } from 'next-intl';
import OtpInput from 'react-otp-input';
import { OtpPageProps } from 'types/page';

const OtpPage: React.FC<OtpPageProps> = ({ phone, deviceId = '', accessToken = '' }) => {
  let timer: any;
  const router = useRouter();
  const t = useTranslations();
  const [time, setTime] = useState(OTP_EXPIRED_TIME_IN_SECOND);
  const [OTP, setOTP] = useState('');
  const [btnText, setBtnText] = useState(t('auth.btnNext'));
  const [loading, setLoading] = useState(false);
  const { sendOtp, verifyOtp, errorMessage, setMessage } = useAuth();
  console.log('accessToken in otp page', accessToken);

  useEffect(() => {
    if (!phone) {
      router.push(EntRouters.register);
    }
    setTime(OTP_EXPIRED_TIME_IN_SECOND);
  }, []);
  useEffect(() => {
    if (time > 0) {
      timer = setTimeout(() => {
        setTime(time - 1);
      }, 1000);
    } else {
      clearTimeout(timer);
    }
    return () => {
      clearTimeout(timer);
    };
  }, [time]);

  const handleChangeOTP = (value: string) => {
    setOTP(value);
  };
  const handleVerifyOtp = async () => {
    if (loading) return;
    setMessage('');
    if (!phone) {
      router.push(EntRouters.register);
      return;
    }
    if (OTP.length !== OTP_LENGTH) {
      setMessage(t('auth.otpWrong'));
      return;
    }
    await verifyOtp(OTP, phone, deviceId).then(({ data, success, message }) => {
      setBtnText('Xác nhận');
      if (success) {
        router.push(EntRouters.password + `?accessToken=${data.token}`);
      } else {
        setMessage(message);
      }
    });
  };

  const handleResendOtp = async () => {
    if (loading) return;
    setOTP('');
    setMessage('');
    setBtnText('');
    try {
      setLoading(true);
      setBtnText(t('auth.sendingOTP'));

      await sendOtp(OTP).then(({ success, message }) => {
        if (success) {
          setBtnText('Xác nhận');
          setTime(OTP_EXPIRED_TIME_IN_SECOND);
        } else {
          setMessage(message);
        }
        setLoading(false);
      });
    } catch (e) {
      setLoading(false);
      console.log('exception Otp', e);
    }
  };
  return (
    <div className="container mx-auto px-4 h-full">
      <div className="flex content-center items-center justify-center h-full">
        <div className="w-full lg:w-4/12 px-4">
          <div className="relative border-0 flex flex-col min-w-0 break-words w-full mb-2 ">
            <div className="flex-auto px-4 lg:px-10  pt-10">
              <EntLogoIcon className={'h-auto w-[200px]'} />
              <div className="relative w-full mt-12 mb-10">
                <div className={'!text-normal !top-[-26px] !left-0 !pl-0 text-color-major'}>
                  {t('auth.phone')}
                </div>
                <div className={'text-lg'}>{phone}</div>
              </div>
              <div className="relative w-full my-3">
                <div className={'!text-normal !top-[-26px] !left-0 !pl-0 text-color-major'}>
                  {t('auth.labelOtp')}
                </div>
              </div>
              <div>
                <OtpInput
                  value={OTP}
                  onChange={handleChangeOTP}
                  numInputs={OTP_LENGTH}
                  renderSeparator={''}
                  inputType={'tel'}
                  renderInput={(props) => <input {...props} />}
                  placeholder="0000"
                  containerStyle={{
                    fontSize: '40px',
                  }}
                  inputStyle={{
                    color: '#00A758',
                    width: '3rem',
                    margin: '0 5px',
                    border: '1px solid #E5E5E5',
                    outline: 0,
                    borderRadius: '8px',
                    fontSize: 32,
                  }}
                />
              </div>
              {errorMessage !== '' && (
                <div className={'mt-4 text-xs text-red'}>{t(errorMessage)}</div>
              )}
              {time > 0 ? (
                <div className={'text-xs mt-4 text-color-minor'}>
                  {t('auth.descriptionOtp', { second: time })}
                </div>
              ) : (
                <button className={'text-xs mt-4 text-color-minor'} onClick={handleResendOtp}>
                  {t('auth.resendOtp')}
                </button>
              )}
              <div className="text-center flex items-center justify-start mt-8">
                <Link
                  href={EntRouters.register}
                  className={'text-sm flex items-center hover:text-purple hidden'}
                >
                  <i className={'text-base icon-arrow-left text-color-minor'} />
                  {t('auth.btnBack')}
                </Link>
                <Button
                  onClick={handleVerifyOtp}
                  isLoading={loading}
                  disabled={OTP.length !== OTP_LENGTH || time <= 0}
                  className={classNames(
                    'flex-0 !h-10 text-bg-general text-sm  px-12 py-3 mr-1 ease-linear transition-all duration-150  !bg-primary-200 !shadow-large !w-[175px]',
                    {}
                  )}
                >
                  {btnText}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default OtpPage;
