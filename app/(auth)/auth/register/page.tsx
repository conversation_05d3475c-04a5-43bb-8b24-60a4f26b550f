'use client';

import React, { useState } from 'react';

import { useRouter } from 'next/navigation';

import classNames from 'classnames';
import Button from 'components/Button';
import EntLogoRectangle from 'components/Icons/EntLogoRectangle';
import Input from 'components/Input';
import { COOKIE_FULL_NAME, COOKIE_PHONE, DEVICE_ID } from 'configs';
import EntRouters from 'configs/EntRouters';
import { setCookie } from 'cookies-next';
import useAuth from 'hooks/Ent/useAuth';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import { RegisterPageProps } from 'types/page';

const Page: React.FC<RegisterPageProps> = ({ deviceId: cookieDeviceId }) => {
  const { sendOtp } = useAuth();
  const router = useRouter();
  const t = useTranslations();
  const [phone, setPhone] = React.useState('');
  const [fullname, setFullname] = React.useState('');

  const [phoneError, setPhoneError] = useState<string>('');
  const [fullnameError, setFullnameError] = useState<string>('');
  const handleRegister = async () => {
    setPhoneError('');
    setFullnameError('');
    if (!fullname || fullname === '') {
      setFullnameError(t('auth.error.wrongFullname'));
      return;
    }
    if (!/(84|0[35789])+(\d{8})\b/.test(phone)) {
      setPhoneError(t('auth.error.wrongPhone'));
      return;
    }
    await sendOtp(phone, cookieDeviceId).then(({ data, success }) => {
      if (success) {
        setCookie(COOKIE_PHONE, phone);
        setCookie(COOKIE_FULL_NAME, fullname);
        setCookie(DEVICE_ID, data.device_id);
        router.push(EntRouters.otp + `?accessToken=${data.device_id}`);
      }
    });
  };
  const mounted = useMounted();
  if (!mounted) return null;
  return (
    <div className="container mx-auto px-4 h-full">
      <div className="flex content-center items-center justify-center h-full">
        <div className="w-full md:w-[500px] px-4">
          <div className="relative border-0 flex flex-col min-w-0 break-words w-full mb-2 ">
            <div className="flex-auto px-4 lg:px-10  pt-10">
              <EntLogoRectangle className={'w-[160px]'} />
              <div className="relative w-full mt-12">
                <Input
                  value={fullname}
                  onValueChange={setFullname}
                  removeLabel={false}
                  labelPlacement={'outside'}
                  className={classNames(
                    'mt-5 border-0 placeholder-blueGray-300 text-blueGray-600 bg-bg-general rounded-none border-b border-bg-box focus:outline-none focus:ring w-full ease-linear transition-all duration-150 text-xl text-color-major',
                    { '!border-red-100': fullnameError !== '' }
                  )}
                  type={'text'}
                  label={t('auth.fullname')}
                  placeholder={t('auth.fullname')}
                />
              </div>
              {fullnameError ? <div className={'mt-2 text-red'}>{t(fullnameError)}</div> : null}
              <div className="relative w-full mt-12">
                <Input
                  value={phone}
                  onValueChange={setPhone}
                  removeLabel={false}
                  labelPlacement={'outside'}
                  className={classNames(
                    'mt-5 border-0 placeholder-blueGray-300 text-blueGray-600 bg-bg-general rounded-none border-b border-bg-box focus:outline-none focus:ring w-full ease-linear transition-all duration-150 text-xl text-color-major',
                    { '!border-red-100': phoneError !== '' }
                  )}
                  type={'phone'}
                  label={t('auth.phone')}
                  placeholder={'0987 654 321'}
                />
              </div>
              {phoneError ? <div className={'mt-2 text-red'}>{t(phoneError)}</div> : null}
              <div className="text-center mt-10">
                <Button onClick={handleRegister} color={'primary'} size={'lg'}>
                  {t('auth.btnSendOtp')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
