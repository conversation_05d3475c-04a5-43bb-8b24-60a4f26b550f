'use client';

import React from 'react';

import { useRouter } from 'next/navigation';

import classNames from 'classnames';
import Button from 'components/Button';
import EntLogoIcon from 'components/Icons/EntLogoIcon';
import Input from 'components/Input';
import EntRouters from 'configs/EntRouters';
import useAuth from 'hooks/Ent/useAuth';
import { useMounted } from 'hooks/common/useMounted';
import { useTranslations } from 'next-intl';
import { PasswordPageProps } from 'types/page';

const Page: React.FC<PasswordPageProps> = ({ deviceId, accessToken: otpAccessToken, fullname }) => {
  const { savePassword, errorMessage, setMessage } = useAuth();
  const router = useRouter();
  const t = useTranslations();
  const [password, setPassword] = React.useState('');
  const [rePassword, setRePassword] = React.useState('');
  const handleRegister = async () => {
    if (!password || password.length < 8 || password !== rePassword) {
      setMessage(t('auth.error.wrongPassword'));
      return;
    }
    await savePassword(deviceId, otpAccessToken, password, fullname).then(
      ({ data, success, message }) => {
        if (success) {
          router.push(EntRouters.login + `?accessToken=${data.accessToken}`);
        } else {
          setMessage(message);
        }
      }
    );
  };
  const mounted = useMounted();
  if (!mounted) return null;
  return (
    <div className="container mx-auto px-4 h-full">
      <div className="flex content-center items-center justify-center h-full">
        <div className="w-full md:w-[500px] px-4">
          <div className="relative border-0 flex flex-col min-w-0 break-words w-full mb-2 ">
            <div className="flex-auto px-4 lg:px-10  pt-10">
              <EntLogoIcon />
              <div className="relative w-full mt-12 mb-10">
                <Input
                  value={password}
                  onValueChange={setPassword}
                  removeLabel={false}
                  labelPlacement={'outside'}
                  className={classNames(
                    'border-0 text-color-minor bg-bg-general rounded-none border-b border-bg-box text-sm focus:outline-none focus:ring w-full ease-linear transition-all duration-150',
                    { '!border-red-100': errorMessage !== '' }
                  )}
                  type={'password'}
                  label={t('auth.password')}
                  placeholder={'**** **** ****'}
                />
              </div>
              <div className="relative w-full mt-[45px] mb-1">
                <Input
                  value={rePassword}
                  onValueChange={setRePassword}
                  removeLabel={false}
                  labelPlacement={'outside'}
                  className={classNames(
                    'border-0 text-color-minor bg-bg-general rounded-none border-b border-bg-box text-sm focus:outline-none focus:ring w-full ease-linear transition-all duration-150',
                    { '!border-red-100': errorMessage !== '' }
                  )}
                  type={'password'}
                  label={t('auth.rePassword')}
                  placeholder={'**** **** ****'}
                />
              </div>
              {errorMessage ? <div className={'mt-2 text-red'}>{t(errorMessage)}</div> : null}
              <div className="text-center mt-10">
                <Button onClick={handleRegister} color={'primary'} size={'lg'}>
                  {t('auth.save')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page;
