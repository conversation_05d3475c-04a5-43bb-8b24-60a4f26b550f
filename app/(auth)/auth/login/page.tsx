import React from 'react';

import type { Metadata } from 'next';
import Link from 'next/link';

import EntLogoRectangle from 'components/Icons/EntLogoRectangle';
import EntRouters from 'configs/EntRouters';
import LoginForm from 'containers/auth/LoginForm';
import { useTranslations } from 'next-intl';

export const metadata: Metadata = {
  title: 'Đăng nhập',
};
const Login = () => {
  const t = useTranslations();
  return (
    <section className="w-full h-full bg-bg-general py-40 min-h-screen">
      <div className="container mx-auto px-4 h-full">
        <div className="flex content-center items-center justify-center h-full">
          <div className="w-full max-w-[490px] px-4">
            <div className="relative border-0 flex flex-col min-w-0 break-words w-full mb-2">
              <div className="flex-auto px-4 lg:px-10 pt-10">
                <EntLogoRectangle className={'w-[160px]'} />
                <LoginForm />
              </div>
            </div>
            <div className="text-center text-sm text-color-minor mt-7 !text-[12px]">
              <span>{t('auth.labelRegister')}&nbsp;</span>
              <Link href={EntRouters.register} className={'text-sm text-purple'}>
                {t('auth.linkRegister')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Login;
