import React from 'react';

export async function generateMetadata() {
  return {
    title: '<PERSON> Enter',
    description: 'lang enter',
  };
}

async function MainLayout({ children }) {
  return (
    <div className={'h-screen w-full flex-1 bg-bg-general relative'}>
      {/*<FluidCursor />*/}
      <div className="relative w-full h-full md:px-0">{children}</div>
    </div>
  );
}

export default MainLayout;
