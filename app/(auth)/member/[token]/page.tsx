'use client';

import React from 'react';

import Head from 'next/head';
import { useParams } from 'next/navigation';

import useGetMember from '@/actions/member';
import MemberContainer from '@/containers/member/MemberContainer';
import { MemberToken } from '@/types/hooks';
import { useTranslations } from 'next-intl';

const Page: React.FC<MemberToken> = () => {
  const params = useParams();
  const { member } = useGetMember((params.token as string) || '');
  const t = useTranslations();
  return (
    <>
      <Head>
        <title>
          {t('member.titlePageLogin')} - {member?.fullname}
        </title>
      </Head>
      <MemberContainer member={member} />
    </>
  );
};

export default Page;
