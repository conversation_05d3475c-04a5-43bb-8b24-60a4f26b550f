'use client';

import React, { useState } from 'react';

import OnBoardingHeader from 'containers/onboarding/OnBoardingHeader';
import OnBoardingPage from 'containers/onboarding/OnBoardingPage';
// import QuestionScreen from 'containers/onboarding/QuestionScreen';
import useOnBoarding from 'hooks/Ent/useOnBoarding';
import { useMounted } from 'hooks/common/useMounted';
import { OnboardPageProps } from 'types/page';

const OnboardPage: React.FC<OnboardPageProps> = () => {
  const { quizzQuestions } = useOnBoarding({});

  const mounted = useMounted();
  const [activeStep, setStep] = useState<number>(1);
  if (!mounted) return null;
  return (
    <>
      <div>
        {/* <OnBoardingHeader setStep={setStep} activeStep={activeStep} /> */}
        <OnBoardingHeader
          setStep={setStep}
          activeStep={activeStep}
          quizzQuestions={quizzQuestions}
        />
        <OnBoardingPage setStep={setStep} activeStep={activeStep} quizzQuestions={quizzQuestions} />
        {/* <QuestionScreen/> */}
      </div>
    </>
  );
};

export default OnboardPage;
