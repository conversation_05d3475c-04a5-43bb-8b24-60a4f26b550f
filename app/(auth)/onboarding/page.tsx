'use client';

import React, { useState } from 'react';

import OnBoardingHeader from 'containers/onboarding/OnBoardingHeader';
// import WelcomeScreen from 'containers/onboarding/WelcomeScreen';
import QuestionScreen from 'containers/onboarding/QuestionScreen';
import useOnBoarding from 'hooks/Ent/useOnBoarding';
import { useMounted } from 'hooks/common/useMounted';
import { OnboardPageProps } from 'types/page';

const OnboardPage: React.FC<OnboardPageProps> = () => {
  const { quizzQuestions } = useOnBoarding({});

  const mounted = useMounted();
  const [step, setStep] = useState<number>(1);
  if (!mounted) return null;
  return (
    <>
      <OnBoardingHeader setStep={setStep} activeStep={step} quizzQuestions={quizzQuestions} />
      {/* <WelcomeScreen /> */}
      <QuestionScreen setStep={setStep} step={step} quizzQuestions={quizzQuestions} />
    </>
  );
};

export default OnboardPage;
