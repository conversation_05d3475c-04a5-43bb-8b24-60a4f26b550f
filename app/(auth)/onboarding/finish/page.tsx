'use client';

import React from 'react';

// import OnBoardingHeader from 'containers/onboarding/OnBoardingHeader';
import FinishScreen from 'containers/onboarding/FinishScreen';
import { useMounted } from 'hooks/common/useMounted';
import { OnboardPageProps } from 'types/page';

const FinishOnboardPage: React.FC<OnboardPageProps> = () => {
  const mounted = useMounted();
  // const [step, setStep] = useState<number>(1);
  if (!mounted) return null;
  return <FinishScreen />;
};

export default FinishOnboardPage;
