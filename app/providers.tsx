'use client';

import React, { useEffect, useMemo, useState } from 'react';

import { useReportWebVitals } from 'next/web-vitals';

import { swrFetcher } from '@/services/swrFetcher';
import { AppProvider } from '@/store/contexts/AppContext';
import { HeroUIProvider } from '@heroui/system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider as NextThemesProvider, useTheme } from 'next-themes';
import { SWRConfig } from 'swr';
import swrMiddleware from 'utils/swrMiddleware';

const queryClient = new QueryClient();

export const Providers = ({ children, session }) => {
  const { theme } = useTheme();
  useReportWebVitals(() => {
    //console.log(metric);
  });
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  // Memoize SWR config to prevent unnecessary rerenders
  const swrConfig = useMemo(
    () => ({
      revalidateIfStale: false,
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      fetcher: swrFetcher,
      use: [swrMiddleware(session?.accessToken || null)],
    }),
    [session?.accessToken]
  );

  if (!isLoaded) {
    return null;
  }
  return (
    <SessionProvider session={session}>
      <SWRConfig value={swrConfig}>
        <QueryClientProvider client={queryClient}>
          <AppProvider>
            <HeroUIProvider>
              <NextThemesProvider enableSystem={false} defaultTheme={theme}>
                {children}
              </NextThemesProvider>
            </HeroUIProvider>
          </AppProvider>
        </QueryClientProvider>
      </SWRConfig>
    </SessionProvider>
  );
};
