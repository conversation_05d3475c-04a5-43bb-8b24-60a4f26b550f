import React from 'react';

import { Providers } from '@/app/providers';
import { auth } from '@/auth';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages, setRequestLocale } from 'next-intl/server';
import 'styles/globals.css';
import 'styles/tailwind_base.css';
import 'utils/axios.config';

export async function generateMetadata() {
  return {
    title: 'Lang Enter',
    description: 'lang enter',
  };
}

async function RootLayout({ children }) {
  const locale = await getLocale();
  const messages = await getMessages();
  const session = await auth();
  setRequestLocale(locale);
  return (
    <html lang={locale}>
      <head>
        <link rel="stylesheet" href="https://file.langenter.com/config/fontello/css/fontello.css" />
        <link rel="shortcut icon" href="/favicon.png" />
        <meta
          name="viewport"
          content="width=device-width,height=device-height,initial-scale=1,maximum-scale=1,user-scalable=no,shrink-to-fit=no,viewport-fit=cover"
        />
        <meta name="apple-mobile-web-app-capable" content="yes" />
      </head>
      <body>
        <NextIntlClientProvider messages={messages} locale={locale}>
          <Providers session={session}>{children}</Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}

export default RootLayout;
