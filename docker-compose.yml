version: '3.8'

services:
  longan:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - GIT_TAG=${GIT_TAG:-latest}
        - GIT_COMMIT=${GIT_COMMIT:-unknown}
        - COMPILED_AT=${COMPILED_AT:-2025-04-15T07:28:21Z}
        - NODE_ENV=${NODE_ENV:-dev}
    ports:
      - '127.0.0.1:3000:3000'
    environment:
      - NODE_ENV=${NODE_ENV:-dev}
      - PORT=3000
    restart: unless-stopped
