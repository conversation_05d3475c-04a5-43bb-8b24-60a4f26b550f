import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./utils/i18n.request.ts');

const nextConfig = {
  reactStrictMode: false,
  env: {
    IMAGE_DOMAIN_LOCALHOST: process.env.IMAGE_DOMAIN_LOCALHOST,
    IMAGE_DOMAIN_FILE_CDN: process.env.IMAGE_DOMAIN_FILE_CDN,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'file.langenter.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  experimental: {
    turbo: {},
  },
  // webpack: (config, { dev }) => {
  //   if (config.cache && !dev) {
  //     config.cache = Object.freeze({
  //       type: 'memory',
  //     });
  //   }
  //   // Important: return the modified config
  //   return config;
  // },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
};

export default withNextIntl(nextConfig);
