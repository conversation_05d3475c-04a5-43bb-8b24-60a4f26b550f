/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin');
const { heroui } = require('@heroui/react');
const { WidthEnum } = require('./configs/WidthEnum');

module.exports = {
  mode: 'jit',
  content: [
    './app/**/*.{js,ts,jsx,tsx}',
    './components/**/**/*.{js,ts,jsx,tsx}',
    './containers/**/**/*.{js,ts,jsx,tsx}',
    './layout/*.{js,ts,jsx,tsx}',
    './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        'bg-general': 'rgb(var(--bg-general) / <alpha-value>)',
        'bg-box': 'rgb(var(--bg-box) / <alpha-value>)',
        'bg-button': 'rgb(var(--bg-button) / <alpha-value>)',
        'color-line': 'rgb(var(--color-line) / <alpha-value>)',
        'color-border': 'rgb(var(--color-border) / <alpha-value>)',
        'color-major': 'rgb(var(--color-major) / <alpha-value>)',
        'color-minor': 'rgb(var(--color-minor) / <alpha-value>)',
        'circle-checkbox': '#dee1e4',
        primary: {
          DEFAULT: 'rgb(var(--primary) / <alpha-value>)',
          100: 'var(--primary-100)',
          200: 'var(--primary-200)',
        },
        purple: {
          DEFAULT: 'var(--purple)',
          100: 'var(--purple-100)',
        },
        black: {
          DEFAULT: 'var(--black)',
        },
        white: {
          DEFAULT: 'var(--white)',
        },
        yellow: {
          DEFAULT: 'var(--yellow)',
          100: 'var(--yellow-100)',
        },
        red: {
          DEFAULT: 'var(--red)',
          100: 'var(--red-100)',
        },
      },
      fontFamily: {
        family: 'var(--family)',
        svn: 'SVN-Poppins',
      },
      screens: {
        'max-xs': { max: '389px' },
        'max-xxs': { max: '350px' },
        'max-lg': { max: '1023px' },
        'max-md': { max: '767px' },
        'max-sm': { max: '639px' },
        'ip-se': { max: '376px' },
      },
      textShadow: {},
      width: {
        panel: WidthEnum.PANEL,
        'panel-inner': `calc(${WidthEnum.PANEL} - 2rem)`,
      },
      zIndex: {
        hero: 1,
        footer: 10,
        modal: 12,
        header: 11,
      },
      minHeight: (theme) => ({
        ...theme('spacing'),
      }),
      boxShadow: {
        md: '0px 1px 2px 0px rgba(0,0,0, 0.1)',
        lg: '0px 1px 4px rgba(0,0,0,0.1)',
      },
    },
  },
  plugins: [
    heroui({
      prefix: 'next', // prefix for themes variables
      addCommonColors: true, // override common colors (e.g. "blue", "green", "pink").
      defaultTheme: 'light', // default theme from the themes object
      defaultExtendTheme: 'light', // default theme to extend on custom themes
      layout: {
        spacingUnit: 4, // in px
        disabledOpacity: 0.5, // this value is applied as opacity-[value] when the component is disabled
        dividerWeight: '1px', // h-divider the default height applied to the divider component
        fontSize: {
          tiny: '0.75rem', // text-tiny
          small: '0.875rem', // text-small
          base: '1rem', // text-base
          medium: '0.8125rem', // text-medium
          large: '1.125rem', // text-large
        },
        lineHeight: {
          tiny: '1rem', // text-tiny
          small: '1.25rem', // text-small
          medium: '1.5rem', // text-medium
          large: '1.75rem', // text-large
        },
        radius: {
          tiny: '1px', // rounded-small
          small: '4px', // rounded-small
          medium: '5px', // rounded-medium
          large: '14px', // rounded-large
        },
        borderWidth: {
          tiny: '0.5px', // border-small
          small: '1px', // border-small
          medium: '1px', // border-medium (default)
          large: '4px', // border-large
        },
        borderRadius: {
          normal: '5px', // rounded
        },
        boxShadow: {
          small: '0px 1px 0px 0px rgba(0, 0, 0, 0.09)',
          medium: '0px 1px 4px 0px rgba(0, 0, 0, 0.09)',
          large: '0px 2px #FFF',
        },
      },
    }),
    require('tailwind-scrollbar'),
    plugin(function ({ matchUtilities, theme }) {
      matchUtilities(
        {
          'text-shadow': (value) => ({
            textShadow: value,
          }),
        },
        { values: theme('textShadow') }
      );
    }),
  ],
  corePlugins: {
    preflight: false,
  },
};
