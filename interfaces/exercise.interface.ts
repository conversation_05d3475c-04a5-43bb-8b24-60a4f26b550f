import { AnswerFormat, AnswerType, QUESTION_CONFIGS, QuestionDisplay } from 'configs/Exercise';

interface BaseResponse {
  status: number;
}

export interface ExercisesResponse extends BaseResponse {
  data: {
    questions: Question[];
  };
}

export interface Question {
  id: number;
  quiz_id: number;
  content: string;
  type: number;
  status: number;
  question_format: keyof typeof QUESTION_CONFIGS;
  answer_format: AnswerFormat;
  display: QuestionDisplay;
  audio?: string;
  image?: string;
  extra?: string;
}

export interface ExerciseResponse extends BaseResponse {
  data: {
    answers: Answer[];
  };
}

export interface Answer {
  id: number;
  question_id: number;
  content: string;
  image?: string;
  audio?: string;
  type: number;
  status: number;
}

export interface SubmitAnswerResponse extends BaseResponse {
  data: {
    answers: SubmitAnswerData[];
    is_correct: AnswerType;
  };
}

export interface SubmitAnswerData extends Omit<Answer, 'audio'> {
  right: AnswerType;
}
