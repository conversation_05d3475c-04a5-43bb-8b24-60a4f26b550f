export interface SentenceGroupResponse {
  data: DataSentenceGroup;
  status: number;
}

export interface DataSentenceGroup {
  sentence_groups: SentenceGroup[];
}

export interface SentenceGroup {
  id: number;
  course_id: number;
  document_id: number;
  paragraph_id: number;
  keyword: string;
  emotion: string;
  emoji: string;
  keyx: string;
  format: string;
  image: string;
  status: number;
  process_image: number;
  created_at: number;
  position?: number;
  updated_at: number;
  image_time: number;
}

export interface SentenceResponse {
  success: boolean;
  message: string;
  data: DataSentence;
}

export interface DataSentence {
  sentences: Sentence[];
  total: number;
  characters: Character[];
  paragraph_current_id: number;
  listen_sentence_current_id: number;
}

export interface Sentence {
  id: number;
  course_id: number;
  document_id: number;
  paragraph_id: number;
  character_id: number;
  content: string;
  process_audio: number;
  status: number;
  audios: Audio[];
  translation: Translation;
  color: string;
  emotion: string;
  emoji: string;
  sentence_group_id: number;
  score?: number;
}

export interface Audio {
  id: number;
  sentence_id: number;
  voice_id: number;
  transcription_id: string;
  speed: number;
  duration: number;
  url: string;
  process: number;
  created_at: number;
  status: number;
  keyx: string;
  format: string;
  version: string;
}

export interface Translation {
  id: number;
  item: string;
  object_id: number;
  content: string;
  translate_google: string;
  lang_from: string;
  lang_to: string;
  field: string;
  status: number;
  created_at: number;
}

export interface Character {
  id: number;
  create_id: number;
  course_id: number;
  document_id: number;
  paragraph_id: number;
  fullname: string;
  gender: string;
  sentence_current_id: number;
}
