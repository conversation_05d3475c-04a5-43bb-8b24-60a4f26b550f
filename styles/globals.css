@tailwind components;
@tailwind utilities;

button {
  outline: none;
}

input[type='range']::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #dee1e4;
  margin: 0;
  height: 8px;
  width: 8px;
  border-top-right-radius: 100%;
  border-bottom-right-radius: 100%;
  top: -10px;
  padding: 0;
  cursor: pointer;
  /*opacity: 0;*/
}
.mask-image-none {
  mask-image: none !important;
}
.group[data-focus='true'] .group-data-\[focus\=true\]\:border-default-foreground {
  outline: none !important;
  border: 0 !important;
  background-color: transparent !important;
}

.add-item[data-focus='true'] .add-item-data-\[focus\=true\]\:border-default-foreground {
  outline: none !important;
  border: 0 !important;
  background-color: transparent !important;
}

@font-face {
  font-family: 'Inter';
  src: url('../public/font/Inter/static/Inter-Black.ttf');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../public/font/Inter/static/Inter-Bold.ttf');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../public/font/Inter/static/Inter-Medium.ttf');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../public/font/Inter/static/Inter-Regular.ttf');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Inter';
  src: url('../public/font/Inter/static/Inter-Light.ttf');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('../public/font/SVN-Poppins/TTF/SVN-Poppins-Regular.ttf');
  font-weight: 400;
  font-style: normal;
}

body {
  font-size: 13px;
}

@-webkit-keyframes autofill {
  to {
    color: #666;
    background: transparent;
  }
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  background: transparent !important;
  -webkit-animation-name: autofill;
  -webkit-animation-fill-mode: both;
  -webkit-box-shadow: 0 0 0 0 transparent inset !important;
}

[class^='icon-'],
[class*=' icon-'] {
  font-family: 'fontello';
  line-height: 1;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

[class^='icon-']:before,
[class*=' icon-']:before {
  margin-left: 0;
  margin-right: 0;
  line-height: unset;
  font-family: 'fontello';
  font-style: normal;
  font-weight: normal;
  speak: never;
  display: inline;
  text-decoration: inherit;
  text-align: center;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Uncomment for 3D effect */
  /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.text-base {
  font-size: 1rem;
}

:root {
  --primary: 0 191 85;
  --primary-100: #01ae4e;
  --primary-200: #58cc02;
  --purple: #2b398e;
  --purple-100: #6e79d6;
  --black: #000000;
  --white: #ffffff;
  --yellow: #f1c94b;
  --yellow-100: #feb041;
  --red: #ff3b30;
  --red-100: #ff7c75;
}

/* Ent theme */
html[data-theme='light'] {
  --family: 'Inter', sans-serif;
  --bg-general: 255 255 255;
  --bg-box: 244 245 248;
  --bg-button: 255 255 255;
  --color-line: 238 241 244;
  --color-border: 240 240 240;
  --color-major: 40 42 48;
  --color-minor: 133 134 153;
}

/* Ent2 theme */
html[data-theme='dark'] {
  --family: 'Inter', sans-serif;
  --bg-general: 25 26 35;
  --bg-box: 42 45 60;
  --bg-button: 56 59 76;
  --color-line: 44 45 60;
  --color-border: 31 33 44;
  --color-major: 238 239 252;
  --color-minor: 107 111 118;
}

.scroll-area::-webkit-scrollbar {
  width: 8px;
  -webkit-appearance: none;
  height: 3px;
}

.scroll-area::-webkit-scrollbar-thumb {
  background-color: rgb(var(--color-line));
  box-shadow: inset 0 0 6px rgb(var(--color-line)) !important;
  border-radius: 4px;
  height: 50px;
}

.scroll-area::-webkit-scrollbar-track {
  background-color: rgb(var(--bg-general));
  height: 5px !important;
}

::-webkit-scrollbar {
  width: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}
textarea:focus {
  outline: none !important;
  border-color: #719ece;
}
