import { useEffect, useState } from 'react';

import { ApiEndpoints } from 'configs';
import useSWRImmutable from 'swr/immutable';
import { GroupAssignMemberList } from 'types/model';

type GroupMemberListProps = {
  offset: number;
  groupId: number;
  shouldNotFetch: boolean;
};
const useGroupMemberList = (params: GroupMemberListProps) => {
  const [groupMemberList, setGroupMemberList] = useState<Array<GroupAssignMemberList>>([]);

  const { data: dataGroupMembers, isValidating } = useSWRImmutable(
    !params.shouldNotFetch
      ? `${ApiEndpoints.ADD_GET_GROUPS_BY_TOKEN}?group_id=${params.groupId}&includes=total`
      : null
  );
  const groupMembers = dataGroupMembers?.data?.member_groups || [];
  useEffect(() => {
    if (groupMembers.length) {
      if (params.offset === 0) setGroupMemberList(groupMembers);
      else setGroupMemberList([...groupMemberList, ...groupMembers]);
    }
  }, [groupMembers.length, params.offset]);

  return {
    groupMemberList,
    isValidating,
  };
};

export default useGroupMemberList;
