import { ApiEndpoints } from 'configs';
import useSWR from 'swr';

import { useSession } from '../useSession';

const useGroupMember = (id: number) => {
  const { data: session } = useSession();
  const hasMembers = session?.members && session.members.length > 0; // Kiểm tra members có dữ liệu không

  const queryParams = hasMembers ? '?includes=account' : '';
  const { data, isLoading, mutate } = useSWR(
    id > 0 ? `${ApiEndpoints.GET_GROUPS_BY_ID}/${id}${queryParams}` : null,
    {
      entity: 'groupMember',
      revalidateOnMount: true,
    }
  );

  return {
    group: data?.data || null,
    message: data?.message || '',
    isLoading,
    mutate,
  };
};

export default useGroupMember;
