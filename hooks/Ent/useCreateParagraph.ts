import { ApiEndpoints } from '@/configs';
import { QUERY_KEY } from '@/constant/query-key';
import axiosConfig from '@/utils/axios.config';
import { UseMutationOptions, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError, AxiosResponse } from 'axios';

interface CreateParagraphParams {
  document_id: number;
  course_id: number;
  title: string;
  item: string;
  description: string;
  pre_paragraph_id: number;
}

export const useCreateParagraphMutation = (
  options?: Omit<UseMutationOptions<AxiosResponse, AxiosError, CreateParagraphParams>, 'mutationFn'>
) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: CreateParagraphParams) => {
      return await axiosConfig.post(ApiEndpoints.PARAGRAPH_LIST, {
        ...payload,
      });
    },
    ...options,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEY.PARAGRAPH] });
    },
  });
};
