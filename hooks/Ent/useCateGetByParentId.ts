import { ApiEndpoints } from 'configs';
import useSWR from 'swr';
import { CategoryEntity } from 'types/model';

const useCateGetByParentId = (parent_ids: string, shouldNotFetch = false, includes?: string) => {
  const response = useSWR(
    shouldNotFetch
      ? null
      : `${ApiEndpoints.CATEGORY_LIST}?status=2&${
          typeof includes !== 'undefined' && includes !== '' ? 'includes=' + includes + '&' : ''
        }parent_ids=${parent_ids}`
  );
  const { data } = response;

  // generate tree list with root is items have parent_id  in list of parent_ids. with item haven't parent_id in list of parent_ids push each item to childrenOf with key is parent_id.
  const buildTree = (items: CategoryEntity[]) => {
    const idToItem = new Map();
    const parentIdToChildren = new Map();
    // First pass: create a map of id to item, and parent_id to children
    items.forEach((item: CategoryEntity) => {
      idToItem.set(item.id, { ...item });
      if (!parentIdToChildren.has(item.parent_id)) {
        parentIdToChildren.set(item.parent_id, []);
      }
      parentIdToChildren.get(item.parent_id).push(idToItem.get(item.id));
    });

    // Second pass: set each item's children and find the root items
    const tree: CategoryEntity[] = [];
    items.forEach((item: CategoryEntity) => {
      const currentItem: CategoryEntity = idToItem.get(item.id);
      currentItem.items = parentIdToChildren.get(item.id) || [];
      if (!idToItem.has(item.parent_id)) {
        tree.push(currentItem);
      }
    });
    return tree;
  };

  return {
    categories: buildTree(data?.data?.categories || []),
    message: data?.message || null,
  };
};
export default useCateGetByParentId;
