import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { HistoryParagraphEntity, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useTransactionParagraph = () => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.HISTORY_PARAGRAPH}?includes=total&limit=${DEFAULT_RECORD_PER_PAGE}&offset=${pageIndex}`;
  };

  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'paragraphs',
    revalidateOnMount: true,
  });
  return {
    paragraphList: (items as HistoryParagraphEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};

export default useTransactionParagraph;
