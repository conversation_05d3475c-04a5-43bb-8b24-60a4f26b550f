import { BALANCE_MIN } from '@/configs';
import { StatusEnum } from '@/configs/StatusEnum';
import useBalanceStore from '@/store/balance';
import { useLocale } from 'next-intl';

const updateBalance = () => {
  const { setBalance, setBalanceString, setBalanceStatus } = useBalanceStore();
  const locale = useLocale();
  return (balance: number = 0) => {
    setBalanceString(balance.toLocaleString(locale, { minimumFractionDigits: 0 }));
    setBalance(balance);
    setBalanceStatus(balance >= BALANCE_MIN ? StatusEnum.ON : StatusEnum.OFF);
  };
};

export default updateBalance;
