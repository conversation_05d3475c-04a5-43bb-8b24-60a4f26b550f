import { apiRequest } from '@/services';
import { EditSentenceItem } from '@/types/component';
import { UpsertCharacterEntity } from '@/types/hooks';
import { ApiEndpoints } from 'configs';
import useSWR from 'swr';
import { Character } from 'types/model';

const useCharacter = (fullname: string, sentence: EditSentenceItem) => {
  const { data } = useSWR(
    fullname !== undefined && fullname.length > 1
      ? `${ApiEndpoints.CHARACTER_LIST}?fullname=${fullname}&course_id=${sentence.course_id}&limit=30&status=2`
      : null,
    {
      revalidateOnMount: true,
    }
  );

  const upsertCharacter = async (character: UpsertCharacterEntity) => {
    return await apiRequest(ApiEndpoints.CHARACTER_UPSERT, {
      data: character,
      method: 'put',
    });
  };
  return {
    characters: (data?.data.characters as Character[]) || [],
    upsertCharacter,
  };
};
export default useCharacter;
