import { ApiEndpoints } from 'configs';
import { apiRequest } from 'services';

const useAddMemberGroups = () => {
  const doAddMemberGroups = async (group_id: number) => {
    const response = await apiRequest(ApiEndpoints.ADD_GET_GROUPS_BY_TOKEN, {
      cache: true,
      method: 'post',
      data: { group_id: group_id },
    });
    return response;
  };
  const doInviteMembersGroup = async (dataMember: any) => {
    const response = await apiRequest(ApiEndpoints.INVATE_MEMBERS_IN_GROUP, {
      cache: true,
      method: 'post',
      data: dataMember,
    });
    return response;
  };

  return {
    doAddMemberGroups,
    doInviteMembersGroup,
  };
};
export default useAddMemberGroups;
