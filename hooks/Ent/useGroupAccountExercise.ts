import { StatusEnum } from '@/configs/StatusEnum';
import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { apiRequest } from 'services';
import { AccountExercisesList, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useGroupAccountExercise = (groupId = 0) => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if ((pageIndex > 0 && !previousPageData) || groupId === 0) return null;
    return `${ApiEndpoints.GROUP_ACCOUNT_EXERCISES}?includes=total&limit=${DEFAULT_RECORD_PER_PAGE}&group_id=${groupId}&status=${StatusEnum.ON}&offset=${pageIndex}`;
  };
  const { items, isLoading, isReachingEnd, setPage, page, mutate } = usePaginatedSWR(getKey, {
    entity: 'account_exercises',
  });
  return {
    accountExercisesList: (items as AccountExercisesList[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
    reFetch: mutate,
  };
};

export const useAddGroupAssignAccount = (reFetch) => {
  const addGroupAssignAccount = async (dataAssign) => {
    await apiRequest(ApiEndpoints.GROUP_ACCOUNT_EXERCISES, {
      cache: true,
      method: 'post',
      data: dataAssign,
    });
    if (reFetch) {
      await reFetch();
    }
  };

  return { addGroupAssignAccount };
};

export default useGroupAccountExercise;
