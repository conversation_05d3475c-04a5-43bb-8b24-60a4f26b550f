import { ApiEndpoints } from '@/configs';
import { UseReportTokenProps } from '@/types/hooks';
import { MemberReportsEntity } from '@/types/model';
import useSWRImmutable from 'swr/immutable';

const useReportMemberToken = (params: UseReportTokenProps, shouldNotFetch: boolean) => {
  // @ts-ignore
  const queryParamsToken = new URLSearchParams(params);
  const { data, isLoading } = useSWRImmutable(
    shouldNotFetch ? null : `${ApiEndpoints.REPORT_MEMBER_TOKEN}?${queryParamsToken}`
  );
  return {
    reportsData: ((data?.data ?? null) as MemberReportsEntity) || null,
    isLoading,
  };
};
export default useReportMemberToken;
