import { useState } from 'react';

import { ApiEndpoints } from 'configs';
import { apiRequest } from 'services';

const useParagraphApprove = () => {
  const [errorMessage, setMessage] = useState('');

  const doUpdateByID = async (id: number, process_approve: number) => {
    return await apiRequest(ApiEndpoints.APPROVED_PARAGRAPH, {
      cache: true,
      method: 'post',
      data: {
        id: id,
        process_approve: process_approve,
      },
    });
  };

  return {
    doUpdateByID,
    errorMessage,
    setMessage,
  };
};
export default useParagraphApprove;
