import { ApiEndpoints } from 'configs';
// import useSWR from 'swr';
import useSWRImmutable from 'swr/immutable';
import { PronounceEntity } from 'types/model';

const usePronounce = (pronounce_id: number = 0) => {
  //sử dụng để dịch nghĩa trong block "Tra từ"
  const { data, isValidating } = useSWRImmutable(
    pronounce_id > 0 ? `${ApiEndpoints.GET_PRONOUNCE_ID}/${pronounce_id}` : null
  );
  const _pronounce: PronounceEntity = data?.data || null;

  //sử dụng lấy thông tin trong block :C<PERSON><PERSON> từ
  //   const { data, isValidating:isValidating } = useSWR(sentence_id > 0 ? `${ApiEndpoints.TRANSLATE_SENTENCE}?id=${sentence_id}`:null);
  //   const sentenceDetails: SentenceDetail[] | null = data?.data?.sentence_poses || null;
  //   const sentencePhrases: SentencePhrase[] | null = data?.data?.phrases || null;
  return {
    // sentenceDetails,
    // sentencePhrases,
    // isLoadingPos: isValidating,
    isLoadingSentence: isValidating,
    pronounce: _pronounce,
  };
};
export default usePronounce;
