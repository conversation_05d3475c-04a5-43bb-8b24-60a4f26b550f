import { ApiEndpoints } from 'configs';
import { apiRequest } from 'services';

const useCreatedGroups = () => {
  const doCreatedGroups = async (
    group_id: number,
    type: number | undefined,
    parent_id: number,
    title: string
  ) => {
    const response = await apiRequest(ApiEndpoints.GET_GROUPS_BY_TOKEN, {
      cache: true,
      method: 'post',
      data: { group_id: group_id, title: title, type: type, parent_id: parent_id },
    });
    return response;
  };

  return {
    doCreatedGroups,
  };
};
export default useCreatedGroups;
