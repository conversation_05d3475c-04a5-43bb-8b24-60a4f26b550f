import { useEffect, useState } from 'react';

const usePlans = () => {
  const [plans, setPlans] = useState<Array<any>>([]);
  const [selectedPlan, setPlan] = useState<any>({ id: 2 });
  const [plansTitle, setPlanTitle] = useState<Array<any>>([]);

  useEffect(() => {
    setPlanTitle([
      {
        title: 'Dung lượng',
        features: [
          {
            key: 'totalMember',
            value: 'Số thành viên',
          },
          {
            key: 'totalCourse',
            value: 'Số bài học',
          },
          {
            key: 'totalReadingCourse',
            value: 'Số bài luyện nói',
          },
          {
            key: 'totalHomework',
            value: 'Số bài tập',
          },
        ],
      },
      {
        title: 'Tính năng',
        features: [
          {
            key: 'hiddenConversation',
            value: 'Ẩn nội dung hội thoại',
          },
          {
            key: 'passiveLearning',
            value: '<PERSON><PERSON><PERSON> thụ động',
          },
          {
            key: 'trainExam',
            value: '<PERSON>yện đề thi',
          },
          {
            key: 'chooseVoice',
            value: 'Chọn giọng đọc',
          },
          {
            key: 'customConversation',
            value: 'Tự thêm hội thoại',
          },
        ],
      },
      {
        title: 'Phân tích & Báo cáo',
        features: [
          {
            key: 'historyLearn',
            value: 'Lịch sử học tập',
          },
          {
            key: 'historyWord',
            value: 'Các từ đã học',
          },
          {
            key: 'suggestCourse',
            value: 'Gợi ý bài giảng dựa trên trình độ',
          },
        ],
      },
    ]);
    setPlans([
      {
        id: 1,
        title: 'Miễn phí',
        price: '0đ',
        description: '',
        totalMember: 'Không giới hạn',
        totalCourse: '1 bài / ngày',
        totalReadingCourse: null,
        totalHomework: '',
        hiddenConversation: '',
        passiveLearning: '',
        trainExam: '',
        chooseVoice: '',
        customConversation: '',
        historyLearn: '',
        historyWord: '',
        suggestCourse: '',
      },
      {
        id: 2,
        title: 'Tiêu chuẩn',
        price: '500.000đ',
        description: '1 thành viên / 1 năm',
        totalMember: 'Không giới hạn',
        totalCourse: 'Không giới hạn',
        totalReadingCourse: 'Không giới hạn',
        totalHomework: '',
        hiddenConversation: 'Không giới hạn',
        passiveLearning: 'Không giới hạn',
        trainExam: 'Không giới hạn',
        chooseVoice: 'Không giới hạn',
        customConversation: '',
        historyLearn: 'Không giới hạn',
        historyWord: '',
        suggestCourse: '',
      },
      {
        id: 3,
        title: 'Cao cấp',
        price: '1.500.000đ',
        description: '1 thành viên / 1 năm',
        totalMember: 'Không giới hạn',
        totalCourse: 'Không giới hạn',
        totalReadingCourse: 'Không giới hạn',
        totalHomework: 'Không giới hạn',
        hiddenConversation: 'Không giới hạn',
        passiveLearning: 'Không giới hạn',
        trainExam: 'Không giới hạn',
        chooseVoice: 'Không giới hạn',
        customConversation: 'Không giới hạn',
        historyLearn: 'Không giới hạn',
        historyWord: 'Không giới hạn',
        suggestCourse: 'Không giới hạn',
      },
    ]);
  }, []);

  return {
    plans,
    plansTitle,
    selectedPlan,
    setPlan,
  };
};
export default usePlans;
