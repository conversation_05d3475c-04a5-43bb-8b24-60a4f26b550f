import { useState } from 'react';

import { ChartItem } from '@/types/component';
import { ReportsEntity } from '@/types/model';
import { eachDayOfInterval, endOfMonth, format, startOfMonth } from 'date-fns';
import { useTheme } from 'next-themes';

const useChartConfig = () => {
  const { theme } = useTheme();
  const [colors] = useState({
    titleColor: theme === 'dark' ? 'rgb(238 239 252)' : 'rgb(40 42 48)', // Màu tiêu đề
    bodyColor: theme === 'dark' ? 'rgb(238 239 252)' : 'rgb(40 42 48)', // Màu nội dung
    borderColor: theme === 'dark' ? 'rgb(31 33 44)' : 'rgb(240 240 240)', // Màu
    barColor: {
      BLUE: 'rgba(74, 161, 129, 1)',
      GREEN: 'rgba(107,212,103,255)',
      YELLOW: 'rgba(254, 176, 65, 1)',
      RED: 'rgba(235, 87, 86, 1)',
    },
  });

  const makeBarDate = (data: ReportsEntity[] = [], colors) => {
    const charts: ChartItem[] = [];
    const timestamps = data?.map((item) => item.day * 86400 * 1000) ?? [];

    const minDate = new Date(Math.min(...timestamps));
    const maxDate = new Date(Math.max(...timestamps));

    const startOfCurrentMonth = startOfMonth(minDate);
    const endOfCurrentMonth = endOfMonth(maxDate);

    const unixDay = (time) =>
      Math.floor(Date.UTC(time.getFullYear(), time.getMonth(), time.getDate()) / 86400000);

    // 🔹 Tạo danh sách tất cả các ngày trong tháng
    const allDays = eachDayOfInterval({
      start: startOfCurrentMonth,
      end: endOfCurrentMonth,
    });

    const initBar = colors.reduce((item, color) => {
      item[color] = 0;
      return item;
    }, {});

    allDays.forEach((date) => {
      const dayKey = format(date, 'dd');
      charts.push({ day: dayKey, date: unixDay(date), isActive: false, ...initBar });
    });
    return charts as ChartItem[];
  };
  const makeBarDateToken = (data: ReportsEntity[] = [], colors) => {
    const charts: ChartItem[] = [];

    // Add null check for empty data array
    if (!data || data.length === 0) {
      return charts;
    }

    const timestamps = data?.map((item) => item.created_at * 1000) ?? [];

    // Add check for empty timestamps
    if (timestamps.length === 0) {
      return charts;
    }

    const minDate = new Date(Math.min(...timestamps));
    const maxDate = new Date(Math.max(...timestamps));

    const startOfCurrentMonth = startOfMonth(minDate);
    const endOfCurrentMonth = endOfMonth(maxDate);

    // 🔹 Tạo danh sách tất cả các ngày trong tháng
    const allDays = eachDayOfInterval({
      start: startOfCurrentMonth,
      end: endOfCurrentMonth,
    });

    const initBar = colors.reduce((item, color) => {
      item[color] = 0;
      return item;
    }, {});

    // Tạo map để lưu amount theo ngày
    const amountByDay = {};
    data.forEach((item) => {
      const date = new Date(item.created_at * 1000);
      const day = date.getDate();
      amountByDay[day] = item.amount;
    });

    allDays.forEach((date) => {
      const day = date.getDate();
      const dayKey = format(date, 'dd');
      const hasData = amountByDay[day] !== undefined;

      charts.push({
        day: dayKey,
        date: day - 1, // Trừ 1 để bắt đầu từ 0
        isActive: hasData,
        ...initBar,
        ...(hasData && { BLUE: amountByDay[day] }),
      });
    });

    return charts as ChartItem[];
  };

  const makeBarDatePoint = (data: ReportsEntity[] = [], colors) => {
    const charts: ChartItem[] = [];
  
    if (!data || data.length === 0) {
      return charts;
    }
  
    const timestamps = data?.map((item) => item.created_at * 1000) ?? [];
  
    if (timestamps.length === 0) {
      return charts;
    }
  
    const minDate = new Date(Math.min(...timestamps));
    const maxDate = new Date(Math.max(...timestamps));
  
    const startOfCurrentMonth = startOfMonth(minDate);
    const endOfCurrentMonth = endOfMonth(maxDate);
  
    const allDays = eachDayOfInterval({
      start: startOfCurrentMonth,
      end: endOfCurrentMonth,
    });
  
    const initBar = colors.reduce((item, color) => {
      item[color] = 0;
      return item;
    }, {});
  
    // Map to store amounts by day and point type
    const amountByDay = {};
    data.forEach((item) => {
      const date = new Date(item.created_at * 1000);
      const day = date.getDate();
      if (!amountByDay[day]) {
        amountByDay[day] = {
          RED: 0,    // point 1
          YELLOW: 0, // point 2
          BLUE: 0    // point 3
        };
      }
      // Map point values to colors
      if (item.point === 1) amountByDay[day].RED = item.amount;
      if (item.point === 2) amountByDay[day].YELLOW = item.amount;
      if (item.point === 3) amountByDay[day].BLUE = item.amount;
    });
  
    allDays.forEach((date) => {
      const day = date.getDate();
      const dayKey = format(date, 'dd');
      const hasData = amountByDay[day] !== undefined;
  
      charts.push({
        day: dayKey,
        date: day - 1,
        isActive: hasData,
        ...initBar,
        ...(hasData && amountByDay[day])
      });
    });
  
    return charts as ChartItem[];
  };
  return {
    colors,
    makeBarDate,
    makeBarDateToken,
    makeBarDatePoint,
  };
};

export default useChartConfig;
