import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { UseMemberExerciseProps } from 'types/hooks';
import { GroupMemberExercisesList, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useMemberExercise = (params: UseMemberExerciseProps) => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (
      (pageIndex > 0 && !previousPageData) ||
      params.groupId === undefined ||
      params.groupId === 0
    )
      return null;

    let url = `${ApiEndpoints.GROUP_ASSIGN_MEMBER}?includes=total&group_id=${params?.groupId}&offset=${pageIndex}&limit=${DEFAULT_RECORD_PER_PAGE}`;
    if (params?.memberId) {
      url += `&account_exercise_id=${params.memberId}`;
    }
    return url;
  };
  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'member_exercises',
  });
  return {
    memberExercisesList: (items as GroupMemberExercisesList[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};

export default useMemberExercise;
