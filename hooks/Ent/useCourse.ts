import { UseCourseProps } from '@/types/hooks';
import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { CourseEntity, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useCourse = (params: UseCourseProps) => {
  const { id, limit = DEFAULT_RECORD_PER_PAGE } = params;
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if ((pageIndex && !previousPageData) || id === 0) return null;
    return `${ApiEndpoints.COURSE_LIST}?id=${id}&includes=total&limit=${limit}&offset=${pageIndex}`;
  };

  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'courses',
  });

  return {
    courseList: items as CourseEntity[],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};
export default useCourse;
