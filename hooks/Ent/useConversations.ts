import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { QUERY_KEY } from '@/constant/query-key';
import { IDataSentenceResponse } from '@/interfaces/conversation.interface';
import { TransactionLearnInfo } from '@/types/model';
import axiosConfig from '@/utils/axios.config';
import { UseMutationOptions, useMutation, useQuery } from '@tanstack/react-query';
import { AxiosError, AxiosResponse } from 'axios';
import { ApiEndpoints } from 'configs';

export const useConversations = ({
  paragraphId,
  documentId,
  courseId,
  exerciseToken,
  type,
}: {
  paragraphId: number;
  documentId: number;
  courseId: number;
  exerciseToken?: string;
  type: LearnTypeEnum;
}) => {
  let params = {
    includes: 'total',
    paragraph_id: paragraphId,
    document_id: documentId,
    course_id: courseId,
    exercise_token: exerciseToken,
  };

  const apiUrl =
    type === LearnTypeEnum.APPROVE ? ApiEndpoints.SENTENCE_LIST_V2 : ApiEndpoints.SENTENCE_LIST_V1;
  return useQuery({
    queryKey: [QUERY_KEY.CONVERSATIONS, paragraphId, documentId, courseId, exerciseToken, type],
    queryFn: async (): Promise<IDataSentenceResponse> => {
      const response = await axiosConfig.get(apiUrl, {
        params,
      });
      return response.data;
    },
    enabled: !!paragraphId && !!documentId && !!courseId,
    staleTime: Infinity,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchInterval: false,
    refetchIntervalInBackground: false,
  });
};

export const useApproveConversationMutation = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        paragraph_id: number;
        process_approve: number;
      }
    >,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: async (payload) => {
      return axiosConfig.post(`${ApiEndpoints.APPROVED_PARAGRAPH}`, payload);
    },
    ...options,
  });
};

export const useStartListenMutation = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        document_id: number;
        course_id: number;
        paragraph_id: number;
        activeTab?: string;
        member_exercise_token?: string;
      }
    >,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: async (payload) => {
      const { activeTab, ...restParams } = payload;
      const typeValue = activeTab === LearnTypeEnum.LISTEN ? 2 : 1;
      //     const typeValue = activeTab === LearnTypeEnum.LISTEN ? 2 : 1;
      await axiosConfig.post(`${ApiEndpoints.START_PARAGRAPH}`, {
        ...restParams,
      });
      const response = await axiosConfig.post(`${ApiEndpoints.MARK_TRANSACTION_START_LEARN}`, {
        type: typeValue,
      });
      return response.data;
    },
    ...options,
  });
};
export const useEndListenMutation = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        document_id: number;
        course_id: number;
        paragraph_id: number;
        member_id: number;
        member_exercise_token?: string;
        transactionInfo: TransactionLearnInfo;
      }
    >,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: async (payload) => {
      const { transactionInfo, ...restParams } = payload;
      await axiosConfig.post(`${ApiEndpoints.END_PARAGRAPH}`, { ...restParams });
      return await axiosConfig.post(`${ApiEndpoints.MARK_TRANSACTION_END_LEARN}`, {
        id: transactionInfo.id,
      });
    },
    ...options,
  });
};

// const useChangeSentencesPositionMutation = async (sentences: SentencePositionEntity[]) => {
//   try {
//     const result = await apiRequest(ApiEndpoints.SENTENCES_CHANGE_POSITION, {
//       cache: false,
//       method: 'post',
//       data: { sentences },
//     });

//     return result;
//   } catch (error) {
//     console.error('Error changing sentence positions:', error);
//     return { success: false, message: error.message };
//   }
// };
