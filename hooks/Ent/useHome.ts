import { ApiEndpoints } from 'configs';
import { PaginatedResponse, TagDetailEntity } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

interface UseHomeResultHook {
  newList: TagDetailEntity[];
  isLoading: boolean;
  isReachingEnd: boolean;
  setPage: (page: number | ((_size: number) => number)) => Promise<PaginatedResponse[] | undefined>;
  page: number;
}
const useHome = (): UseHomeResultHook => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.NEW_LIST}?includes=total&limit=15&offset=${pageIndex}`;
  };
  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'News',
  });
  return {
    newList: (items as TagDetailEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};
export default useHome;
