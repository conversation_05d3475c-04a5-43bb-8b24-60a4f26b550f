import useSWR from 'swr';

import { ApiEndpoints } from '../../configs';
import { SentenceResponse } from '../../interfaces';

type UseSentenceGroupsDetailProps = {
  group_sentence_id: number;
  paragraph_id: number;
  document_id: number;
  course_id: number;
  type: 'Listening' | 'Reading';
};

export const useSentenceGroupsDetail = ({
  group_sentence_id,
  paragraph_id,
  document_id,
  course_id,
  type = 'Listening',
}: UseSentenceGroupsDetailProps) => {
  const { data: sentenceResponse, isLoading: isLoadingSentences } = useSWR<
    SentenceResponse,
    boolean
  >(
    `${ApiEndpoints.SENTENCE_LIST_V1}?includes=total&paragraph_id=${paragraph_id}&document_id=${document_id}&course_id=${course_id}&group_sentence_id=${group_sentence_id}`,
    {}
  );

  return {
    sentenceResponse,
    isLoading: isLoadingSentences,
  };
};
