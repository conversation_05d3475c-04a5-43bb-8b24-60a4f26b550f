import React, { ReactElement, useMemo, useState } from 'react';

import { useRouter } from 'next/navigation';

import { SAVED_SEARCH_KEYWORD } from 'configs';
import EntRouters from 'configs/EntRouters';
import { getCookie, setCookie } from 'cookies-next';
import { reverse, uniq } from 'lodash';
import useSearchStore from 'store/search';
import { SearchItem, SearchResultItem } from 'types/hooks';
import { useDebounceCallback } from 'usehooks-ts';

const useAutocomplete = (
  searchResults: Array<SearchResultItem>,
  inputSearchRef: ReactElement | null = null
) => {
  const [suggestions, setSuggestions] = useState<SearchItem[]>([]);
  const [activeSuggestion, setActiveSuggestion] = useState(0);
  const { keywordDebounce, setKeyword, setKeywordDebounce, setPageSearch } = useSearchStore();
  const [showSearchPanel, setShowPanel] = useState(false);
  const [searchHistory, setSearchHistory] = useState<Array<string>>([]);
  const router = useRouter();

  const filteredSuggestions = (keyword: string) => {
    return searchResults.filter((itemData) =>
      itemData.title.toUpperCase().startsWith(keyword.toUpperCase())
    );
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>): void => {
    if (event.key === 'ArrowDown' && activeSuggestion < searchResults.length) {
      setActiveSuggestion(activeSuggestion + 1);
    } else if (event.key === 'ArrowUp' && activeSuggestion > 1) {
      setActiveSuggestion(activeSuggestion - 1);
    } else if (event.key === 'Enter') {
      saveToHistory(keywordDebounce);
      setKeywordDebounce('');
      setShowPanel(false);
      _replaceRouter();
      // @ts-ignore
      inputSearchRef?.blur();
    }
  };

  const handleClick = (value: string) => {
    setSuggestions(filteredSuggestions(value));
    setKeyword(value);
    _replaceRouter();
    saveToHistory(value);
    setShowPanel(false);
  };

  const _replaceRouter = () => {
    setPageSearch(1);
    router.push(EntRouters.search);
  };

  const debouncedSearch = useDebounceCallback(setKeywordDebounce, 500);
  const handleChangeKeyword = (searchKey: string) => {
    // const searchKey = event.target.value;
    setKeyword(searchKey);
    if (searchKey.length > 1) {
      debouncedSearch(searchKey);
    }
  };

  const _getListKeyword = () => {
    const savedSearch = getCookie(SAVED_SEARCH_KEYWORD) as string;
    return savedSearch ? JSON.parse(savedSearch).filter((item: string) => item) : [];
  };

  const keywordsHistory = useMemo(() => reverse([..._getListKeyword()]), [searchHistory]);

  const saveToHistory = (keyword: string) => {
    if (!keyword) return;
    const listKeywords = _getListKeyword();
    listKeywords.push(keyword);
    const uniqList = reverse(uniq(reverse([...listKeywords])));
    setSearchHistory(uniqList);
    setCookie(SAVED_SEARCH_KEYWORD, JSON.stringify(uniqList));
  };

  const removeFromHistory = (keyword: string, removeAll = false) => {
    if (removeAll) {
      setSearchHistory([]);
      setCookie(SAVED_SEARCH_KEYWORD, '');
    } else {
      const newLists = _getListKeyword().filter((item) => item !== keyword);
      setSearchHistory(newLists);
      setCookie(SAVED_SEARCH_KEYWORD, JSON.stringify(newLists));
    }
  };

  return {
    keywordsHistory,
    suggestions,
    activeSuggestion,
    showSearchPanel,
    handleKeyDown,
    handleClick,
    handleChangeKeyword,
    removeFromHistory,
    setShowPanel,
  };
};

export default useAutocomplete;
