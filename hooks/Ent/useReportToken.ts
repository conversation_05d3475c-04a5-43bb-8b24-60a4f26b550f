import { useEffect } from 'react';

import { ApiEndpoints } from '@/configs';
import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import useReportStore from '@/store/report';
import { UseReportsProps } from '@/types/hooks';
import { ReportsEntity } from '@/types/model';
import useSWRImmutable from 'swr/immutable';

const useReportToken = (params: UseReportsProps, shouldNotFetch: boolean) => {
  const { setLoading } = useReportStore();
  // @ts-ignore
  const queryParamsToken = new URLSearchParams(params);
  const { data, isLoading } = useSWRImmutable(
    shouldNotFetch ||
      params.object_id === 0 ||
      params.item === '' ||
      !Object.values(ReportTypeEnum).includes(params.item)
      ? null
      : `${ApiEndpoints.REPORT_TOKENS}?${queryParamsToken}`
  );
  useEffect(() => {
    if (data) {
      setLoading(isLoading);
    }
  }, [isLoading]);
  return {
    reportsData: ((data?.data?.report_tokens ?? null) as ReportsEntity[]) || null,
    isLoading,
  };
};
export default useReportToken;
