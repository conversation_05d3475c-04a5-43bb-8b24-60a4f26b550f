import { ApiEndpoints } from 'configs';
import { apiRequest } from 'services';

const useApproveMemberGroups = () => {
  const doApproveMemberGroups = async (id: number, approve_process: number) => {
    return await apiRequest(ApiEndpoints.APPROVE_MEMBER_GROUPS, {
      cache: true,
      method: 'post',
      data: { id: id, approve_process: approve_process },
    });
  };

  return {
    doApproveMemberGroups,
  };
};
export default useApproveMemberGroups;
