import { QUERY_KEY } from '@/constant/query-key';
import axiosConfig from '@/utils/axios.config';
import { useQuery } from '@tanstack/react-query';
import { ApiEndpoints } from 'configs';
import { DocumentEntity } from 'types/model';

// const useDocuments = (params: UseDocumentProps) => {
//   const shouldNotFetch = params.shouldNotFetch || false;
//   const queryParams = useMemo(() => {
//     const paramsCopy = { ...params };
//     delete paramsCopy.shouldNotFetch;
//     // @ts-ignore
//     return new URLSearchParams(paramsCopy).toString();
//   }, [params]);
//   const { data, isLoading } = useSWR(
//     shouldNotFetch ? null : `${ApiEndpoints.DOCUMENT_LIST}?${queryParams}`,
//   );

//   return {
//     documentList: (data?.data?.documents as DocumentEntity[]) || null,
//     isLoading,
//   };
// };

// export default useDocuments;

export const useDocuments = ({ course_id }: { course_id: number }) => {
  return useQuery({
    queryKey: [QUERY_KEY.DOCUMENTS, course_id],
    queryFn: async (): Promise<DocumentEntity[]> => {
      const response = await axiosConfig.get(ApiEndpoints.DOCUMENT_LIST, {
        params: {
          course_id,
        },
      });
      if (response.data.documents.length > 0) {
        return response.data.documents;
      }
      return Promise.resolve([]);
    },
    staleTime: Infinity,
    enabled: !!course_id,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
  });
};
