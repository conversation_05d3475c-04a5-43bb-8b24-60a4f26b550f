import { ConversationTypeEnum } from '@/configs/ConversationEnum';
import { QUERY_KEY } from '@/constant/query-key';
import { DataSentenceGroup } from '@/interfaces/sentence-groups.interface';
import { SentencePositionEntity } from '@/types/hooks';
import axiosConfig from '@/utils/axios.config';
import { UseMutationOptions, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AxiosError, AxiosResponse } from 'axios';

import { ApiEndpoints } from '../../configs';

type UseGalleryProps = {
  paragraph_id: number;
  item: string | undefined;
};

export const useSentenceGroups = ({ paragraph_id, item }: UseGalleryProps) => {
  return useQuery({
    queryKey: [QUERY_KEY.SENTENCE_GROUPS, paragraph_id, item],
    queryFn: async (): Promise<DataSentenceGroup> => {
      const response = await axiosConfig.get(ApiEndpoints.SENTENCE_GROUPS, {
        params: { paragraph_id },
      });
      return response.data;
    },
    enabled: !!paragraph_id && item === ConversationTypeEnum.GALLERY,
    staleTime: Infinity,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchInterval: false,
    refetchIntervalInBackground: false,
  });
};
export const useUpdateSentenceGroupPosition = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        sentenceGroups: SentencePositionEntity[];
      }
    >,
    'mutationFn'
  >
) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (payload: { sentenceGroups: SentencePositionEntity[] }) => {
      return await axiosConfig.post(ApiEndpoints.UPDATE_SENTENCE_GROUP_POSITION, {
        sentence_groups: payload.sentenceGroups,
      });
    },
    ...options,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEY.SENTENCE_GROUPS] });
    },
  });
};

export const useUpdateSentenceGroup = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        file: File | null;
        sentenceGroupId: string;
        content?: string;
        status?: number;
      }
    >,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: async ({ file, sentenceGroupId, content, status }) => {
      const formData = new FormData();
      if (file) {
        formData.append('file', file);
      }
      if (content) {
        formData.append('content', content);
      }
      if (status) {
        formData.append('status', status.toString());
      }
      formData.append('id', sentenceGroupId);
      return await axiosConfig.put(ApiEndpoints.SENTENCE_GROUPS, formData);
    },
    ...options,
  });
};

export const useCreateSentenceGroup = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        course_id: string;
        paragraph_id: string;
        document_id: string;
        file: File;
        content: string;
        pre_sentence_group_id: string;
      }
    >,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: async ({
      course_id,
      paragraph_id,
      document_id,
      file,
      content,
      pre_sentence_group_id,
    }) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('content', content);
      formData.append('course_id', course_id);
      formData.append('paragraph_id', paragraph_id);
      formData.append('document_id', document_id);
      formData.append('pre_sentence_group_id', pre_sentence_group_id);
      return await axiosConfig.post(ApiEndpoints.SENTENCE_GROUPS, formData);
    },
    ...options,
  });
};
