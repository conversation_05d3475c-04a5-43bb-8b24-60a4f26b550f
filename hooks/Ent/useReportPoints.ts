import { useEffect } from 'react';

import { ApiEndpoints } from '@/configs';
import useReportStore from '@/store/report';
import { UseReportsProps } from '@/types/hooks';
import { ReportsEntity } from '@/types/model';
import useSWR from 'swr';

const useReportPoints = (params: UseReportsProps, shouldNotFetch: boolean) => {
  const { setLoading } = useReportStore();

  // @ts-ignore
  const queryParamsPoints = new URLSearchParams(params);
  const { data, isLoading } = useSWR(
    shouldNotFetch || !['class', 'member'].includes(params.item)
      ? null
      : `${ApiEndpoints.REPORT_POINTS}?${queryParamsPoints}`
  );
  useEffect(() => {
    if (data) {
      setLoading(isLoading);
    }
  }, [isLoading, data]);

  return {
    reportPoints: ((data?.data?.report_points ?? null) as ReportsEntity[]) || null,
    isLoading,
  };
};
export default useReportPoints;
