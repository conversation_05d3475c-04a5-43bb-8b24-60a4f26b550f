import { StatusEnum } from '@/configs/StatusEnum';
import { apiRequest } from '@/services';
import { LearnProgramPositionEntity } from '@/types/hooks';
import { DocumentEntity } from '@/types/model';
import { ApiEndpoints } from 'configs';
import useSWR from 'swr';

const useGroupDocument = (groupId: number = 0) => {
  const { data, isLoading } = useSWR(
    groupId !== 0 ? `${ApiEndpoints.SAVE_GROUPS_DOCUMENT}?group_id=${groupId}` : null
  );

  const updateGroupDocument = async (params) => {
    const res = await apiRequest(ApiEndpoints.SAVE_GROUPS_DOCUMENT, {
      method: 'put',
      data: params,
    });
    return res.data;
  };
  const removeGroupDocument = async (params: DocumentEntity) => {
    params.status = StatusEnum.OFF;
    const res = await apiRequest(ApiEndpoints.SAVE_GROUPS_DOCUMENT, {
      method: 'put',
      data: params,
    });
    return res.data;
  };
  const saveGroupDocument = async (params: DocumentEntity[], groupId: number) => {
    const response = params.map(async (param) => {
      const addEntity = {
        document_id: param.id,
        group_id: groupId,
        title: param.title,
      };
      return await apiRequest(ApiEndpoints.SAVE_GROUPS_DOCUMENT, {
        method: 'post',
        data: addEntity,
      });
    });
    return Promise.all(response);
  };
  const doChangeLearnProgramPosition = async (entities: LearnProgramPositionEntity[]) => {
    return await apiRequest(ApiEndpoints.GROUP_PARAGRAPH_CHANGE_POSITION, {
      cache: false,
      method: 'post',
      data: {
        group_documents: [...entities],
      },
    });
  };

  return {
    group_documents: data?.data?.group_documents || [],
    isLoading,
    message: data?.message || '',
    updateGroupDocument,
    removeGroupDocument,
    saveGroupDocument,
    doChangeLearnProgramPosition,
  };
};
export default useGroupDocument;
