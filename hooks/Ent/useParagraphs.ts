import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { UseParagraphProps } from 'types/hooks';
import { PaginatedResponse, ParagraphEntity } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useParagraphs = (params: UseParagraphProps) => {
  if (typeof params.includes === 'undefined' || params.includes === '') {
    params.includes = 'total';
  }
  // @ts-ignore
  const queryParams = new URLSearchParams(params);

  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.PARAGRAPH_LIST}?includes=total&limit=${DEFAULT_RECORD_PER_PAGE}&${queryParams}&offset=${pageIndex}`;
  };

  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'paragraphs',
  });
  return {
    paragraphList: (items as ParagraphEntity[]) || null,
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};
export default useParagraphs;
