import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { HistorySentenceEntity, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useSentences = () => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.HISTORY_SENTENCE}?includes=total&limit=${DEFAULT_RECORD_PER_PAGE}&offset=${pageIndex}`;
  };

  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'sentences',
  });
  return {
    sentenceList: (items as HistorySentenceEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};

export default useSentences;
