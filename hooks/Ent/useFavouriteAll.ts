import { useEffect } from 'react';

import useFavouriteStore from '@/store/favourite';
import { FavouriteEntity } from '@/types/model';
import { getStoredUserId, saveStoredUserId } from '@/utils/storage';
import { ApiEndpoints } from 'configs';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';
import { useSession } from '@/hooks/useSession';

const useFavouriteAll = () => {
  const { data: session } = useSession();
  const userId = session?.member?.id;
  const { favouriteList, setFavourites } = useFavouriteStore();

  const getKey = () => {
    if (!userId) return null; // Chưa đăng nhập thì không gọi API
    if (!Array.isArray(favouriteList) || favouriteList.length === 0) {
      return `${ApiEndpoints.FAVOURITE_LIST}?status=2&userId=${userId}`;
    }
    return null;
  };

  const { items, isLoading, isReachingEnd, setPage, page, mutate } = usePaginatedSWR(getKey, {
    entity: 'favourites',
  });

  useEffect(() => {
    if (!userId) return; // Không làm gì nếu chưa đăng nhập

    const currentStoredId = getStoredUserId();
    if (currentStoredId !== userId.toString()) {
      saveStoredUserId(userId.toString());
      setFavourites([]); // Reset danh sách khi user thay đổi
    }
  }, [userId, setFavourites]);

  useEffect(() => {
    if (
      userId &&
      (!favouriteList || favouriteList.length === 0) &&
      Array.isArray(items) &&
      items.length > 0
    ) {
      setFavourites(items as FavouriteEntity[]);
    }
  }, [userId, favouriteList, items, setFavourites]);

  return {
    favouriteAll: favouriteList,
    isLoading,
    isReachingEnd,
    setPage,
    page,
    mutate,
  };
};

export default useFavouriteAll;
