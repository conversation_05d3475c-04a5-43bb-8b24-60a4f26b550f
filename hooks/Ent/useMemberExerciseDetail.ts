import { useEffect, useState } from 'react';

import { Member } from '@/types/auth';
import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { apiRequest } from 'services';
import useSWR from 'swr';
import { UseMemberExerciseDetailProps } from 'types/hooks';
import { GroupSpeakList, Pagination, ParagraphEntity } from 'types/model';

const useMemberExerciseDetail = (params: UseMemberExerciseDetailProps) => {
  const [exerciseLists, setSpeakList] = useState<Array<GroupSpeakList>>([]);
  const [pagination, setPagination] = useState<Pagination>();

  const buildQueryParams = () => {
    const searchParams = new URLSearchParams();
    searchParams.set('includes', 'total');
    if (params.memberId) searchParams.set('member_id', params.memberId.toString());
    if (params.memberExerciseToken)
      searchParams.set('member_exercise_token', params.memberExerciseToken);
    if (params.keyx) searchParams.set('keyx', params.keyx.toString());
    searchParams.set('offset', params.offset?.toString() || '0');
    searchParams.set('limit', DEFAULT_RECORD_PER_PAGE.toString());

    return searchParams.toString();
  };

  const { data, isLoading } = useSWR(
    params.isFetch ? `${ApiEndpoints.SPEAK_LIST}?${buildQueryParams()}` : null
  );
  const speaks = data?.data?.speaks || [];
  useEffect(() => {
    if (speaks.length) {
      const { total } = data?.data;
      setSpeakList([...exerciseLists, ...speaks]);
      if (total)
        setPagination({
          hasMore: Math.ceil(total / DEFAULT_RECORD_PER_PAGE) > (params.offset || 0),
          total: total,
          page: params.offset || 0,
          last_page: Math.ceil(total / DEFAULT_RECORD_PER_PAGE),
        });
    }
  }, [data, params.offset]);

  const saveSpeak = async (answers) => {
    const response = await apiRequest(ApiEndpoints.UPDATE_TRANSACTION_SPEAK, {
      cache: true,
      method: 'put',
      data: answers,
    });
    return response;
  };

  return {
    exerciseMember: (data?.data?.member as Member) || null,
    exerciseParagraph: (data?.data?.paragraph as ParagraphEntity) || null,
    exerciseLists,
    pagination,
    isLoading,
    saveSpeak,
  };
};

export default useMemberExerciseDetail;
