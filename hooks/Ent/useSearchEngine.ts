import { PaginatedResponse } from '@/types/model';
import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { SearchResultItem, UseSearchEngineProps } from 'types/hooks';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useSearchEngine = (params: UseSearchEngineProps) => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if ((pageIndex > 0 && !previousPageData) || params.title === '') return null;
    return `${ApiEndpoints.SEARCH_ENGINE_PY}?content=${params.title || ''}&filter=${
      params.item || ''
    }&limit=${params.limit || DEFAULT_RECORD_PER_PAGE}&offset=${pageIndex}`;
  };

  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'data',
  });
  return {
    searchResults: items as SearchResultItem[],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};

export default useSearchEngine;
