import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { MemberEntity, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useListMembers = (member_id = 0) => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.LIST_MEMBER}?includes=total&id=${member_id}&limit=${DEFAULT_RECORD_PER_PAGE}&offset=${pageIndex}`;
  };

  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'members',
  });
  return {
    membersList: (items as MemberEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};
export default useListMembers;
