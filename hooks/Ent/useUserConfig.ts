import CategoryEnum from '@/configs/CategoryEnum';
import useCategoryStore from '@/store/category';
import { CategoryEntity } from '@/types/model';
import { ApiEndpoints } from 'configs';
import { isEmpty } from 'lodash';
import useSWR from 'swr';

const useUserConfig = (category_id: number) => {
  const { data } = useSWR(`${ApiEndpoints.GET_CONFIG_MEMBER_CATEGORIES}/${category_id}`);
  return data?.data || null;
};
export const getLocales = (locale = 'en') => {
  const { categories } = useCategoryStore();
  const languages: CategoryEntity[] =
    categories?.filter((item: CategoryEntity) => item.parent_id === CategoryEnum.LANG_LIST) || [];

  if (!isEmpty(languages)) {
    if (locale === 'vi') {
      const lists = languages.map((item) => ({
        id: item.id,
        keyx: item.keyx,
        title: item.title_vi,
        valuex: item.valuex,
      }));
      return lists;
    } else {
      return languages.map((item) => ({
        id: item.id,
        keyx: item.keyx,
        title: item.title,
        valuex: item.valuex,
      }));
    }
  }
};
export const getFontSize = (locale = 'en') => {
  const { categories } = useCategoryStore();
  const fontLists: CategoryEntity[] =
    categories?.filter((item: CategoryEntity) => item.parent_id === CategoryEnum.FONT_SIZE_LIST) ||
    [];

  if (!isEmpty(fontLists)) {
    if (locale === 'vi') {
      const lists = fontLists.map((item) => ({
        id: item.id,
        keyx: item.keyx,
        title: item.title_vi,
        valuex: item.valuex,
      }));
      return lists;
    } else {
      return fontLists.map((item) => ({
        id: item.id,
        keyx: item.keyx,
        title: item.title,
        valuex: item.valuex,
      }));
    }
  }
};
export default useUserConfig;
