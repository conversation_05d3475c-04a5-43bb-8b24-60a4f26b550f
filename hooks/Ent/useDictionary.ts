import { ApiEndpoints } from 'configs';
import useSWR from 'swr';
import { DictionaryEntity, SentenceDetail, SentencePhrase } from 'types/model';

const useDictionary = (
  sentence_id: number = 0,
  position: number = 0,
  exerciseToken: string | null = null
) => {
  //sử dụng để dịch nghĩa trong block "Tra từ"
  const { data: dataPos, isLoading: loadingPos } = useSWR(
    sentence_id > 0 && position >= 0
      ? `${ApiEndpoints.DICTIONARY}?position=${position}&sentence_id=${sentence_id}&member_exercise_token=${exerciseToken}`
      : null
  );
  const { success, data: dictionary } = dataPos || { success: false, data: null };
  let _dictionary: DictionaryEntity | null = null;
  if (success) {
    _dictionary = dictionary;
  }

  //sử dụng lấy thông tin trong block :Cụm từ
  const { data, isLoading } = useSWR(
    sentence_id > 0 ? `${ApiEndpoints.TRANSLATE_SENTENCE}?id=${sentence_id}` : null
  );
  const sentenceDetails: SentenceDetail[] | null = data?.data?.sentence_poses || null;
  const sentencePhrases: SentencePhrase[] | null = data?.data?.phrases || null;
  return {
    sentenceDetails,
    sentencePhrases,
    isLoadingPos: loadingPos && !dataPos,
    isLoadingSentence: isLoading && !data,
    dictionary: _dictionary,
  };
};
export default useDictionary;
