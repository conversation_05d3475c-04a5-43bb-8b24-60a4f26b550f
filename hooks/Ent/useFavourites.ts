import { FavouriteEntity, PaginatedResponse } from '@/types/model';
import { ApiEndpoints } from 'configs';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useFavourites = () => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.FAVOURITE_LIST}?status=2&includes=total&limit=15&offset=${pageIndex}`;
  };
  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'favourites',
    revalidateOnMount: true,
  });

  return {
    favouriteList: (items as FavouriteEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};

export default useFavourites;
