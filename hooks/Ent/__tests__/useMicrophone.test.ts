import { renderHook, act } from '@testing-library/react';
import useMicrophone from '../useMicrophone';

// Mock các dependencies
jest.mock('franc', () => ({
  franc: jest.fn(() => 'en')
}));

jest.mock('utils/common', () => ({
  delay: jest.fn(() => Promise.resolve())
}));

// Mock Web APIs
const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  pause: jest.fn(),
  resume: jest.fn(),
  state: 'inactive',
  ondataavailable: null,
  onstop: null
};

const mockMediaStream = {
  getAudioTracks: jest.fn(() => [
    { enabled: true, stop: jest.fn() }
  ]),
  getTracks: jest.fn(() => [
    { stop: jest.fn() }
  ])
};

const mockAudioContext = {
  createAnalyser: jest.fn(() => ({
    fftSize: 512,
    frequencyBinCount: 256,
    getByteFrequencyData: jest.fn()
  })),
  createMediaStreamSource: jest.fn(() => ({
    connect: jest.fn()
  })),
  decodeAudioData: jest.fn(() => Promise.resolve({
    numberOfChannels: 1,
    length: 1000,
    duration: 1,
    sampleRate: 44100,
    getChannelData: jest.fn(() => new Float32Array(1000))
  })),
  close: jest.fn(() => Promise.resolve()),
  state: 'running'
};

const mockOfflineAudioContext = {
  createBufferSource: jest.fn(() => ({
    buffer: null,
    connect: jest.fn(),
    start: jest.fn()
  })),
  destination: {},
  startRendering: jest.fn(() => Promise.resolve({
    numberOfChannels: 1,
    length: 1000,
    duration: 1,
    sampleRate: 16000,
    getChannelData: jest.fn(() => new Float32Array(1000))
  }))
};

// Setup global mocks
beforeAll(() => {
  global.MediaRecorder = jest.fn(() => mockMediaRecorder) as any;
  global.AudioContext = jest.fn(() => mockAudioContext) as any;
  global.OfflineAudioContext = jest.fn(() => mockOfflineAudioContext) as any;
  
  global.navigator.mediaDevices = {
    getUserMedia: jest.fn(() => Promise.resolve(mockMediaStream))
  } as any;

  global.window.SpeechRecognition = jest.fn(() => ({
    continuous: false,
    lang: '',
    interimResults: false,
    start: jest.fn(),
    stop: jest.fn(),
    onresult: null,
    onerror: null,
    onstart: null,
    onend: null
  }));

  global.Audio = jest.fn(() => ({
    play: jest.fn(() => Promise.resolve()),
    preload: '',
    volume: 1
  })) as any;

  global.URL.createObjectURL = jest.fn(() => 'mock-url');
  global.URL.revokeObjectURL = jest.fn();
});

describe('useMicrophone Hook - Error Handling & Race Conditions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test('should prevent rapid start/stop calls with debounce', async () => {
    const { result } = renderHook(() => useMicrophone());

    // Attempt rapid start calls
    await act(async () => {
      result.current.startMicrophone();
      result.current.startMicrophone(); // Should be ignored
      result.current.startMicrophone(); // Should be ignored
    });

    // Should only call getUserMedia once
    expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledTimes(1);
  });

  test('should handle minimum recording duration', async () => {
    const { result } = renderHook(() => useMicrophone());

    await act(async () => {
      result.current.startMicrophone();
    });

    // Try to stop immediately (should be prevented by minimum duration)
    await act(async () => {
      result.current.stopMicrophone();
    });

    // MediaRecorder.stop should not be called due to minimum duration
    expect(mockMediaRecorder.stop).not.toHaveBeenCalled();
  });

  test('should handle audio decoding errors gracefully', async () => {
    // Mock decodeAudioData to throw error
    mockAudioContext.decodeAudioData.mockRejectedValueOnce(new Error('Unable to decode audio data'));

    const { result } = renderHook(() => useMicrophone());

    await act(async () => {
      result.current.startMicrophone();
    });

    // Simulate recording completion
    const mockBlob = new Blob(['audio data'], { type: 'audio/wav' });
    
    await act(async () => {
      if (mockMediaRecorder.onstop) {
        mockMediaRecorder.onstop();
      }
    });

    // Should not crash and should handle error gracefully
    expect(result.current.audioBlob).toBeDefined();
  });

  test('should validate audio data before processing', async () => {
    const { result } = renderHook(() => useMicrophone());

    await act(async () => {
      result.current.startMicrophone();
    });

    // Simulate empty blob
    const emptyBlob = new Blob([], { type: 'audio/wav' });
    
    await act(async () => {
      if (mockMediaRecorder.onstop) {
        mockMediaRecorder.onstop();
      }
    });

    // Should handle empty blob gracefully
    expect(console.warn).toHaveBeenCalledWith(expect.stringContaining('Invalid audio blob'));
  });

  test('should cleanup resources properly on cancel', async () => {
    const { result } = renderHook(() => useMicrophone());

    await act(async () => {
      result.current.startMicrophone();
    });

    await act(async () => {
      result.current.cancelRecording();
    });

    // Should cleanup all resources
    expect(mockMediaStream.getTracks()[0].stop).toHaveBeenCalled();
    expect(result.current.isStartMicrophone).toBe(false);
    expect(result.current.audioBlob).toBeNull();
  });

  test('should prevent concurrent operations', async () => {
    const { result } = renderHook(() => useMicrophone());

    // Start recording
    await act(async () => {
      result.current.startMicrophone();
    });

    // Try to start again while first is still in progress
    await act(async () => {
      result.current.startMicrophone();
    });

    // Should only initialize once
    expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledTimes(1);
  });
});
