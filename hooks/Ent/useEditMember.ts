import { ApiEndpoints } from 'configs';
import { apiRequest } from 'services';

const useEditMember = () => {
  const doChangeMemberById = async (member) => {
    const response = await apiRequest(ApiEndpoints.CHANGE_PASS_MEMBER, {
      cache: true,
      method: 'post',
      data: member,
    });
    return response;
  };
  const doTransactionRecommend = async () => {
    return await apiRequest(ApiEndpoints.TRANSACTION_RECOMMEND, {
      cache: true,
      method: 'post',
      data: [],
    });
  };
  return {
    doChangeMemberById,
    doTransactionRecommend,
  };
};
export default useEditMember;
