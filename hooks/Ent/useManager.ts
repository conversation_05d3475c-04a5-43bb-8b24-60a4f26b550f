import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { AccountGroupsEntity, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useManagers = (group_id: number | undefined) => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if ((pageIndex > 0 && !previousPageData) || group_id === 0 || group_id === undefined)
      return null;
    return `${ApiEndpoints.MANAGER_LIST}?includes=total&limit=${DEFAULT_RECORD_PER_PAGE}&group_id=${group_id}&offset=${pageIndex}`;
  };
  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'account_groups',
  });
  return {
    managerList: (items as AccountGroupsEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};

export default useManagers;
