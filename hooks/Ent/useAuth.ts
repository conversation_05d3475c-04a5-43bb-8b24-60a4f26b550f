import { useState } from 'react';

import { ApiEndpoints } from 'configs';
import { apiRequest } from 'services';

const useAuth = () => {
  const [errorMessage, setMessage] = useState('');

  const sendOtp = async (phone: string, existDeviceId: string = '') => {
    return await apiRequest(ApiEndpoints.REQUEST_REGISTER, {
      cache: true,
      method: 'post',
      data: {
        phone: phone,
        deviceId: existDeviceId,
      },
    });
  };
  const verifyOtp = async (otp: string, phone: string, existDeviceId: string = '') => {
    return await apiRequest(ApiEndpoints.VERIFY_OTP, {
      cache: false,
      method: 'post',
      data: {
        phone: phone,
        otp: otp,
        deviceId: existDeviceId,
      },
    });
  };
  const verifyOtpMember = async (password: string, token: string) => {
    return await apiRequest(ApiEndpoints.LOGIN_MEMBER, {
      cache: false,
      method: 'post',
      data: {
        token: token,
        password: password,
      },
    });
  };
  const savePassword = async (
    deviceId: string,
    otpAccessToken: string,
    password: string,
    fullname: string
  ) => {
    return await apiRequest(ApiEndpoints.SAVE_PASSWORD, {
      cache: true,
      method: 'post',
      data: {
        token: otpAccessToken,
        deviceId: deviceId,
        fullname: fullname,
        password: password,
      },
    });
  };

  return {
    sendOtp,
    verifyOtp,
    verifyOtpMember,
    savePassword,
    errorMessage,
    setMessage,
  };
};
export default useAuth;
