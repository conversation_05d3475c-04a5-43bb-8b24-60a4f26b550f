import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import useSWR from 'swr';
import { UseTagsProps } from 'types/hooks';
import { PaginatedResponse, TagsEntity } from 'types/model';

const useTags = (params: UseTagsProps) => {
  const getKey = (pageIndex: number = 0, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.TAGS_LIST}/${params.parent_id}?status=2&limit=${DEFAULT_RECORD_PER_PAGE}&includes=total&item${params.item}&offset=${pageIndex}`;
  };
  const { data, isLoading } = useSWR(getKey, { revalidateOnMount: true });
  return {
    tagsDetailList: (data?.data?.['children_tags'] as TagsEntity[]) || [],
    isLoading,
    isReachingEnd: true,
    setPage: (page: number) => page,
    page: 0,
    tags: data?.data || null,
  };
};
export default useTags;
