import { useEffect } from 'react';

import { ApiEndpoints } from '@/configs';
import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import useReportStore from '@/store/report';
import { UseReportsProps } from '@/types/hooks';
import { ReportsEntity } from '@/types/model';
import useSWRImmutable from 'swr/immutable';

const useReportMember = (params: UseReportsProps, shouldNotFetch: boolean) => {
  const { setLoading } = useReportStore();
  // @ts-ignore
  const queryParamsOrganization = new URLSearchParams(params);
  const { data, isLoading } = useSWRImmutable(
    shouldNotFetch || params.item === '' || !Object.values(ReportTypeEnum).includes(params.item)
      ? null
      : `${ApiEndpoints.REPORT_MEMBERS}?${queryParamsOrganization}`
  );
  useEffect(() => {
    if (data) {
      setLoading(isLoading);
    }
  }, [isLoading]);

  return {
    reportsData: ((data?.data?.report_members ?? null) as ReportsEntity[]) || null,
    isLoading,
    totalGroupClass: data?.data?.total_class ?? 0,
  };
};
export default useReportMember;
