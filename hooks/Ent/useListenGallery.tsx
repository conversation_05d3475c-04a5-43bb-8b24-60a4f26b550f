import { useCallback, useEffect, useReducer } from 'react';

import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { StatusEnum } from '@/configs/StatusEnum';
import { SentenceGroup } from '@/interfaces';
import
  {
    IListenGalleryState,
    ListenGalleryActionType,
    listenGalleryReducer,
  } from '@/reducers/listen-gallery.reducers';
import useBalanceStore from '@/store/balance';
import useLearnStore from '@/store/learn';
import useSpeakingStore from '@/store/speaking';
import { Character, ParagraphEntity, SentenceEntity } from '@/types/model';
import { useGlobalAudioPlayer } from 'react-use-audio-player';

import { useSession } from '../useSession';
import
  {
    useBackSentenceMutation,
    useEndListenMutation,
    useSaveCurrentSentenceMutation,
    useStartListenMutation,
  } from './useSentence';

export const useListenGallery = ({
  sortedSentences,
  sortedSentenceGroups,
  listenCurrentId,
  paragraph,
}: {
  sortedSentences: SentenceEntity[];
  sortedSentenceGroups: SentenceGroup[];
  listenCurrentId?: number;
  paragraph?: ParagraphEntity;
}) => {
  const { data: session } = useSession();
  const { exerciseToken, transactionInfo } = useLearnStore();
  const { balanceStatus } = useBalanceStore();
  const { volume, autoReading, setAutoReading, replay } = useSpeakingStore();
  const { load, stop } = useGlobalAudioPlayer();

  const initialState: IListenGalleryState = {
    isStartedLearn: false,
    isFinishLearn: false,
    isEndSentences: true,
    activeCharacter: null,
    activeSentenceGroup: null,
    currentSentences: [],
    nextSentences: [],
    isLastSentence: false,
  };

  const [state, dispatch] = useReducer(listenGalleryReducer, initialState);

  const {
    isStartedLearn,
    isFinishLearn,
    isEndSentences,
    activeCharacter,
    activeSentenceGroup,
    currentSentences,
    nextSentences,
    isLastSentence,
  } = state;

  useEffect(() => {
    if (!sortedSentences?.length) return;

    dispatch({
      type: ListenGalleryActionType.INITIALIZE,
      payload: {
        sentences: sortedSentences,
        sortedSentenceGroups,
        listenCurrentId,
      },
    });
  }, [listenCurrentId, sortedSentences, sortedSentenceGroups]);

  const startListenMutation = useStartListenMutation();
  const endListenMutation = useEndListenMutation();
  const backSentenceMutation = useBackSentenceMutation();

  const handleStartListen = useCallback(() => {
    if (!paragraph || balanceStatus !== StatusEnum.ON) return;
    dispatch({ type: ListenGalleryActionType.SET_STARTED_LEARN, payload: true });
    startListenMutation.mutate({
      document_id: paragraph?.document_id || 0,
      paragraph_id: paragraph?.id || 0,
      course_id: paragraph?.course_id || 0,
      activeTab: LearnTypeEnum.LISTEN,
      member_exercise_token: exerciseToken || '',
    });
  }, [paragraph, balanceStatus, exerciseToken, startListenMutation]);

  const handleFinishConversation = useCallback(() => {
    // If replay is active, go back to the first sentence instead of finishing
    if (replay) {
      // Reset to the first sentence
      if (sortedSentences.length > 0) {
        dispatch({
          type: ListenGalleryActionType.SET_NEXT_SENTENCES,
          payload: [sortedSentences[0]],
        });

        // Set the active sentence group for the first sentence
        const firstSentenceGroup = sortedSentenceGroups.find(
          (group) => group.id === sortedSentences[0].sentence_group_id
        );

        if (firstSentenceGroup) {
          dispatch({
            type: ListenGalleryActionType.SET_ACTIVE_SENTENCE_GROUP,
            payload: firstSentenceGroup,
          });
        }

        // Clear current sentences and reset states
        dispatch({
          type: ListenGalleryActionType.SET_CURRENT_SENTENCES,
          payload: [],
        });

        dispatch({
          type: ListenGalleryActionType.SET_FINISH_LEARN,
          payload: false,
        });

        dispatch({
          type: ListenGalleryActionType.SET_END_SENTENCES,
          payload: true,
        });
      }
      return;
    }

    // Normal finish behavior if replay is not active
    if (transactionInfo) {
      endListenMutation.mutate({
        transactionInfo: transactionInfo,
      });
    }
    dispatch({ type: ListenGalleryActionType.FINISH_CONVERSATION });
  }, [replay, sortedSentences, sortedSentenceGroups, transactionInfo, endListenMutation]);

  const handleRestart = useCallback(() => {
    if (autoReading) return;
    dispatch({
      type: ListenGalleryActionType.LEARN_AGAIN,
      payload: sortedSentences,
    });

    // Need to set the active sentence group after resetting
    if (sortedSentenceGroups.length > 0) {
      dispatch({
        type: ListenGalleryActionType.SET_ACTIVE_SENTENCE_GROUP,
        payload: sortedSentenceGroups[0],
      });
      const timeout = setTimeout(() => {
        backSentenceMutation.mutate({
          activeTab: LearnTypeEnum.LISTEN as LearnTypeEnum,
          sentence_id: sortedSentences[0].id,
          paragraph_id: paragraph?.id || 0,
          member_exercise_token: exerciseToken || '',
        });
      }, 500);
      return () => clearTimeout(timeout);
    }
  }, [
    autoReading,
    sortedSentenceGroups,
    sortedSentences,
    paragraph,
    exerciseToken,
    backSentenceMutation,
  ]);

  const handleBackSentence = () => {
    if (autoReading) return;
    // Check if we're at the first sentence
    if (nextSentences.length > 0) {
      const currentId = nextSentences[0].id;
      const currentIndex = sortedSentences.findIndex((s) => s.id === currentId);

      // If we're at the first sentence, don't allow going back
      if (currentIndex <= 0) {
        return;
      }
    } else if (currentSentences.length > 0) {
      const currentId = currentSentences[0].id;
      const currentIndex = sortedSentences.findIndex((s) => s.id === currentId);

      // If we're at the first sentence, don't allow going back
      if (currentIndex <= 0) {
        return;
      }
    } else {
      // No current or next sentences, we must be at the beginning
      return;
    }

    dispatch({
      type: ListenGalleryActionType.PREV_SENTENCE,
      payload: {
        sortedSentences,
        sortedSentenceGroups,
        onSaveBackSentence: ({ sentence_id }: { sentence_id: number }) => {
          backSentenceMutation.mutate({
            activeTab: LearnTypeEnum.LISTEN as LearnTypeEnum,
            sentence_id,
            paragraph_id: paragraph?.id || 0,
            member_exercise_token: exerciseToken || '',
          });
        },
      },
    });
  };

  const handleNextSentence = () => {
    if (!isEndSentences || balanceStatus !== StatusEnum.ON || isFinishLearn) return;

    // Check if we're in replay mode and at the last sentence
    if (replay && nextSentences.length > 0) {
      const currentSentenceId = nextSentences[0]?.id;
      const currentSentenceIndex = sortedSentences.findIndex((s) => s.id === currentSentenceId);
      const isAtLastSentence = currentSentenceIndex === sortedSentences.length - 1;

      if (isAtLastSentence) {
        // Reset to the first sentence in replay mode
        dispatch({
          type: ListenGalleryActionType.SET_CURRENT_SENTENCES,
          payload: nextSentences,
        });
        dispatch({
          type: ListenGalleryActionType.SET_NEXT_SENTENCES,
          payload: [sortedSentences[0]],
        });

        // Set the active sentence group for the first sentence
        const firstSentenceGroup = sortedSentenceGroups.find(
          (group) => group.id === sortedSentences[0].sentence_group_id
        );
        if (firstSentenceGroup) {
          dispatch({
            type: ListenGalleryActionType.SET_ACTIVE_SENTENCE_GROUP,
            payload: firstSentenceGroup,
          });
        }

        // Play the first sentence
        playAudio([sortedSentences[0]]);
        return;
      }
    }

    dispatch({
      type: ListenGalleryActionType.NEXT_SENTENCE,
      payload: {
        sortedSentences,
        sortedSentenceGroups,
      },
    });
    if (nextSentences.length > 0 || replay) {
      playAudio(nextSentences);
    }
  };

  const handleClickGalleryItem = useCallback(
    (sentenceGroup: SentenceGroup) => {
      if (!sentenceGroup || autoReading) return;

      dispatch({
        type: ListenGalleryActionType.CLICK_GALLERY_ITEM,
        payload: {
          sentenceGroup,
          sortedSentences,
        },
      });
    },
    [autoReading, sortedSentences]
  );

  const saveCurrentSentenceMutation = useSaveCurrentSentenceMutation();

  // Simple playAudio function for manual calls (like from gallery clicks)
  const playAudio = (sentences: SentenceEntity[]) => {
    if (isFinishLearn) return;

    stop();
    dispatch({ type: ListenGalleryActionType.SET_END_SENTENCES, payload: false });

    const listAudio = sentences.map((sentence) => sentence.audios?.[0]?.url);

    const playAudioAtIndex = (index: number) => {
      if (index >= listAudio.length) {
        dispatch({ type: ListenGalleryActionType.SET_END_SENTENCES, payload: true });
        const timeout = setTimeout(() => {
          saveCurrentSentenceMutation.mutate({
            document_id: paragraph?.document_id || 0,
            paragraph_id: paragraph?.id || 0,
            sentence_ids: sentences.map((s) => s.id),
            member_id: session?.user.id,
            member_exercise_token: exerciseToken,
          });
        }, 500);
        return () => clearTimeout(timeout);
      }

      const currentSentenceGroup = sortedSentenceGroups.find(
        (group) => group.id === sentences[index]?.sentence_group_id
      );

      if (currentSentenceGroup) {
        dispatch({
          type: ListenGalleryActionType.SET_ACTIVE_SENTENCE_GROUP,
          payload: currentSentenceGroup,
        });
      }

      const isPlayLastSentence =
        sentences[index]?.id === sortedSentences[sortedSentences.length - 1]?.id;

      if (isPlayLastSentence && !replay) {
        setAutoReading(false);
      }

      if (listAudio[index]) {
        console.log('listAudio[index]', listAudio[index]);
        load(listAudio[index], {
          autoplay: true,
          initialVolume: volume / 100,
          onend: () => {
            dispatch({ type: ListenGalleryActionType.SET_END_SENTENCES, payload: true });
            playAudioAtIndex(index + 1);
          },
        });
      } else {
        // When there's no audio data, we need to simulate the audio ending
        // Set a small timeout to mimic audio playback completion
        const timeout = setTimeout(() => {
          dispatch({ type: ListenGalleryActionType.SET_END_SENTENCES, payload: true });
          playAudioAtIndex(index + 1);
        }, 100); // Small delay to ensure state updates properly

        return () => clearTimeout(timeout);
      }
    };

    playAudioAtIndex(0);
  };

  useEffect(() => {
    if (nextSentences.length === 0 && isStartedLearn && !isFinishLearn && !replay) {
      dispatch({ type: ListenGalleryActionType.SET_LAST_SENTENCE, payload: true });
    } else {
      dispatch({ type: ListenGalleryActionType.SET_LAST_SENTENCE, payload: false });
    }
  }, [nextSentences, isStartedLearn, isFinishLearn, replay]);

  useEffect(() => {
    if (autoReading && isEndSentences && !isLastSentence) {
      handleNextSentence();
    }
  }, [autoReading, isEndSentences, isLastSentence]);

  return {
    // Core states
    isStartedLearn,
    isFinishLearn,
    isEndSentences,
    isLastSentence,

    // Character and sentence group states
    activeCharacter,
    setActiveCharacter: (value: Character | null) =>
      dispatch({ type: ListenGalleryActionType.SET_ACTIVE_CHARACTER, payload: value }),
    activeSentenceGroup,

    // Sentence data
    currentSentences,
    nextSentences,

    // Event handlers
    handleStartListen,
    handleFinishConversation,
    handleNextSentence,
    handleClickGalleryItem,
    handleRestart,
    handleBackSentence,
  };
};
