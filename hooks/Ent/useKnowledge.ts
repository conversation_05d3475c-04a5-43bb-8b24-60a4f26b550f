import { ApiEndpoints } from 'configs';
import useSWR from 'swr';
import { KnowledgeEntity } from 'types/model';

const useKnowledge = () => {
  //sử dụng để dịch ngh<PERSON>a trong block "Tra từ"
  const { data, isLoading } = useSWR(`${ApiEndpoints.KNOWLEDGE_LIST}?status=2`);
  const _knowledge: KnowledgeEntity = data?.data || null;

  const convertData = (data) => {
    const result = {};

    const processItem = (item) => {
      const newItem = { ...item };

      if (item.id in data) {
        newItem.children = data[item.id].map(processItem);
      }

      return newItem;
    };

    for (const key in data) {
      result[key] = data[key].map(processItem);
    }

    return result;
  };

  const newData = convertData(_knowledge);
  return {
    knowledge: newData[0],
    isLoading,
  };
};
export default useKnowledge;
