import { useEffect, useState } from 'react';

import { ApiEndpoints } from 'configs';
import useSWR from 'swr';
import { QuizzAnswers } from 'types/model';

const useOnboardingAnswers = (questionId?: number) => {
  const [quizzAnswers, setQuizzAnswers] = useState<Array<QuizzAnswers>>([]);

  const shouldFetchAnswers = !!questionId;
  const { data, isValidating } = useSWR(
    shouldFetchAnswers ? `${ApiEndpoints.GET_ANSWERS}?status=2&question_id=${questionId}` : null
  );

  useEffect(() => {
    if (data?.data.answers?.length) {
      setQuizzAnswers([...data.data.answers]);
    }
  }, [data?.data.answers?.length, questionId]);

  return {
    quizzAnswers,
    isLoading: isValidating,
  };
};

export default useOnboardingAnswers;
