import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { GroupsEntity, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useGroup = (parent_id: number) => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;

    const offset = parent_id < 0 ? 0 : pageIndex;

    return `${ApiEndpoints.GET_GROUPS}?includes=total&limit=${DEFAULT_RECORD_PER_PAGE}&parent_id=${parent_id}&offset=${offset}`;
  };

  const {
    items,
    isLoading,
    isReachingEnd: originalIsReachingEnd,
    setPage,
    page,
    data,
    mutate,
  } = usePaginatedSWR(getKey, {
    entity: 'groups',
  });

  const isReachingEnd = parent_id < 0 ? true : originalIsReachingEnd;

  return {
    groupsList: (items as GroupsEntity[]) || [],
    groupParent: data?.[0]?.data?.data?.group_parent || null,
    isLoading,
    isReachingEnd,
    setPage,
    page,
    mutate,
  };
};

export default useGroup;
