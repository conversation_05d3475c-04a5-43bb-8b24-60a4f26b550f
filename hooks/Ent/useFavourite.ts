import { useState } from 'react';

import { ApiEndpoints } from 'configs';
import { apiRequest } from 'services';

const useFavourite = () => {
  const [errorMessage, setMessage] = useState('');

  const saveFavourite = async (newStatus: number, item: string, objectId: number) => {
    return await apiRequest(ApiEndpoints.FAVOURITE_LIST, {
      cache: true,
      method: 'post',
      data: {
        item: item,
        object_id: Number(objectId),
        status: newStatus,
      },
    });
  };

  return {
    saveFavourite,
    errorMessage,
    setMessage,
  };
};
export default useFavourite;
