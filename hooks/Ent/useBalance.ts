import { ApiEndpoints, BALANCE_MIN } from '@/configs';
import { StatusEnum } from '@/configs/StatusEnum';
import { QUERY_KEY } from '@/constant/query-key';
import useBalanceStore from '@/store/balance';
import axiosConfig from '@/utils/axios.config';
import { useQuery } from '@tanstack/react-query';
import { useLocale } from 'next-intl';

import { useSession } from '../useSession';

// const useBalance = () => {
//   const locale = useLocale();
//   const { setBalance, setBalanceString, setBalanceStatus } = useBalanceStore();
//   const { data } = useSWR<BalanceResponse>(`${ApiEndpoints.GET_BALANCE}`, {
//     revalidateOnMount: true,
//   });

//   useEffect(() => {
//     if (data?.data?.id) {
//       setBalanceString(data?.data?.quantity?.toLocaleString(locale, { minimumFractionDigits: 0 }));
//       setBalance(data?.data?.quantity ?? 0);
//       setBalanceStatus((data?.data?.quantity ?? 0) < BALANCE_MIN ? 1 : 2);
//     } else {
//       setBalanceString('0');
//     }
//   }, [data]);

//   return data?.data;
// };

// export default useBalance;

export const useBalance = () => {
  const locale = useLocale();
  const { setBalance, setBalanceString, setBalanceStatus } = useBalanceStore();
  const { data: session } = useSession();

  return useQuery({
    queryKey: [QUERY_KEY.BALANCE],
    queryFn: async () => {
      setBalanceString('');
      try {
        const response = await axiosConfig.get(`${ApiEndpoints.GET_BALANCE}`, {
          headers: {
            'x-access-token': session?.accessToken,
          },
        });
        if (response?.data?.id) {
          const quantity = response.data?.quantity ?? 0;
          setBalanceString(
            quantity === 0 ? '0' : quantity.toLocaleString(locale, { minimumFractionDigits: 0 })
          );
          setBalance(quantity);
          setBalanceStatus(quantity < BALANCE_MIN ? StatusEnum.OFF : StatusEnum.ON);
        } else {
          setBalance(0);
          setBalanceString('0');
          setBalanceStatus(StatusEnum.OFF);
        }
        return response.data;
      } catch (e) {
        setBalance(0);
        setBalanceString('0');
        setBalanceStatus(StatusEnum.OFF);
        throw e;
      }
    },
    staleTime: Infinity,
    retry: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchIntervalInBackground: false,
    refetchOnReconnect: true,
    enabled: !!session?.accessToken,
  });
};
