# Debug Guide cho useMicrophone Hook

## 🔍 Vấn đề hiện tại
User báo cáo: "<PERSON>ần đầu tiên ghi âm, stop không có tác dụng, ph<PERSON>i start lại lần 2"

## 🎯 Root Cause Found!
**Vấn đề chính**: `isCancelRef.current = true` được set trong `cancelRecording()` khi component mount, khiến lần đầu tiên recording bị coi như cancelled.

## 🛠️ C<PERSON>c cải tiến đã implement

### 1. **Early State Setting**
```typescript
// Set recording state early to prevent timing issues
setIsRecording(true);
console.log('🎤 Recording state set to true early');
```

### 2. **Enhanced Stop Logic**
```typescript
// Check if not currently recording - prioritize MediaRecorder state over React state
const isMediaRecorderActive = mediaRecorder.current && 
  (mediaRecorder.current.state === 'recording' || mediaRecorder.current.state === 'paused');

console.log('🛑 Stop check - React state:', state.isStartMicrophone, 'MediaRecorder state:', mediaRecorder.current?.state);

if (!state.isStartMicrophone && !isMediaRecorderActive) {
  console.warn('Not currently recording, ignoring stop request');
  return;
}

// Allow stop if either React state says recording OR MediaRecorder is active
if (!state.isStartMicrophone && isMediaRecorderActive) {
  console.log('🔄 React state not updated yet, but MediaRecorder is active - allowing stop');
}
```

### 3. **Comprehensive Logging**
Đã thêm detailed logging để debug:
- 🎤 Start process tracking
- 🛑 Stop process tracking  
- State transitions
- MediaRecorder state changes

## 🧪 Cách test và debug

### Bước 1: Mở Browser Console
1. Mở Developer Tools (F12)
2. Chuyển sang tab Console
3. Clear console để dễ theo dõi

### Bước 2: Test Flow
1. **Click Start Recording**
   - Quan sát logs: `🎤 Recording state set to true early`
   - Kiểm tra: `🎤 MediaRecorder started, state: recording`
   - Xác nhận: `✅ Start process completed - isStartingRef reset to false`

2. **Click Stop Recording ngay sau đó**
   - Quan sát logs: `🛑 Stop microphone requested...`
   - Kiểm tra states: `reactState`, `mediaRecorderState`, `isStartingRef`, `isStoppingRef`
   - Xem có bị block không: `🛑 Stop action blocked by debounce/validation`

### Bước 3: Phân tích logs

#### ✅ **Expected Success Flow (After Fix):**
```
🚀 useMicrophone hook initialized
✅ Initial state reset completed
🎤 Cancel flag reset to false
🎤 Recording state set to true early
🎤 MediaRecorder started, state: recording
✅ Start process completed - isStartingRef reset to false
🛑 Stop microphone requested...
🛑 Current states: {reactState: true, mediaRecorderState: "recording", isStartingRef: false, isStoppingRef: false}
🛑 Starting stop process...
🛑 Speech recognition stopped
🛑 Stopping MediaRecorder, current state: recording
🛑 Recording state set to false
🛑 Microphone released
✅ Stop process completed successfully
🎵 MediaRecorder.onstop triggered, isCancelRef: false
✅ Recording completed normally, processing audio...
```

#### ❌ **Problem Scenarios:**

**Scenario 1: React State Not Updated**
```
🛑 Stop check - React state: false MediaRecorder state: recording
🔄 React state not updated yet, but MediaRecorder is active - allowing stop
```
→ **Should still work** vì logic đã được update để allow stop

**Scenario 2: Debounce Block**
```
🛑 Stop action blocked by debounce/validation
```
→ **Expected behavior** nếu click quá nhanh (< 300ms)

**Scenario 3: First-time Cancel Issue (FIXED)**
```
🎵 MediaRecorder.onstop triggered, isCancelRef: true
❌ Recording was cancelled, skipping audio processing
```
→ **FIXED**: `isCancelRef.current` now properly reset to `false` on initialization and start

## 🔧 Troubleshooting

### Nếu vẫn không work:

1. **Check Timing Issue:**
   - Có thể cần tăng delay trong `canPerformStopAction`
   - Hoặc giảm `DEBOUNCE_DELAY` từ 300ms xuống 100ms

2. **Check MediaRecorder State:**
   - Verify `mediaRecorder.current.state` có đúng là "recording" không
   - Có thể cần thêm delay sau `mediaRecorder.start()`

3. **Check React State Update:**
   - Verify `state.isStartMicrophone` có được update không
   - Có thể cần force re-render

## 🎯 Next Steps

Nếu vẫn có vấn đề, hãy:
1. Share console logs từ test flow
2. Specify exact timing (bao lâu sau start thì click stop)
3. Check browser compatibility (Chrome vs Safari vs Firefox)

## 📝 Test Commands

```javascript
// Test trong browser console
const hook = useMicrophone();

// Test start
hook.startMicrophone();

// Wait 1 second then test stop
setTimeout(() => {
  hook.stopMicrophone();
}, 1000);
```
