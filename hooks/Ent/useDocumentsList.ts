import { ApiEndpoints } from 'configs';
import { UseDocumentProps } from 'types/hooks';
import { DocumentEntity, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useDocumentsList = (params: UseDocumentProps) => {
  // @ts-ignore
  const queryParams = new URLSearchParams(params);
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if ((pageIndex && !previousPageData) || params.shouldNotFetch) return null;
    delete queryParams['shouldNotFetch'];
    return `${ApiEndpoints.DOCUMENT_LIST}?includes=total&${queryParams.toString()}&offset=${pageIndex}`;
  };

  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'documents',
  });
  return {
    documentList: items as DocumentEntity[],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};
export default useDocumentsList;
