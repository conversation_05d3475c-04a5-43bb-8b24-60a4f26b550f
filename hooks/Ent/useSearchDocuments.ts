import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import useSWR from 'swr';

const useSearchDocuments = (keyword = '') => {
  // @ts-ignore
  const { data, isLoading } = useSWR(
    keyword === '' || keyword.length < 3
      ? null
      : `${ApiEndpoints.DOCUMENT_LIST}?title=${keyword}&limit=${DEFAULT_RECORD_PER_PAGE}&offset=0`,
    { keepPreviousData: true }
  );
  return {
    documentsList: data?.data?.documents,
    isLoading,
  };
};
export default useSearchDocuments;
