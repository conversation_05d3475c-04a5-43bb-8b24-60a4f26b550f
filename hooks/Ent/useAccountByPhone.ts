import { ApiEndpoints } from 'configs';
import useSWR from 'swr';
import { AccountEntity } from 'types/model';

const useAccountByPhone = (phone: string) => {
  const { data, isLoading } = useSWR(
    !phone ? null : `${ApiEndpoints.ACCOUNT_BY_PHONE}?phone=${phone}`
  );
  return {
    isLoading,
    account: (data?.data?.accounts?.[0] as AccountEntity) || null,
  };
};
export default useAccountByPhone;
