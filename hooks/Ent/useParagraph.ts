import { QUERY_KEY } from '@/constant/query-key';
import { ParagraphEntity } from '@/types/model';
import axiosConfig from '@/utils/axios.config';
import { useQuery } from '@tanstack/react-query';
import { ApiEndpoints } from 'configs';

// const useParagraph = (keyx: string = '', shouldNotFetch = false) => {
//   // Track render count
//   const renderCount = useRef(0);
//   const dataProcessed = useRef(false);

//   // Memoize the key to prevent unnecessary renders
//   const swrKey = useMemo(() => {
//     return shouldNotFetch
//       ? null
//       : `${ApiEndpoints.PARAGRAPH_DETAIL}/${keyx}?token=${useLearnStore.getState().exerciseToken}`;
//   }, [keyx, shouldNotFetch]);

//   renderCount.current += 1;
//   // console.log(
//   //   `[useParagraph] Render #${renderCount.current} - key: ${keyx}, shouldNotFetch: ${shouldNotFetch}`,
//   // );
//   const { data, mutate: swrMutate, isLoading } = useSWR(swrKey);
//   useEffect(() => {
//     // console.log(`[useParagraph] Data changed:`, data?.data?.id);

//     if (data?.data && !dataProcessed.current) {
//       // console.log(`[useParagraph] Updating store with paragraph ID:`, data.data.id);
//       const store = useLearnStore.getState();
//       store.setActiveParagraph(data.data);
//       dataProcessed.current = true;
//     }
//   }, [data]);

//   // Memoize the mutate function to avoid recreating it on each render
//   const mutate = useCallback(async () => {
//     // console.log(`[useParagraph] Mutate called for key:`, keyx);
//     dataProcessed.current = false;
//     return swrMutate();
//   }, [keyx, swrMutate]);

//   // Log at the end of render
//   // console.log(`[useParagraph] Render #${renderCount.current} completed - isLoading: ${isLoading}`);

//   // Memoize the return value to prevent creating new objects on each render
//   return {
//     paragraph: (data?.data as ParagraphEntity) || null,
//     mutate,
//     isLoading,
//   };
// };

// export default useParagraph;

export const useParagraph = ({ keyx }: { keyx: string }) => {
  return useQuery({
    queryKey: [QUERY_KEY.PARAGRAPH, keyx],
    queryFn: async (): Promise<ParagraphEntity> => {
      const response = await axiosConfig.get(`${ApiEndpoints.PARAGRAPH_DETAIL}/${keyx}`);
      return response.data;
    },
    staleTime: Infinity,
    enabled: !!keyx,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
  });
};
