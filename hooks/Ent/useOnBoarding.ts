import { useEffect, useState } from 'react';

import { ApiEndpoints } from 'configs';
import { apiRequest } from 'services';
import useSWR from 'swr';
import { UseOnboardingProps } from 'types/hooks';
import { QuizzQuestion } from 'types/model';

const useOnBoarding = (params: UseOnboardingProps) => {
  const [quizzSaveAnswers, setQuizzSaveAnswers] = useState([]);
  const [isValidatingGetSaveAnswer, setIsValidatingGetSaveAnswer] = useState<boolean>(false);

  // Fetch danh sách câu hỏi
  const { data: dataQues, isValidating: isValidatingQ } = useSWR(
    `${ApiEndpoints.GET_QUESTION}?status=2&quiz_id=1`
  );

  const quizzQuestions: QuizzQuestion[] = dataQues?.data.questions || [];

  useEffect(() => {
    if (!params.quest_id) return; // Nếu không có quest_id, không gọi API

    const fetchData = async () => {
      try {
        setIsValidatingGetSaveAnswer(true);
        const dataSaveAnswer = await apiRequest(
          `${ApiEndpoints.GET_SAVE_ANSWERS}?status=2&question_id=${params.quest_id}`
        );
        setQuizzSaveAnswers(dataSaveAnswer?.data.answers || []);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsValidatingGetSaveAnswer(false);
      }
    };

    fetchData();
  }, [params.quest_id]);

  const saveAnswers = async (answers) => {
    return await apiRequest(ApiEndpoints.SAVE_ANSWERS, {
      cache: true,
      method: 'post',
      data: answers,
    });
  };

  const cancelAnswers = async (answers) => {
    return await apiRequest(ApiEndpoints.CANCEL_ANSWER, {
      cache: true,
      method: 'post',
      data: answers,
    });
  };

  return {
    saveAnswers,
    quizzQuestions,
    isLoadingQuestion: isValidatingQ,
    cancelAnswers,
    quizzSaveAnswers,
    isValidatingGetSaveAnswer,
  };
};

export default useOnBoarding;
