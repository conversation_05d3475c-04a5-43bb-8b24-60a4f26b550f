import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { PaginatedResponse, TransactionMemberEntity } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useTransactionAccount = () => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.TRANSACTION_ACCOUNT_TOKEN}?includes=total&limit=${DEFAULT_RECORD_PER_PAGE}&offset=${pageIndex}`;
  };
  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'member_token_transactions',
  });
  return {
    memberList: (items as TransactionMemberEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};

export default useTransactionAccount;
