import { ApiEndpoints } from 'configs';
import { UseParagraphProps } from 'types/hooks';
import { PaginatedResponse, ParagraphEntity } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useParagraphConversations = (params: UseParagraphProps) => {
  if (typeof params.includes === 'undefined' || params.includes === '') {
    params.includes = 'total';
  }
  const { ...filteredParams } = params;
  // @ts-ignore
  const queryParams = new URLSearchParams(filteredParams);
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.LIBRARY_ENGINE_LIST_PARAGRAPH}?includes=total&${queryParams}&offset=${pageIndex}`;
  };
  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'paragraphs',
  });
  return {
    paragraphList: (items as ParagraphEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};
export default useParagraphConversations;
