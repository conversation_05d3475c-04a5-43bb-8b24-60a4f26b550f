import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import useSWRImmutable from 'swr/immutable';
import { SearchResultItem, UseSearchProps } from 'types/hooks';

const useSearch = (params: UseSearchProps) => {
  const { data, isLoading } = useSWRImmutable(
    params.shouldNotFetch
      ? null
      : `${ApiEndpoints.SEARCH_ENGINE}?title=${params.title || ''}&limit=${
          params.limit || DEFAULT_RECORD_PER_PAGE
        }&offset=${params?.offset || 0}`,
    { keepPreviousData: true }
  );

  const searchResults: SearchResultItem[] = data?.data?.search_engines || [];

  return {
    isLoading,
    searchResults,
  };
};
export default useSearch;
