import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { HistoryWordEntity, PaginatedResponse } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useTransactionWord = () => {
  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.HISTORY_WORD}?includes=total&limit=${DEFAULT_RECORD_PER_PAGE}&offset=${pageIndex}`;
  };
  const { items, isLoading, isReachingEnd, setPage, page } = usePaginatedSWR(getKey, {
    entity: 'lookups',
    revalidateOnMount: true,
  });
  return {
    wordList: (items as HistoryWordEntity[]) || [],
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};

export default useTransactionWord;
