import { ApiEndpoints } from '@/configs';
import { QUERY_KEY } from '@/constant/query-key';
import { apiRequest } from '@/services';
import { SentencePositionEntity } from '@/types/hooks';
import axiosConfig from '@/utils/axios.config';
import { UseMutationOptions, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError, AxiosResponse } from 'axios';

export const useUpdateSentenceGroup = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        file: File | null;
        sentenceGroupId: string;
        content?: string;
        status?: number;
      }
    >,
    'mutationFn'
  >
) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ file, sentenceGroupId, content, status }) => {
      const formData = new FormData();
      if (file) {
        formData.append('file', file);
      }
      if (content) {
        formData.append('content', content);
      }
      if (status) {
        formData.append('status', status.toString());
      }
      formData.append('id', sentenceGroupId);
      return await axiosConfig.put(ApiEndpoints.SENTENCE_GROUPS, formData);
    },
    ...options,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEY.SENTENCE_GROUPS] });
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEY.CONVERSATIONS] });
    },
  });
};

export const useCreateSentenceGroupMutation = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        course_id: string;
        paragraph_id: string;
        document_id: string;
        file: File;
        content: string;
        pre_sentence_group_id: string;
      }
    >,
    'mutationFn'
  >
) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      course_id,
      paragraph_id,
      document_id,
      file,
      content,
      pre_sentence_group_id,
    }) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('content', content);
      formData.append('course_id', course_id);
      formData.append('paragraph_id', paragraph_id);
      formData.append('document_id', document_id);
      formData.append('pre_sentence_group_id', pre_sentence_group_id);
      return await axiosConfig.post(ApiEndpoints.SENTENCE_GROUPS, formData);
    },
    ...options,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEY.SENTENCE_GROUPS] });
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEY.CONVERSATIONS] });
    },
  });
};

export const useUpdateSentenceGroupPosition = async ({
  sentenceGroups,
}: {
  sentenceGroups: SentencePositionEntity[];
}) => {
  return await apiRequest(ApiEndpoints.UPDATE_SENTENCE_GROUP_POSITION, {
    cache: false,
    method: 'post',
    data: {
      sentence_groups: sentenceGroups,
    },
  });
};
