import { ApiEndpoints, DEFAULT_RECORD_PER_PAGE } from 'configs';
import { UseTagDetailsProps } from 'types/hooks';
import { PaginatedResponse, TagDetailEntity } from 'types/model';

import { usePaginatedSWR } from '@/hooks/common/usePaginatedSWR';

const useTagDetails = (params: UseTagDetailsProps) => {
  // @ts-ignore
  const queryParams = new URLSearchParams(params);

  const getKey = (pageIndex: number, previousPageData: PaginatedResponse | null) => {
    if (pageIndex > 0 && !previousPageData) return null;
    return `${ApiEndpoints.TAG_DETAILS_LIST}?limit=${DEFAULT_RECORD_PER_PAGE}&includes=total&${queryParams.toString()}&offset=${pageIndex}`;
  };
  const { items, isLoading, isReachingEnd, setPage, page, data } = usePaginatedSWR(getKey, {
    entity: 'tag_details',
  });
  return {
    tagDetailList: (items as TagDetailEntity[]) || [],
    tag: data?.[0]?.data?.data?.tag || null,
    isLoading,
    isReachingEnd,
    setPage,
    page,
  };
};
export default useTagDetails;
