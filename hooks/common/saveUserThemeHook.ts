import { CategoryEntity } from '@/types/model';
import { ApiEndpoints } from 'configs';
import CategoryEnum from 'configs/CategoryEnum';
import { apiRequest } from 'services';

const saveUserThemeHook = async (selectedTheme: CategoryEntity, accessToken: string = '') => {
  try {
    await apiRequest(ApiEndpoints.SAVE_CONFIG_MEMBER_CATEGORIES, {
      cache: true,
      method: 'post',
      data: {
        category_id: CategoryEnum.THEME_LIST,
        valuex: selectedTheme.valuex,
      },
      headers: {
        'x-access-token': accessToken,
      },
    });
  } catch (error) {
    console.error('Lỗi khi cập nhật theme:', error);
  }
};

export default saveUserThemeHook;
