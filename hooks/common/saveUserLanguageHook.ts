import { startTransition } from 'react';

import { Locale } from '@/configs/config.i18n';
import { setUserLocale } from '@/services/locale';
import { ApiEndpoints } from 'configs';
import CategoryEnum from 'configs/CategoryEnum';
import { apiRequest } from 'services';

const saveUserLanguageHook = async (selectedLang: Locale, accessToken: string = '') => {
  try {
    await apiRequest(ApiEndpoints.SAVE_CONFIG_MEMBER_CATEGORIES, {
      cache: true,
      method: 'post',
      data: {
        category_id: CategoryEnum.LANG_LIST,
        valuex: selectedLang,
      },
      headers: {
        'x-access-token': accessToken,
      },
    });
    startTransition(() => {
      setUserLocale(selectedLang);
    });
  } catch (error) {
    console.error('Lỗi khi cập nhật Ngôn ngữ:', error);
    // Xử lý lỗi nếu cần thiết
  }
};
export default saveUserLanguageHook;
