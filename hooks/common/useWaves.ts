import { useEffect, useState } from 'react';

import WaveSurfer from 'wavesurfer.js';

const useWaves = (containerRef, options) => {
  const [wavesurfer, setWavesurfer] = useState<WaveSurfer | null>(null);

  useEffect(() => {
    if (!containerRef.current || !options.url) return;
    const wsOption = { ...options };
    delete wsOption.url;

    try {
      const ws = WaveSurfer.create({
        ...wsOption,
        container: containerRef.current!,
      });
      ws.load('/api/ent/fetch-audio?url=' + options.url);
      setWavesurfer(ws);
    } catch (e) {
      console.error('Error loading audio:', e);
    }
  }, [options]);

  return wavesurfer;
};
export default useWaves;
