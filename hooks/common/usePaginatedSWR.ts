import { useMemo } from 'react';

import useSWRInfinite from 'swr/infinite';

interface UsePaginatedSWRConfig {
  revalidateOnMount?: boolean;
  initialSize?: number;
  entity: string;
}

export function usePaginatedSWR<T>(
  getKey: (pageIndex: number, previousPageData: T | null) => string | null,
  config: UsePaginatedSWRConfig
) {
  const { entity, initialSize = 1, revalidateOnMount = true } = config;

  const { data, size, setSize, isValidating, isLoading, mutate } = useSWRInfinite<T>(getKey, {
    initialSize,
    revalidateOnMount,
    revalidateFirstPage: false,
    revalidateIfStale: true,
  });

  const items = useMemo(() => {
    return data ? data.flatMap((page) => (page as any)['data'][entity] || []) : [];
  }, [data, entity]);

  const total = data ? data[data.length - 1]?.['data']?.['total'] || null : 0;
  const isLoadingMore =
    total === null
      ? false
      : isValidating &&
        size > 0 &&
        ((data && typeof data[size - 1] === 'undefined') || (items && items.length < total));

  const isReachingEnd =
    total === null
      ? true
      : !!(
          (data && (data[data.length - 1] as any)?.['data']?.[entity]?.length === 0) ||
          (items && items.length >= total)
        );

  return {
    items,
    isLoading: isLoadingMore || isLoading,
    isReachingEnd,
    setPage: setSize,
    page: size,
    data,
    mutate,
  };
}
