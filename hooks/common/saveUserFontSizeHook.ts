import { ApiEndpoints } from 'configs';
import CategoryEnum from 'configs/CategoryEnum';
import { apiRequest } from 'services';

const saveUserFontSizeHook = async (fontSize: string) => {
  try {
    await apiRequest(ApiEndpoints.SAVE_CONFIG_MEMBER_CATEGORIES, {
      cache: true,
      method: 'post',
      data: {
        category_id: CategoryEnum.FONT_SIZE_LIST,
        valuex: fontSize,
      },
    });
  } catch (error) {
    console.error('Lỗi khi cập nhật Fontsize:', error);
    // Xử lý lỗi nếu cần thiết
  }
};
export default saveUserFontSizeHook;
