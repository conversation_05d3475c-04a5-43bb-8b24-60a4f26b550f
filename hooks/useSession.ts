'use client';

import { useMemo } from 'react';

import { Session } from 'next-auth';
import { useSession as authSession } from 'next-auth/react';

// import useSWR from 'swr';

// const fetcher = (url: string) => fetch(url).then((res) => res.json());
export function useSession() {
  const { data: session, update, status } = authSession();

  // Memoize session data to prevent unnecessary rerenders
  const memoizedSession = useMemo(() => {
    if (!session) return null;
    return session;
  }, [
    session?.user?.id,
    session?.member?.id,
    session?.accessToken,
    session?.account_token,
    session?.member_token,
    // Only include essential fields that components actually depend on
  ]);

  // const { data } = useSWR('/api/auth/session', fetcher, {
  //   dedupingInterval: 1000 * 60 * 1, // Cache dữ liệu trong 5 phút
  //   refreshInterval: 0, // Không tự động fetch lại
  //   revalidateOnFocus: false, // Không fetch lại khi tab được focus
  //   revalidateOnReconnect: false, // Không fetch lại khi mạng reconnect
  // });

  return {
    data: memoizedSession as Session,
    update,
    status,
  };
}
