name: Release

on:
  release:
    types: [published]

env:
  DOCKER_REGISTRY: 'ghcr.io'
  ORG: 'fruitozi'
  DOCKER_USERNAME: 'baoozi'
  DOCKER_TOKEN: '****************************************'
  SERVICE_NAME: 'longan'
  ARGOCD_SERVER: argocd-prod.ozidigital.com
  ARGOCD_USERNAME: admin
  ARGOCD_PASSWORD: 0Gl9xlss6VwdSClh
  ARGOCD_APP_PRODUCTION: longan
  DOCKERFILE_PATH: 'Dockerfile'
  SERVICE_MODULE: '.'

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Make envfile Develop
        uses: SpicyPizza/create-envfile@v2.0
        with:
          envkey_NEXT_PUBLIC_NODE_ENV: 'production'
          envkey_NEXT_PUBLIC_BASE_API: 'https://api.langenter.com'
          envkey_BASE_API: 'https://api.langenter.com'
          envkey_NEXTAUTH_URL: 'https://app.langenter.com'
          envkey_NEXTAUTH_SECRET: '62167df7cb9bf1e392c3a2ea0190c665715254ff752d24a72ccc399762e17e57'
          envkey_IMAGE_DOMAIN_LOCALHOST: 'http://localhost:3000'
          envkey_IMAGE_DOMAIN_CDN: 'http://localhost:3000'
          envkey_LOGGER_LEVEL: 'info'
          envkey_FACEBOOK_ID: '1'
          envkey_FACEBOOK_SECRET: '3'
          envkey_GOOGLE_ID: '4'
          envkey_GOOGLE_SECRET: '5'
          envkey_TWITTER_ID: '6'
          envkey_TWITTER_SECRET: '8'
          envkey_NEW_RELIC_NAME_OF_FEATURE_FLAG_ENABLED: 'true'
          envkey_NEW_RELIC_LOG: 'stdout'
          envkey_NEW_RELIC_APP_NAME: 'EntLearningFE'
          envkey_NEW_RELIC_LICENSE_KEY: 'b4d47d48f59697bbd8d0d3f6e76557d9FFFFNRAL'
          envkey_NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: 'true'
          directory: .
          file_name: .env.production
          fail_on_empty: false

      - name: Prepare GITHUB_ENV
        run: |
          echo "COMPILED_AT=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_ENV
          echo "GIT_TAG=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV
          echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-8`" >> $GITHUB_ENV

      - name: Login to GitHub Container Registry
        run: echo "$DOCKER_TOKEN" | docker login $DOCKER_REGISTRY -u $DOCKER_USERNAME --password-stdin

      - name: Build and push
        id: build-image
        uses: docker/build-push-action@v6
        env:
          DEV_ECR_REGISTRY: ${{ env.DOCKER_REGISTRY }}/${{ env.ORG }}
        with:
          context: ${{ env.SERVICE_MODULE }}
          platforms: linux/amd64
          push: true
          provenance: false
          tags: |
            ${{ env.DOCKER_REGISTRY }}/${{ env.ORG }}/${{ env.SERVICE_NAME }}:${{ env.GIT_TAG }}
            ${{ env.DOCKER_REGISTRY }}/${{ env.ORG }}/${{ env.SERVICE_NAME }}:${{ env.GIT_TAG }}-${{ env.SHORT_SHA }}
          build-args: |
            GIT_COMMIT=${{ github.sha }}
            GIT_TAG=${{ env.GIT_TAG }}
            COMPILED_AT=${{ env.COMPILED_AT }}

  update-image:
    name: update-image
    needs: build
    runs-on: ubuntu-latest
    environment: production

    steps:
      - uses: actions/checkout@v4
        name: Check out code repo K8S Deployment Manifest
        with:
          repository: 'fruitozi/ent-gitops'
          token: *********************************************************************************************

      - name: Setup Kustomize
        uses: imranismail/setup-kustomize@v1
        with:
          kustomize-version: '3.8.8'

      - name: Update Kubernetes resources
        env:
          GITHUB_COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "DevOps"
          git checkout master

          echo "Update newimage with kustomize"
          SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-8`

          cd kustomize/$SERVICE_NAME/production
          kustomize edit set image $DOCKER_REGISTRY/$ORG/$SERVICE_NAME:${GITHUB_REF#refs/*/}-$SHORT_SHA
          cat kustomization.yaml
          git commit -am "[${{ github.repository }}] $GITHUB_ACTOR - $GITHUB_COMMIT_MESSAGE" || true
          git push -u origin master

  deploy:
    name: deploy
    needs: update-image
    runs-on: ubuntu-latest
    environment: production

    steps:
      - uses: clowdhaus/argo-cd-action@v1.12.1
        name: Install ArgoCD CLI

      - name: Sync ArgoCD App
        run: |
          argocd login --insecure $ARGOCD_SERVER --username $ARGOCD_USERNAME --password $ARGOCD_PASSWORD
          argocd --server $ARGOCD_SERVER --insecure app sync $ARGOCD_APP_PROD
