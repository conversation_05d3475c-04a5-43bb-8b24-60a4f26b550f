// require('newrelic');
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'dev';
const app = next({ dev, hostname: 'localhost', port: 3000 });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer((req, res) => {
    // Be sure to pass `true` as the second argument to `url.parse`.
    // This tells it to parse the query portion of the URL.
    const parsedUrl = parse(req.url, true);
    const { pathname } = parsedUrl;

    if (pathname === '/health') {
      res.end(JSON.stringify({ status: 'ok' }));
    } else {
      const userAgent = req.headers['user-agent'];
      const isIos = userAgent.includes('iPhone OS');
      const versionText = userAgent.split(isIos ? 'iPhone OS ' : 'Android ')?.[1];
      const minVer = isIos ? 13 : 7;
      if (versionText) {
        let ver = versionText.split(isIos ? '_' : ';')?.[0];
        if (!isIos && ver.includes('.')) {
          ver = ver.split('.')?.[0];
        }
        if (Number(ver) < minVer) {
          res.write(
            `<html lang='en'>
              <head>
                  <meta charset='UTF-8'>
                  <meta http-equiv='X-UA-Compatible' content='IE=edge'>
                  <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                  <title>Warning</title>
                  <link rel='preconnect' href='https://fonts.googleapis.com'></link>
                  <link
                    rel='preconnect'
                    href='https://fonts.gstatic.com'
                    crossOrigin='anonymous'
                  ></link>
                  <link
                    href='https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap'
                    rel='stylesheet'
                  ></link>
              </head>
              <body>
                <div style="font-family: 'Roboto', sans-serif;max-width: 768px;height:896px; margin-left:auto; margin-right:auto; position: relative;">
                  <main style='position: relative; z-index: 10; width: 100% height: 100%;'>
                    <div style='position:absolute; top: 100px; transform: translateX(-50%); left: 50%; width: 100%;'>
                      <div style='position: relative; width: 100%; margin-left: auto; margin-right; auto; margin-top: -28px;'>
                        <div style='width:100%; position:absolute; top: 0; bottom: 0; margin-top: auto; margin-bottom: auto; margin-left: 0; margin-right: 0;z-index: 1; text-align:center;'>
                          <div style='padding-bottom: 24px;'>
                           
                            <div style='margin-top: 20px; max-width:240px; margin-left: auto;margin-right: auto;'>
                              <p
                                style='color: white; font-size: 14px; line-height: 18px; font-weight: 400; margin-top:22px;'
                              >
                                Thiết bị Quý Khách đang sử dụng hiện không được hỗ trợ để sử dụng dịch vụ của chúng tôi. 
                                <br/>
                                <br/>
                                Vui lòng truy cập trên một thiết bị khác với hệ điều hành phiên bản IOS 13 hoặc Android 7 trở lên.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </main>
                  <div
                    style='background-color: rgba(0, 0, 0, 0.6);width: 100%; height:100%; position:absolute; top:0; left:0; z-index: 2;'
                  />
                </div>
              </body>
              </html>`
          );
          res.end();
        }
      }
    }
    handle(req, res, parsedUrl);
  }).listen(3000, (err) => {
    if (err) throw err;
    console.log('> Ready on http://localhost:3000');
  });
});
